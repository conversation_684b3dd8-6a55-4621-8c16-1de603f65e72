<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeActivityLogColumnsToUuid extends Migration
{
    public function up()
    {
        Schema::table('activity_log', function (Blueprint $table) {
            // Ubah causer_id dan subject_id menjadi uuid atau string
            $table->uuid('causer_id')->nullable()->change();
            $table->uuid('subject_id')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('activity_log', function (Blueprint $table) {
            // Kembalikan ke tipe asli jika rollback
            $table->unsignedBigInteger('causer_id')->nullable()->change();
            $table->unsignedBigInteger('subject_id')->nullable()->change();
        });
    }
}
