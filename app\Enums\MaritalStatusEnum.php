<?php

namespace App\Enums;

enum MaritalStatusEnum: string
{
    case BELUM_MENIKAH = 'belum_menikah';
    case MENIKAH = 'menikah';
    case CERAI_HIDUP = 'cerai_hidup';
    case CERAI_MATI = 'cerai_mati';
    case DUDA = 'duda';
    case JANDA = 'janda';
    case HIDUP_BERPISAH = 'hidup_berpisah';

    public function label(): string
    {
        return match ($this) {
            self::BELUM_MENIKAH => 'Belum Menikah',
            self::MENIKAH => 'Menikah',
            self::CERAI_HIDUP => 'Cerai Hidup',
            self::CERAI_MATI => 'Cerai Mati',
            self::DUDA => 'Duda',
            self::JANDA => 'Janda',
            self::HIDUP_BERPISAH => 'Hidup Berpisah',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::BELUM_MENIKAH => 'Status untuk orang yang belum pernah menikah',
            self::MENIKAH => 'Status untuk pasangan yang terikat perkawinan sah',
            self::CERAI_HIDUP => 'Status perceraian melalui putusan pengadilan',
            self::CERAI_MATI => 'Status karena pasangan meninggal dunia',
            self::DUDA => 'Status untuk pria yang istrinya telah meninggal',
            self::JANDA => 'Status untuk wanita yang suaminya telah meninggal',
            self::HIDUP_BERPISAH => 'Status pasangan yang hidup terpisah tanpa perceraian resmi',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::BELUM_MENIKAH => 'heroicon-o-user',
            self::MENIKAH => 'heroicon-o-heart',
            self::CERAI_HIDUP => 'heroicon-o-scissors',
            self::CERAI_MATI => 'heroicon-o-x-mark',
            self::DUDA => 'heroicon-o-user-minus',
            self::JANDA => 'heroicon-o-user-minus',
            self::HIDUP_BERPISAH => 'heroicon-o-arrows-right-left',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::BELUM_MENIKAH => 'gray',
            self::MENIKAH => 'green',
            self::CERAI_HIDUP => 'red',
            self::CERAI_MATI => 'purple',
            self::DUDA => 'blue',
            self::JANDA => 'pink',
            self::HIDUP_BERPISAH => 'orange',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_reduce(self::cases(), function ($options, $case) {
            $options[$case->value] = $case->label();
            return $options;
        }, []);
    }

    public static function selectOptions(): array
    {
        return [
            self::BELUM_MENIKAH->value => self::BELUM_MENIKAH->label(),
            self::MENIKAH->value => self::MENIKAH->label(),
            self::CERAI_HIDUP->value => self::CERAI_HIDUP->label(),
            self::CERAI_MATI->value => self::CERAI_MATI->label(),
            self::DUDA->value => self::DUDA->label(),
            self::JANDA->value => self::JANDA->label(),
            self::HIDUP_BERPISAH->value => self::HIDUP_BERPISAH->label(),
        ];
    }

    public static function kemenagOptions(): array
    {
        return [
            self::BELUM_MENIKAH->value => 'Belum Kawin',
            self::MENIKAH->value => 'Kawin',
            self::CERAI_HIDUP->value => 'Cerai Hidup',
            self::CERAI_MATI->value => 'Cerai Mati',
        ];
    }
}
