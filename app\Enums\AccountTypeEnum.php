<?php

namespace App\Enums;

enum AccountTypeEnum: string
{
    case CASH = 'cash';
    case BANK = 'bank';
    case SAVINGS = 'savings';
    case CREDIT_CARD = 'credit_card';
    case INVESTMENT = 'investment';
    case OTHER = 'other';

    public function label(): string
    {
        return match ($this) {
            self::CASH => 'Kas Tunai',
            self::BANK => 'Rekening Bank',
            self::SAVINGS => 'Tabungan',
            self::CREDIT_CARD => 'Kartu Kredit',
            self::INVESTMENT => 'Investasi',
            self::OTHER => 'Lainnya',
        };
    }

    public function defaultCurrency(): string
    {
        return match ($this) {
            self::CASH, self::BANK, self::SAVINGS => 'IDR',
            self::CREDIT_CARD => 'USD',
            default => 'IDR',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::CASH => 'heroicon-o-banknotes',
            self::BANK => 'heroicon-o-building-library',
            self::SAVINGS => 'heroicon-o-piggy-bank',
            self::CREDIT_CARD => 'heroicon-o-credit-card',
            self::INVESTMENT => 'heroicon-o-chart-bar',
            self::OTHER => 'heroicon-o-archive-box',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::CASH => 'primary',
            self::BANK => 'success',
            self::SAVINGS => 'info',
            self::CREDIT_CARD => 'purple',
            self::INVESTMENT => 'yellow',
            self::OTHER => 'gray',
        };
    }

    public function isLiquid(): bool
    {
        return match ($this) {
            self::CASH, self::BANK, self::SAVINGS => true,
            default => false,
        };
    }

    public static function options(): array
    {
        return [
            self::CASH->value => self::CASH->label(),
            self::BANK->value => self::BANK->label(),
            self::SAVINGS->value => self::SAVINGS->label(),
//            self::CREDIT_CARD->value => self::CREDIT_CARD->label(),
//            self::INVESTMENT->value => self::INVESTMENT->label(),
//            self::OTHER->value => self::OTHER->label(),
        ];
    }

    public static function liquidOptions(): array
    {
        return array_filter(self::options(), fn($type) => self::from($type)->isLiquid());
    }
}
