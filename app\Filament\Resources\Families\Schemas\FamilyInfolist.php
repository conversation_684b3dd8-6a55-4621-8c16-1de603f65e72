<?php

namespace App\Filament\Resources\Families\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class FamilyInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('subscriber.name'),
                TextEntry::make('village.name')
                    ->numeric(),
                TextEntry::make('rt'),
                TextEntry::make('rw'),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                TextEntry::make('deleted_at')
                    ->dateTime(),
            ]);
    }
}
