<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SubscriberUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        DB::table('subscriber_users')->insert([
            'subscriber_id' => 'c3e82d1a-66c3-43a9-9bc0-e0b9e1559ef6',
            'user_id' => 'd06d2fea-65be-4450-8c1b-5d65088d4fab',
            'role' => 'warga',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
