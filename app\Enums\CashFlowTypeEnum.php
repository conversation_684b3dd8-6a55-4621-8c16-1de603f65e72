<?php

namespace App\Enums;

enum CashFlowTypeEnum: string
{
    case OPERATIONAL = 'operational';
    case INVESTMENT = 'investment';
    case FINANCING = 'financing';
    case OTHER = 'other';

    public function label(): string
    {
        return match($this) {
            self::OPERATIONAL => 'Operasional',
            self::INVESTMENT => 'Investasi',
            self::FINANCING => 'Pendanaan',
            self::OTHER => 'Lainnya',
        };
    }

    public function icon(): string
    {
        return match($this) {
            self::OPERATIONAL => 'currency-dollar',
            self::INVESTMENT => 'building-library',
            self::FINANCING => 'arrows-right-left',
            self::OTHER => 'ellipsis-horizontal-circle',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::OPERATIONAL => 'text-sky-500',
            self::INVESTMENT => 'text-emerald-500',
            self::FINANCING => 'text-violet-500',
            self::OTHER => 'text-gray-500',
        };
    }

    public static function options(): array
    {
        return [
            self::OPERATIONAL->value => self::OPERATIONAL->label(),
            self::INVESTMENT->value => self::INVESTMENT->label(),
            self::FINANCING->value => self::FINANCING->label(),
            self::OTHER->value => self::OTHER->label(),
        ];
    }

    public static function selectOptions(): array
    {
        return [
            self::OPERATIONAL->value => [
                'value' => self::OPERATIONAL->value,
                'label' => self::OPERATIONAL->label(),
                'icon' => self::OPERATIONAL->icon(),
                'color' => self::OPERATIONAL->color(),
            ],
            // ... (lakukan hal yang sama untuk case lainnya)
        ];
    }
}
