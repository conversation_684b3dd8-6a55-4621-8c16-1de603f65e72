<?php

namespace App\Enums;

enum BloodTypeEnum: string
{
    case A = 'a';
    case B = 'b';
    case AB = 'ab';
    case O = 'o';
    case UNKNOWN = 'unknown';

    public function label(): string
    {
        return match ($this) {
            self::A => 'Golongan Darah A',
            self::B => 'Golongan Darah B',
            self::AB => 'Golongan Darah AB',
            self::O => '<PERSON>longan Darah O',
            self::UNKNOWN => 'Tidak Diketahui',
        };
    }

    public function rhesusOptions(): array
    {
        return ['+', '-', 'unknown'];
    }

    public function color(): string
    {
        return match ($this) {
            self::A => 'red',
            self::B => 'blue',
            self::AB => 'purple',
            self::O => 'orange',
            self::UNKNOWN => 'gray',
        };
    }

    public function icon(): string
    {
        return 'heroicon-o-heart';
    }

    public static function options(): array
    {
        return [
            self::A->value => self::A->label(),
            self::B->value => self::B->label(),
            self::AB->value => self::AB->label(),
            self::O->value => self::O->label(),
            self::UNKNOWN->value => self::UNKNOWN->label(),
        ];
    }

    public static function selectOptions(): array
    {
        return self::options();
    }
}
