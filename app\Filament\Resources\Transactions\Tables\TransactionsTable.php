<?php

namespace App\Filament\Resources\Transactions\Tables;

use App\Enums\TransactionTypeEnum;
use Carbon\Carbon;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Actions\ViewAction;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class TransactionsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('subscriber.name')
                    ->label('Pelanggan')
                    ->limit(25)
                    ->searchable()
                    ->visible(fn() => auth()->user()->isSuperAdmin()),
                TextColumn::make('account.name')
                    ->label('Akun')
                    ->searchable(),
                TextColumn::make('category.name')
                    ->label('Kategori')
                    ->searchable(),
                TextColumn::make('type')
                    ->label('Tipe')
                    ->formatStateUsing(fn (?TransactionTypeEnum $state): string => $state?->label() ?? '-')
                    ->icon(fn (?TransactionTypeEnum $state): string => $state?->icon() ?? '-')
                    ->iconColor(fn (?TransactionTypeEnum $state): string => $state?->color() ?? '-')
                    ->color(fn (?TransactionTypeEnum $state): string => $state?->color() ?? '-')
                    ->searchable(),
                TextColumn::make('transaction_date')
                    ->label('Tanggal')
                    ->formatStateUsing(fn($state) => Carbon::parse($state)->locale(app()->getLocale())->format('d M y'))
                    ->sortable(),
                TextColumn::make('amount')
                    ->label('Jumlah')
                    ->money('IDR')
                    ->sortable(),
                TextColumn::make('destinationAccount.name')
                    ->label('Akun Tujuan')
                    ->formatStateUsing(fn ($state) => $state ?: '-')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('reference_number')
                    ->label('Nomor Referensi')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->defaultSort('created_at', 'desc')
            ->recordActions([
                EditAction::make()
                    ->label(false)
                    ->tooltip('Edit data')
                    ->icon(Heroicon::PencilSquare),
                RestoreAction::make()
                    ->label(false)
                    ->tooltip('Pulihkan data')
                    ->icon(Heroicon::ArrowPath),
                DeleteAction::make()
                    ->label(false)
                    ->tooltip('Hapus data')
                    ->icon(Heroicon::Trash),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus semua'),
                    ForceDeleteBulkAction::make()
                        ->label('Hapus permanen'),
                    RestoreBulkAction::make()
                        ->label('Pulihkan semua'),
                ]),
            ]);
    }
}
