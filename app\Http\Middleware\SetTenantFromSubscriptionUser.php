<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetTenantFromSubscriptionUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        if (auth()->check() && !session()->has('tenant_id')) {
            $tenant = auth()->user()->subscribers()->first();
            session(['tenant_id' => $tenant?->id]);
        }

        return $next($request);

    }
}
