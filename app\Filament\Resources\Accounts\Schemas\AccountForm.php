<?php

namespace App\Filament\Resources\Accounts\Schemas;

use App\Enums\AccountTypeEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class AccountForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('subscriber_id')
                    ->label('Pelanggan')
                    ->relationship('subscriber', 'name')
                    ->required()
                    ->native(false)
                    ->visible(fn () => auth()->user()->isSuperAdmin())
                    ->default(auth()->user()->activeSubscription()->id ?? null),
                TextInput::make('name')
                    ->label('Nama')
                    ->placeholder('Kas Lingkungan')
                    ->required()
                    ->maxLength(255),
                TextInput::make('account_number')
                    ->label('Nomor Akun')
                    ->placeholder('1000')
                    ->unique()
                    ->maxLength(255),
                Select::make('type')
                    ->label('Tipe')
                    ->options(AccountTypeEnum::options())
                    ->native(false)
                    ->required(),
                TextInput::make('initial_balance')
                    ->label('Saldo Awal')
                    ->numeric()
                    ->default(0)
                    ->prefix('Rp'),
            ]);
    }
}
