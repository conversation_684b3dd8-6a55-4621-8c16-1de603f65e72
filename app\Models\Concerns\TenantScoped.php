<?php

namespace App\Models\Concerns;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

trait TenantScoped
{
    public static function bootTenantScoped(): void
    {
        // <PERSON><PERSON> proses CLI (seeder, migrate, queue worker, dst.)
        if (app()->runningInConsole()) {
            return;
        }

        /** @var \App\Models\User|null $user */
        $user = Auth::user();

        // Super‑admin & guest tetap bebas — atur via policy masing‑masing
        if (!$user || $user->isSuperAdmin()) {
            return;
        }

        // Selalu pasang global‑scope, bahkan jika subscription‑nya null
        static::addGlobalScope('tenant', function (Builder $builder) use ($user): void {

            logger()->debug('Applying tenant scope', [
                'model' => get_class($builder->getModel()),
                'user_id' => $user->id,
                'subscription_id' => $user->activeSubscription()?->id
            ]);

            $subscription = $user->activeSubscription();

            if ($subscription) {
                // Tampilkan hanya data tenant sendiri
                $builder->where($builder->getModel()->getTable() . '.subscriber_id', $subscription->id);
            } else {
                // User tanpa subscription => tak boleh melihat apa‑pun
                $builder->whereRaw('0 = 1');   // set hasil query menjadi kosong
            }
        });

        // Otomatis isi subscriber_id saat CREATE
        static::creating(function (Model $model) use ($user): void {
            // Jika model memang punya kolom subscriber_id
            if (
                $model->isFillable('subscriber_id') ||
                array_key_exists('subscriber_id', $model->getAttributes())
            ) {
                if (is_null($model->subscriber_id)) {
                    $subscription = $user->activeSubscription();

                    if (!$subscription) {
                        throw new AuthorizationException(
                            'Anda belum terdaftar di tenant mana pun.'
                        );
                    }

                    $model->subscriber_id = $subscription->id;
                }
            }
        });
    }

    /**
     * Scope manual bila Anda butuh panggilan eksplisit:
     *    Account::query()->forCurrentTenant()->get();
     */
    public function scopeForCurrentTenant(Builder $query): Builder
    {
        $user = Auth::user();

        if (!$user || $user->isSuperAdmin()) {
            return $query;                           // bebas
        }

        $subscription = $user->activeSubscription();

        return $subscription
            ? $query->where($this->getTable() . '.subscriber_id', $subscription->id)
            : $query->whereRaw('0 = 1');             // kosongkan hasil
    }
}
