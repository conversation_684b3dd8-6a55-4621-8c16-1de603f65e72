<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SubscriberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('subscribers')->insert([
            'id' => 'c3e82d1a-66c3-43a9-9bc0-e0b9e1559ef6',
            'type' => 'rt',
            'name' => 'RT 25 Rajeg Hill Residence',
            'desc' => 'RT 25 Rajeg Hill Residence',
            'village_id' => 3603112009,
            'address' => 'RT 25 Rajeg Hill Residence',
            'data' => null,
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
        ]);
    }
}
