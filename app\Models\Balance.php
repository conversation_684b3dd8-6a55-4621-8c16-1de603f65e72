<?php

namespace App\Models;

use App\Enums\TransactionTypeEnum;
use App\Models\Concerns\TenantScoped;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Balance extends Model
{

    use TenantScoped, HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $guarded = [];

    protected $casts = [
        'balance_date' => 'datetime',
        'balance' => 'decimal:2',
        'type' => TransactionTypeEnum::class,
    ];

    public function subscriber(): BelongsTo
    {
        return $this->belongsTo(Subscriber::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    public function balanceDetails(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(BalanceDetail::class, 'account_id', 'account_id');
    }
}
