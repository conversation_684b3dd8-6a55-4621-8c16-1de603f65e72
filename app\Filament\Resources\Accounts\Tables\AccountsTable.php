<?php

namespace App\Filament\Resources\Accounts\Tables;

use App\Enums\AccountTypeEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\RestoreAction;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class AccountsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('subscriber.name')
                    ->label('Pelanggan')
                    ->searchable()
                    ->visible(fn() => auth()->user()->isSuperAdmin()),
                TextColumn::make('name')
                    ->label('Nama')
                    ->searchable(),
                BadgeColumn::make('account_number')
                    ->label('Nomor Akun')
                    ->searchable(),
                TextColumn::make('type')
                    ->label('Tipe')
                    ->formatStateUsing(fn (?AccountTypeEnum $state): string => $state?->label() ?? '-')
                    ->icon(fn (?AccountTypeEnum $state): string => $state?->icon() ?? '-')
                    ->color(fn (?AccountTypeEnum $state): string => $state?->color() ?? '-')
                    ->iconColor(fn (?AccountTypeEnum $state): string => $state?->color() ?? '-')
                    ->searchable(),
                TextColumn::make('initial_balance')
                    ->label('Saldo Awal')
                    ->money('IDR'),
                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Diubah')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label('Tipe')
                    ->native(false)
                    ->options(AccountTypeEnum::options()),
            ])
            ->recordActions([
                EditAction::make()
                    ->label(false)
                    ->tooltip('Edit data')
                    ->icon(Heroicon::PencilSquare),
                RestoreAction::make()
                    ->label(false)
                    ->tooltip('Pulihkan data')
                    ->icon(Heroicon::ArrowPath),
                DeleteAction::make()
                    ->label(false)
                    ->tooltip('Hapus data')
                    ->icon(Heroicon::Trash),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
