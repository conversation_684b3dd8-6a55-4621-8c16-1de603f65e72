<?php

namespace App\Enums;

enum GenderEnum: string
{
    case L = 'l';
    case P = 'p';

    public function label(): string
    {
        return match ($this) {
            self::L => 'Laki-laki',
            self::P => 'Perempuan',
        };
    }

//    public function icon(): string
//    {
//        return match ($this) {
//            self::L => 'heroicon-o-user',       // Ikon untuk laki-laki
//            self::P => 'heroicon-o-user',       // Ikon untuk perempuan
//        };
//    }

    // Alternatif jika ingin ikon yang lebih spesifik

    public function icon(): string
    {
        return match ($this) {
            self::L => 'heroicon-m-face-smile',  // Alternatif ikon laki-laki
            self::P => 'heroicon-m-sparkles',    // Alternatif ikon perempuan
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::L => 'blue',      // Warna biru untuk laki-laki
            self::P => 'pink',      // Warna pink untuk perempuan
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }
        return $options;
    }

    public static function select(): array
    {
        return [
            self::L->value => self::L->label(),
            self::P->value => self::P->label(),
        ];
    }
}
