<?php

namespace App\Filament\Resources\Accounts\Pages;

use App\Filament\Resources\Accounts\AccountResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditAccount extends EditRecord
{
    protected static string $resource = AccountResource::class;


    protected static ?string $title = 'Ubah Data';

    protected function getHeaderActions(): array
    {
        return [
//            ViewAction::make(),
            DeleteAction::make(),
        ];
    }
}
