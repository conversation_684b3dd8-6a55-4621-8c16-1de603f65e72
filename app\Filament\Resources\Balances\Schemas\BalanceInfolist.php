<?php

namespace App\Filament\Resources\Balances\Schemas;

use Carbon\Carbon;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class BalanceInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('subscriber.name')
                    ->label('Pelanggan')
                    ->visible(fn() => auth()->user()->isSuperAdmin()),
                TextEntry::make('account.name')
                    ->label('Akun'),
                TextEntry::make('balance')
                    ->label('Saldo')
                    ->money('IDR'),
                TextEntry::make('balance_date')
                    ->label('Tanggal')
                    ->formatStateUsing(function ($value) {
                        return Carbon::parse($value)->locale('id')->translatedFormat('D, d M Y');
                    }),
                TextEntry::make('created_at')
                    ->formatStateUsing(function ($value) {
                        return Carbon::parse($value)->locale('id')->translatedFormat('D, d M Y H:i:s');
                    }),
                TextEntry::make('updated_at')
                    ->formatStateUsing(function ($value) {
                        return Carbon::parse($value)->locale('id')->translatedFormat('D, d M Y H:i:s');
                    }),
            ]);
    }
}
