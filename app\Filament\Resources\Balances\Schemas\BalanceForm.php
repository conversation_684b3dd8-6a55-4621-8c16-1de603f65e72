<?php

namespace App\Filament\Resources\Balances\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class BalanceForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('subscriber_id')
                    ->relationship('subscriber', 'name'),
                Select::make('account_id')
                    ->relationship('account', 'name'),
                TextInput::make('balance')
                    ->required()
                    ->numeric(),
                DatePicker::make('balance_date')
                    ->required(),
                Select::make('transaction_id')
                    ->relationship('transaction', 'id'),
            ]);
    }
}
