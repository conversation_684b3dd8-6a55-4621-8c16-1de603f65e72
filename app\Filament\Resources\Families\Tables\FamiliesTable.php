<?php

namespace App\Filament\Resources\Families\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class FamiliesTable
{
    public static function formatFamilyId($number): string
    {
        $str = (string)$number;
        $formatted = '';
        for ($i = 0; $i < strlen($str); $i += 4) {
            if ($i > 0) {
                $formatted .= '-';
            }
            $formatted .= substr($str, $i, 4);
        }
        return $formatted;
    }

    public static function configure(Table $table): Table
    {
        return $table
            ->columns([

                TextColumn::make('family_identifier')
                    ->label('No KK')
                    ->formatStateUsing(function ($state) {
                        return !empty($state) ? static::formatFamilyId($state) : '-';
                    })
                    ->searchable(),

                TextColumn::make('headFamily.fullname')
                    ->label('Kepala Keluarga')
                    ->formatStateUsing(fn($state) => $state ?: '-'),

                TextColumn::make('headFamily.resident_status')
                    ->label('Status Hunian')
                    ->formatStateUsing(fn($state) => $state ?: '-'),

                TextColumn::make('address')
                    ->label('Alamat')
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->searchable(),
                TextColumn::make('rt_rw')
                    ->label('RT/RW')
                    ->getStateUsing(function ($record) {
                        return $record->rt . '/' . $record->rw;
                    })
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->searchable(),
                TextColumn::make('village.name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->label('Desa/Kelurahan'),

                TextColumn::make('village.district.name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->label('Kecamatan'),

                TextColumn::make('village.district.regency.name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->label('Kabupaten/Kota'),

                TextColumn::make('village.district.regency.province.name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->label('Provinsi'),

                TextColumn::make('residents_count')
                    ->label('Anggota Keluarga')
                    ->getStateUsing(function ($record) {
                        return $record->residents()->count() . ' Orang';
                    })
                    ->toggleable(isToggledHiddenByDefault: false),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make()
                    ->native(false),

            ])
            ->recordActions([
                EditAction::make()
                    ->label(false)
                    ->tooltip('Edit data')
                    ->icon(Heroicon::PencilSquare),
                RestoreAction::make()
                    ->label(false)
                    ->tooltip('Pulihkan data')
                    ->icon(Heroicon::ArrowPath),
                DeleteAction::make()
                    ->label(false)
                    ->tooltip('Hapus data')
                    ->icon(Heroicon::Trash),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus semua'),
                    ForceDeleteBulkAction::make()
                        ->label('Hapus permanent'),
                    RestoreBulkAction::make()
                        ->label('Pulihkan semua'),
                ]),
            ]);
    }
}
