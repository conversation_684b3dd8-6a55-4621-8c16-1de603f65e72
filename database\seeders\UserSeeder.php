<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'id' => 'd06d2fea-65be-4450-8c1b-5d65088d4fab',
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('superadmin'),
                'global_role' => 'superadmin',
                'remember_token' => Str::random(10),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => Str::uuid(),
                'name' => 'Test',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('test'),
                'global_role' => 'user',
                'remember_token' => Str::random(10),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => Str::uuid(),
                'name' => 'Manager User',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'global_role' => 'manager',
                'remember_token' => Str::random(10),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('users')->insert($users);

        // If you want to create additional random users
        \App\Models\User::factory(10)->create();
    }
}
