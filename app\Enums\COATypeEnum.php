<?php

namespace App\Enums;

enum COATypeEnum: string
{
    case ASSET = 'asset';
    case LIABILITY = 'liability';
    case EQUITY = 'equity';
    case REVENUE = 'revenue';
    case EXPENSE = 'expense';

    // Label untuk tampilan
    public function label(): string
    {
        return match($this) {
            self::ASSET => 'Aset',
            self::LIABILITY => 'Kewajiban',
            self::EQUITY => 'Ekuitas',
            self::REVENUE => 'Pendapatan',
            self::EXPENSE => 'Beban',
        };
    }

    // Nama Heroicon (https://heroicons.com)
    public function icon(): string
    {
        return match($this) {
            self::ASSET => 'heroicon-o-banknotes',
            self::LIABILITY => 'heroicon-o-scale',
            self::EQUITY => 'heroicon-o-shield-check',
            self::REVENUE => 'heroicon-o-arrow-up-circle',
            self::EXPENSE => 'heroicon-o-arrow-down-circle',
        };
    }

    // <PERSON>na ikon (opsional)
    public function color(): string
    {
        return match($this) {
            self::ASSET => 'primary',
            self::LIABILITY => 'danger',
            self::EQUITY => 'success',
            self::REVENUE => 'info',
            self::EXPENSE => 'warning',
        };
    }

    // Format untuk dropdown HTML/Blade
    public static function options(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_map(fn($case) => $case->label(), self::cases())
        );
    }

    // Format untuk JavaScript/API
    public static function selectOptions(): array
    {
        return array_map(
            fn($case) => [
                'value' => $case->value,
                'label' => $case->label(),
                'icon' => $case->icon(),
                'color' => $case->color(),
            ],
            self::cases()
        );
    }
}
