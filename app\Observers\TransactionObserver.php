<?php

namespace App\Observers;

use App\Enums\TransactionTypeEnum;
use App\Models\Transaction;
use App\Models\Balance;
use App\Models\BalanceDetail;
use App\Models\Account;
use Illuminate\Support\Str;

class TransactionObserver
{
    public function created(Transaction $transaction)
    {
        $this->recalculateBalances($transaction->account_id, $transaction->subscriber_id, $transaction->transaction_date);
        if ($transaction->type === TransactionTypeEnum::TRANSFER && $transaction->destination_account_id) {
            $this->recalculateBalances($transaction->destination_account_id, $transaction->subscriber_id, $transaction->transaction_date);
        }
    }

    public function updated(Transaction $transaction)
    {
        $original = $transaction->getOriginal();
        $startDate = min($original['transaction_date'], $transaction->transaction_date);

        // Delete subsequent balance details for affected accounts
        $this->deleteSubsequentBalanceDetails($transaction->account_id, $startDate, $transaction->id);
        if ($transaction->type === TransactionTypeEnum::TRANSFER && $transaction->destination_account_id) {
            $this->deleteSubsequentBalanceDetails($transaction->destination_account_id, $startDate, $transaction->id);
        }

        // Recalculate balances for affected accounts
        $this->recalculateBalances($transaction->account_id, $transaction->subscriber_id, $startDate);
        if ($transaction->type === TransactionTypeEnum::TRANSFER && $transaction->destination_account_id) {
            $this->recalculateBalances($transaction->destination_account_id, $transaction->subscriber_id, $startDate);
        }
    }

    public function deleted(Transaction $transaction)
    {
        // Delete balance details for this transaction and subsequent ones
        $this->deleteSubsequentBalanceDetails($transaction->account_id, $transaction->transaction_date, $transaction->id);
        if ($transaction->type === TransactionTypeEnum::TRANSFER && $transaction->destination_account_id) {
            $this->deleteSubsequentBalanceDetails($transaction->destination_account_id, $transaction->transaction_date, $transaction->id);
        }

        // Recalculate balances for affected accounts
        $this->recalculateBalances($transaction->account_id, $transaction->subscriber_id, $transaction->transaction_date);
        if ($transaction->type === TransactionTypeEnum::TRANSFER && $transaction->destination_account_id) {
            $this->recalculateBalances($transaction->destination_account_id, $transaction->subscriber_id, $transaction->transaction_date);
        }
    }

    protected function deleteSubsequentBalanceDetails($accountId, $startDate, $transactionId)
    {
        BalanceDetail::where('account_id', $accountId)
            ->where(function ($query) use ($startDate, $transactionId) {
                $query->where('balance_date', '>', $startDate)
                    ->orWhere(function ($query) use ($startDate, $transactionId) {
                        $query->where('balance_date', '=', $startDate)
                            ->where('transaction_id', '>=', $transactionId);
                    });
            })
            ->delete();
    }

    protected function recalculateBalances($accountId, $subscriberId, $startDate = null)
    {
        $account = Account::findOrFail($accountId);
        // If startDate is null, process all transactions to initialize balances
        $query = Transaction::where('subscriber_id', $subscriberId)
            ->where(function ($query) use ($accountId) {
                $query->where('account_id', $accountId)
                    ->orWhere('destination_account_id', $accountId);
            })
            ->orderBy('transaction_date')
            ->orderBy('id');

        if ($startDate) {
            $query->where('transaction_date', '>=', $startDate);
        }

        $transactions = $query->get();

        // Get the latest balance before startDate, or use initial balance
        $latestBalance = $startDate
            ? Balance::where('account_id', $accountId)
                ->where('balance_date', '<', $startDate)
                ->orderBy('balance_date', 'desc')
                ->orderBy('id', 'desc')
                ->first()
            : null;
        $currentBalance = $latestBalance ? $latestBalance->balance : $account->initial_balance;

        foreach ($transactions as $transaction) {
            $balanceBefore = $currentBalance;

            if ($transaction->account_id === $accountId) {
                if ($transaction->type === TransactionTypeEnum::INCOME) {
                    $currentBalance += $transaction->amount;
                } elseif ($transaction->type === TransactionTypeEnum::EXPENSE) {
                    $currentBalance -= $transaction->amount;
                } elseif ($transaction->type === TransactionTypeEnum::TRANSFER) {
                    $currentBalance -= $transaction->amount;
                }
            } elseif ($transaction->destination_account_id === $accountId && $transaction->type === TransactionTypeEnum::TRANSFER) {
                $currentBalance += $transaction->amount;
            }

            // Create or update balance detail
            BalanceDetail::updateOrCreate(
                ['transaction_id' => $transaction->id, 'account_id' => $accountId],
                [
                    'id' => (string) Str::uuid(),
                    'subscriber_id' => $subscriberId,
                    'amount' => $transaction->amount,
                    'type' => $transaction->type,
                    'balance_before' => $balanceBefore,
                    'balance_after' => $currentBalance,
                    'balance_date' => $transaction->transaction_date,
                ]
            );
        }

        // Update running balance in balances table
        Balance::updateOrCreate(
            ['account_id' => $accountId, 'subscriber_id' => $subscriberId],
            [
                'id' => (string) Str::uuid(),
                'balance' => $currentBalance,
                'balance_date' => $transactions->max('transaction_date') ?? now()->toDateString(),
            ]
        );
    }
}
