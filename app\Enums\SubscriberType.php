<?php

namespace App\Enums;

enum SubscriberType: string
{
    case RT = 'rt';
    case RW = 'rw';
    case LINGKUNGAN = 'lingkungan';
    case KELURAHAN = 'kelurahan';
    case KECAMATAN = 'kecamatan';
    case KABUPATEN = 'kabupaten';
    case PROVINSI = 'provinsi';
    case PERUMAHAN = 'perumahan';
    case APARTEMEN = 'apartemen';
    case KOMPLEK = 'komplek';
    case DESA = 'desa';


    public function label(): string
    {
        return match($this) {
            self::RT => 'RT',
            self::RW => 'RW',
            self::LINGKUNGAN => 'Lingkungan',
            self::KELURAHAN => '<PERSON><PERSON><PERSON>an',
            self::KECAMATAN => 'Kecamatan',
            self::KABUPATEN => 'Kabupaten',
            self::PROVINSI => 'Provinsi',
            self::PERUMAHAN => 'Perumahan',
            self::APARTEMEN => 'Apartemen',
            self::KOMPLEK => 'Komplek',
            self::DESA => 'Desa',
        };
    }


    public function icon(): string
    {
        return match($this) {
            self::RT => 'heroicon-o-home',
            self::RW => 'heroicon-o-user-group',
            self::LINGKUNGAN => 'heroicon-o-map-pin',
            self::KELURAHAN => 'heroicon-o-building-office',
            self::KECAMATAN => 'heroicon-o-building-library',
            self::KABUPATEN => 'heroicon-o-map',
            self::PROVINSI => 'heroicon-o-globe-alt',
            self::PERUMAHAN => 'heroicon-o-home-modern',
            self::APARTEMEN => 'heroicon-o-building-office-2',
            self::KOMPLEK => 'heroicon-o-building-storefront',
            self::DESA => 'heroicon-o-tree',
        };
    }

    // Method untuk mendapatkan warna
    public function color(): string
    {
        return match($this) {
            self::RT, self::PERUMAHAN => 'primary',
            self::RW, self::APARTEMEN => 'info',
            self::LINGKUNGAN, self::KOMPLEK => 'success',
            self::KELURAHAN, self::DESA => 'warning',
            self::KECAMATAN => 'secondary',
            self::KABUPATEN => 'dark',
            self::PROVINSI => 'danger',
        };
    }

    // Method untuk mendapatkan level hierarki (semakin besar semakin tinggi)
    public function hierarchyLevel(): int
    {
        return match($this) {
            self::PROVINSI => 7,
            self::KABUPATEN => 6,
            self::KECAMATAN => 5,
            self::KELURAHAN => 4,
            self::DESA => 4,
            self::RW => 3,
            self::LINGKUNGAN => 2,
            self::RT, self::PERUMAHAN, self::APARTEMEN, self::KOMPLEK => 1,
        };
    }

    // Method untuk cek apakah administratif
    public function isAdministrative(): bool
    {
        return in_array($this, [
            self::RT,
            self::RW,
            self::LINGKUNGAN,
            self::KELURAHAN,
            self::KECAMATAN,
            self::KABUPATEN,
            self::PROVINSI,
            self::DESA,
        ]);
    }

    // Method untuk cek apakah hunian
    public function isResidential(): bool
    {
        return in_array($this, [
            self::PERUMAHAN,
            self::APARTEMEN,
            self::KOMPLEK,
        ]);
    }

    // Method untuk mendapatkan semua nilai enum
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    // Method untuk mendapatkan options untuk select
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }
        return $options;
    }

    // Method untuk grouping berdasarkan kategori dengan hierarki
    public static function getGroupedOptions(): array
    {
        return [
            'Administratif Tingkat Tinggi' => [
                self::PROVINSI->value => self::PROVINSI->label(),
                self::KABUPATEN->value => self::KABUPATEN->label(),
                self::KECAMATAN->value => self::KECAMATAN->label(),
            ],
            'Administratif Tingkat Rendah' => [
                self::KELURAHAN->value => self::KELURAHAN->label(),
                self::DESA->value => self::DESA->label(),
                self::RW->value => self::RW->label(),
                self::RT->value => self::RT->label(),
                self::LINGKUNGAN->value => self::LINGKUNGAN->label(),
            ],
            'Area Hunian' => [
                self::PERUMAHAN->value => self::PERUMAHAN->label(),
                self::APARTEMEN->value => self::APARTEMEN->label(),
                self::KOMPLEK->value => self::KOMPLEK->label(),
            ]
        ];
    }

    // Method untuk mendapatkan options berdasarkan hierarki
    public static function getHierarchicalOptions(): array
    {
        $cases = self::cases();

        // Sort berdasarkan hierarchy level (descending)
        usort($cases, function($a, $b) {
            return $b->hierarchyLevel() <=> $a->hierarchyLevel();
        });

        $options = [];
        foreach ($cases as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }

    // Method untuk mendapatkan administrative levels
    public static function getAdministrativeLevels(): array
    {
        return array_filter(self::cases(), fn($case) => $case->isAdministrative());
    }

    // Method untuk mendapatkan residential types
    public static function getResidentialTypes(): array
    {
        return array_filter(self::cases(), fn($case) => $case->isResidential());
    }
}
