<?php

namespace App\Filament\Resources\Accounts;

use App\Filament\Resources\Accounts\Pages\EditAccount;
use App\Filament\Resources\Accounts\Pages\ListAccounts;
use App\Filament\Resources\Accounts\RelationManagers\BalancesRelationManager;
use App\Filament\Resources\Accounts\RelationManagers\TransactionsRelationManager;
use App\Filament\Resources\Accounts\Schemas\AccountForm;
use App\Filament\Resources\Accounts\Schemas\AccountInfolist;
use App\Filament\Resources\Accounts\Tables\AccountsTable;
use App\Models\Account;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class AccountResource extends Resource
{
    protected static ?string $model = Account::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedFolder;

    protected static ?int $navigationSort = 1;
    protected static string|null|\UnitEnum $navigationGroup = 'Arus Kas';

    public static function getNavigationBadge(): ?string
    {
        $user = auth()->user();

        if (!$user || $user->isSuperAdmin()) {
            return (string)static::getModel()::count();
        }

        $activeSubscription = $user?->activeSubscription();
        if (!$activeSubscription) {
            return null;
        }

        return (string)static::getModel()::where('subscriber_id', $activeSubscription?->id)->count();
    }

    protected static ?string $navigationLabel = 'Akun';
    protected static ?string $modelLabel = 'Akun';

    public static function form(Schema $schema): Schema
    {
        return AccountForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return AccountInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return AccountsTable::configure($table);
    }

    public static function getRelations(): array
    {
        $relations = [
            TransactionsRelationManager::class,
        ];

        if (auth()->user()?->isSuperAdmin()) {
            $relations[] = BalancesRelationManager::class;
        }

        return $relations;
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAccounts::route('/'),
//            'create' => CreateAccount::route('/create'),
//            'view' => ViewAccount::route('/{record}'),
            'edit' => EditAccount::route('/{record}/edit'),
        ];
    }
}
