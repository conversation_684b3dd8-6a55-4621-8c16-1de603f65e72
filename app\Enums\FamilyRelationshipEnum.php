<?php

namespace App\Enums;

enum FamilyRelationshipEnum: string
{
    case KEPALA_KELUARGA = 'kepala_keluarga';
    case SUAMI = 'suami';
    case ISTRI = 'istri';
    case ANAK = 'anak';
    case MENANTU = 'menantu';
    case CUCU = 'cucu';
    case ORANGTUA = 'orangtua';
    case MERTUA = 'mertua';
    case FAMILI_LAIN = 'famili_lain';
    case PEMBANTU = 'pembantu';
    case LAINNYA = 'lainnya';

    public function label(): string
    {
        return match ($this) {
            self::SUAMI => 'Suami',
            self::ISTRI => 'Istri',
            self::ANAK => 'Anak',
            self::MENANTU => 'Menantu',
            self::CUCU => 'Cucu',
            self::ORANGTUA => 'Orang Tua',
            self::MERTUA => 'Mertua',
            self::FAMILI_LAIN => 'Famili Lain',
            self::PEMBANTU => 'Pembantu',
            self::LAINNYA => 'Lainnya',
        };
    }

    public function isSpouse(): bool
    {
        return in_array($this, [self::SUAMI, self::ISTRI]);
    }

    public function isChild(): bool
    {
        return $this === self::ANAK;
    }

    public function isHeadOfFamily(): bool
    {
        return $this === self::KEPALA_KELUARGA;
    }

    public function icon(): string
    {
        return match ($this) {
            self::KEPALA_KELUARGA => 'heroicon-o-user-circle',
            self::SUAMI => 'heroicon-o-user',
            self::ISTRI => 'heroicon-o-user',
            self::ANAK => 'heroicon-o-user-plus',
            self::MENANTU => 'heroicon-o-users',
            self::CUCU => 'heroicon-o-user-group',
            self::ORANGTUA => 'heroicon-o-user-minus',
            self::MERTUA => 'heroicon-o-users',
            self::FAMILI_LAIN => 'heroicon-o-ellipsis-horizontal-circle',
            self::PEMBANTU => 'heroicon-o-wrench-screwdriver',
            self::LAINNYA => 'heroicon-o-question-mark-circle',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::KEPALA_KELUARGA => 'primary',
            self::SUAMI => 'blue',
            self::ISTRI => 'pink',
            self::ANAK => 'green',
            self::MENANTU => 'teal',
            self::CUCU => 'emerald',
            self::ORANGTUA => 'amber',
            self::MERTUA => 'orange',
            self::FAMILI_LAIN => 'gray',
            self::PEMBANTU => 'slate',
            self::LAINNYA => 'stone',
        };
    }

    public static function options(): array
    {
        return array_reduce(self::cases(), function ($options, $case) {
            $options[$case->value] = $case->label();
            return $options;
        }, []);
    }

    public static function coreFamilyOptions(): array
    {
        return [
            self::SUAMI->value => self::SUAMI->label(),
            self::ISTRI->value => self::ISTRI->label(),
            self::ANAK->value => self::ANAK->label(),
        ];
    }

    public static function extendedFamilyOptions(): array
    {
        return [
            self::ORANGTUA->value => self::ORANGTUA->label(),
            self::MERTUA->value => self::MERTUA->label(),
            self::MENANTU->value => self::MENANTU->label(),
            self::CUCU->value => self::CUCU->label(),
        ];
    }

    public static function getGroupedOptions(): array
    {
        return [
            'Keluarga Inti' => [
                self::SUAMI->value => self::SUAMI->label(),
                self::ISTRI->value => self::ISTRI->label(),
                self::ANAK->value => self::ANAK->label(),
            ],
            'Keluarga Besar' => [
                self::ORANGTUA->value => self::ORANGTUA->label(),
                self::MERTUA->value => self::MERTUA->label(),
                self::MENANTU->value => self::MENANTU->label(),
                self::CUCU->value => self::CUCU->label(),
            ],
            'Lainnya' => [
                self::FAMILI_LAIN->value => self::FAMILI_LAIN->label(),
                self::PEMBANTU->value => self::PEMBANTU->label(),
                self::LAINNYA->value => self::LAINNYA->label(),
            ]
        ];
    }

}
