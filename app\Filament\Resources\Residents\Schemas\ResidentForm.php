<?php

namespace App\Filament\Resources\Residents\Schemas;

use App\Enums\BloodTypeEnum;
use App\Enums\EducationLevelEnum;
use App\Enums\FamilyRelationshipEnum;
use App\Enums\GenderEnum;
use App\Enums\MaritalStatusEnum;
use App\Enums\OccupationEnum;
use App\Enums\ReligionEnum;
use App\Enums\ResidentStatusEnum;
use App\Models\Family;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;
use Illuminate\Database\Eloquent\Builder;

class ResidentForm
{
    public static function formatFamilyId($number): string
    {
        $str = (string)$number;
        $formatted = '';
        for ($i = 0; $i < strlen($str); $i += 4) {
            if ($i > 0) {
                $formatted .= '-';
            }
            $formatted .= substr($str, $i, 4);
        }
        return $formatted;
    }

    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('subscriber_id')
                    ->relationship('subscriber', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->lazy()
                    ->hidden(fn() => !auth()->user()?->isSuperadmin())
                    ->dehydrated(fn() => auth()->user()?->isSuperadmin()),

                Select::make('family_id')
                    ->label('No KK')
                    ->relationship('family', 'family_identifier')
                    ->searchable()
                    ->getOptionLabelUsing(function ($value) {
                        return static::formatFamilyId($value);
                    })
                    ->getSearchResultsUsing(function (string $search) {
                        $searchTerm = str_replace('-', '', $search);
                        return Family::where('family_identifier', 'like', "%{$searchTerm}%")
                            ->pluck('family_identifier', 'id')
                            ->mapWithKeys(function ($identifier, $id) {
                                return [$id => static::formatFamilyId($identifier)];
                            })
                            ->toArray();
                    })
                    ->options(function () {
                        return Family::pluck('family_identifier', 'id')
                            ->mapWithKeys(function ($identifier, $id) {
                                return [$id => static::formatFamilyId($identifier)];
                            })
                            ->toArray();
                    })
                    ->lazy(),
                Select::make('family_status')
                    ->label('Status Keluarga')
                    ->native(false)
                    ->options(FamilyRelationshipEnum::getGroupedOptions()),
                TextInput::make('nik')
                    ->label('NIK')
                    ->placeholder('1234xxx')
                    ->length(16)
                    ->unique(ignoreRecord:  true)
                    ->validationMessages([
                        'unique' => 'The :attribute has already been registered.',
                    ])
                    ->validationAttribute('NIK')
                    ->required(),
                TextInput::make('fullname')
                    ->label('Nama Lengkap')
                    ->placeholder('Budi')
                    ->required(),
                Select::make('gender')
                    ->label('Jenis Kelamin')
                    ->native(false)
                    ->options(GenderEnum::select()),
                TextInput::make('birthplace')
                    ->label('Tempat Lahir')
                    ->placeholder('Jakarta')
                    ->required(),
                DatePicker::make('birthdate')
                    ->default(now())
                    ->label('Tanggal Lahir')
                    ->required(),
                Select::make('religion')
                    ->label('Agama')
                    ->native(false)
                    ->options(ReligionEnum::select()),
                Select::make('marital_status')
                    ->label('Status Pernikahan')
                    ->native(false)
                    ->options(MaritalStatusEnum::options()),
                Select::make('education')
                    ->label('Pendidikan Terakhir')
                    ->native(false)
                    ->options(EducationLevelEnum::options()),
                Select::make('job')
                    ->label('Pekerjaan')
                    ->native(false)
                    ->options(OccupationEnum::options()),
                Select::make('blood_type')
                    ->label('Golongan Darah')
                    ->native(false)
                    ->options(BloodTypeEnum::options()),
                Select::make('resident_status')
                    ->label('Status Warga')
                    ->native(false)
                    ->options(ResidentStatusEnum::residenceOptions()),
                Checkbox::make('is_head_of_family')
                    ->label('Kepala Keluarga?')
                    ->columnSpanFull()
            ]);
    }
}
