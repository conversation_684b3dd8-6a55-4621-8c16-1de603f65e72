<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

//        User::factory()->create([
//            'name' => 'Test User',
//            'email' => '<EMAIL>',
//        ]);

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $this->call([
            ProvinceSeeder::class,
            RegencySeeder::class,
            DistrictSeeder::class,
            VillageSeeder::class,
            UserSeeder::class,
            SubscriberSeeder::class,
            SubscriberUserSeeder::class,
        ]);

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    }
}
