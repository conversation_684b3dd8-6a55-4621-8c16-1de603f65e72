<?php

namespace App\Filament\Resources\Balances;

use App\Filament\Resources\Balances\Pages\CreateBalance;
use App\Filament\Resources\Balances\Pages\EditBalance;
use App\Filament\Resources\Balances\Pages\ListBalances;
use App\Filament\Resources\Balances\Pages\ViewBalance;
use App\Filament\Resources\Balances\RelationManagers\BalanceDetailsRelationManager;
use App\Filament\Resources\Balances\Schemas\BalanceForm;
use App\Filament\Resources\Balances\Schemas\BalanceInfolist;
use App\Filament\Resources\Balances\Tables\BalancesTable;
use App\Models\Balance;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class BalanceResource extends Resource
{
    protected static ?string $model = Balance::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedBanknotes;

    protected static ?int $navigationSort = 4;
    protected static string|null|\UnitEnum $navigationGroup = 'Arus Kas';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check() && auth()->user()->isSuperAdmin();
    }

    public static function canAccess(): bool
    {
        return static::shouldRegisterNavigation();
    }

    public static function getNavigationBadge(): ?string
    {
        $user = auth()->user();

        if (!$user || $user->isSuperAdmin()) {
            return (string) static::getModel()::count();
        }

        $activeSubscription = $user->activeSubscription();
        if (!$activeSubscription) {
            return null;
        }

        return (string) static::getModel()::where('subscriber_id', $activeSubscription->id)->count();
    }
    protected static ?string $navigationLabel =  'Saldo';
    protected static ?string $modelLabel = 'Saldo';

    public static function form(Schema $schema): Schema
    {
        return BalanceForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return BalanceInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return BalancesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            BalanceDetailsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListBalances::route('/'),
            'create' => CreateBalance::route('/create'),
            'view' => ViewBalance::route('/{record}'),
            'edit' => EditBalance::route('/{record}/edit'),
        ];
    }
}
