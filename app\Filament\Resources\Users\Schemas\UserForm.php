<?php

namespace App\Filament\Resources\Users\Schemas;

use App\Enums\UserRoleEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Hash;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Nama')
                    ->placeholder('Nama User')
                    ->required()
                    ->maxLength(255),
                TextInput::make('email')
                    ->label('Email')
                    ->placeholder('Email User')
                    ->email()
                    ->unique(ignoreRecord: true)
                    ->required()
                    ->maxLength(255),
                Select::make('global_role')
                    ->options(UserRoleEnum::options())
                    ->label('Global Role')
                    ->required()
                    ->default('user')
                    ->native(false),

                Section::make('Password Settings')
                    ->description('Leave blank to keep the current password unchanged.')
                    ->collapsible()
                    ->components([
                        TextInput::make('password')
                            ->password()
                            ->label('Password')
                            ->placeholder('Enter new password')
                            ->minLength(8)
                            ->required(fn ($livewire) => $livewire instanceof \Filament\Resources\Pages\CreateRecord)
                            ->dehydrateStateUsing(fn ($state) => $state ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state)) // Only include password if filled
                            ->autocomplete('new-password'),
                        TextInput::make('password_confirmation')
                            ->password()
                            ->label('Confirm Password')
                            ->placeholder('Confirm new password')
                            ->same('password')
                            ->minLength(8)
                            ->requiredWith('password')
                            ->dehydrated(false)
                            ->autocomplete('new-password'),
                    ])
                    ->columnSpanFull()
                    ->visible(fn ($livewire) => $livewire instanceof \Filament\Resources\Pages\CreateRecord || $livewire instanceof \Filament\Resources\Pages\EditRecord),
            ]);
    }
}
