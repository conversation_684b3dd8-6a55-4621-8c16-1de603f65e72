<?php

namespace App\Filament\Resources\Families\Pages;

use App\Filament\Resources\Families\FamilyResource;
use App\Models\Family;
use Filament\Actions\CreateAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\Width;
use Filament\Support\Icons\Heroicon;

class ListFamilies extends ListRecords
{
    protected static string $resource = FamilyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Tambah Keluarga')
                ->modalHeading('Tambah Keluarga Baru')
                ->closeModalByClickingAway(false)
                ->modalWidth(Width::ThreeExtraLarge),
        ];
    }

//    public function mount(): void
//    {
//        parent::mount();
//
//        $user = auth()->user();
//        if ($user && !$user->isSuperadmin()) {
//            $count = cache()->remember('families_count_' . $user->id, 60, fn () =>
//            Family::query()->count()
//            );
//            if ($count === 0) {
//                Notification::make()
//                    ->title('Tidak ada data keluarga')
//                    ->body('Database keluarga kosong, silahkan tambahkan keluarga baru')
//                    ->warning()
//                    ->persistent()
//                    ->send();
//                redirect()->route('filament.admin.resources.subscribers.index');
//            }
//        }
//    }
//
//    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Relations\Relation|null
//    {
//        return parent::getTableQuery()->with(['subscriber', 'village']);
//    }
}
