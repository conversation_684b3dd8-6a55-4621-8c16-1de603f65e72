<?php

namespace App\Enums;

enum UserRoleEnum: string
{
    case SUPERADMIN = 'superadmin';
    case USER = 'user';

    public function label(): string
    {
        return match ($this) {
            self::SUPERADMIN => 'Super Admin',
            self::USER => 'User',
        };
    }

    public function permissions(): array
    {
        return match ($this) {
            self::SUPERADMIN => ['create', 'read', 'update', 'delete', 'manage_users'],
            self::USER => ['create', 'read', 'update_own'],
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::SUPERADMIN => 'blue',
            self::USER => 'green',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::SUPERADMIN => 'heroicon-s-shield-exclamation',
            self::USER => 'heroicon-s-user',
        };
    }

    public static function options(): array
    {
        return [
            self::SUPERADMIN->value => self::SUPERADMIN->label(),
            self::USER->value => self::USER->label(),
        ];
    }

    public static function selectOptions(): array
    {
        return self::options();
    }
}
