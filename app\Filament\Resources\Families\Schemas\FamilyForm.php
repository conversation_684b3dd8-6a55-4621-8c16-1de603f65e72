<?php

namespace App\Filament\Resources\Families\Schemas;

use App\Models\District;
use App\Models\Province;
use App\Models\Regency;
use App\Models\Village;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Components\Utilities\Set;
use Filament\Schemas\Schema;

class FamilyForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('subscriber_id')
                    ->relationship('subscriber', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->lazy()
                    ->hidden(fn() => !auth()->user()?->isSuperadmin())
                    ->dehydrated(fn() => auth()->user()?->isSuperadmin()),

                TextInput::make('family_identifier')
                    ->label('No KK')
                    ->required()
                    ->placeholder('1234xxxx')
                    ->columnSpan(function (Get $get) {
                        return auth()->user()?->isSuperadmin() ? 1 : 'full';
                    }),

                Select::make('province_id')
                    ->label('Provinsi')
                    ->searchable()
                    ->dehydrated(false)
                    ->required()
                    ->options(Province::pluck('name', 'id'))
                    ->live()
                    ->afterStateUpdated(function ($state, Set $set) {
                        $set('regency_id', null);
                        $set('district_id', null);
                        $set('village_id', null);
                    })
                    ->columnSpan(function (Get $get) {
                        return $get('province_id') ? 1 : 'full';
                    })
                    ->afterStateHydrated(function (Select $component, $state, $record) {
                        if (!$state && $record?->village_id) {
                            $village = Village::with('district.regency.province')->find($record->village_id);
                            $component->state($village->district->regency->province_id ?? null);
                        }
                    }),

                // KABUPATEN
                Select::make('regency_id')
                    ->label('Kabupaten/Kota')
                    ->searchable()
                    ->required()
                    ->dehydrated(false)
                    ->options(function (Get $get) {
                        return $get('province_id') ?
                            Regency::where('province_id', $get('province_id'))->pluck('name', 'id')
                            : [];
                    })
                    ->live()
                    ->afterStateUpdated(function ($state, Set $set) {
                        $set('district_id', null);
                        $set('village_id', null);
                    })
                    ->hidden(fn(Get $get) => !$get('province_id'))
                    ->afterStateHydrated(function (Select $component, $state, $record) {
                        if (!$state && $record?->village_id) {
                            $village = Village::with('district.regency')->find($record->village_id);
                            $component->state($village->district->regency_id ?? null);
                        }
                    }),

                Select::make('district_id')
                    ->label('Kecamatan')
                    ->searchable()
                    ->required()
                    ->dehydrated(false)
                    ->options(function (Get $get) {
                        return $get('regency_id') ?
                            District::where('regency_id', $get('regency_id'))->pluck('name', 'id')
                            : [];
                    })
                    ->live()
                    ->afterStateUpdated(fn($state, Set $set) => $set('village_id', null))
                    ->hidden(fn(Get $get) => !$get('regency_id'))
                    ->afterStateHydrated(function (Select $component, $state, $record) {
                        if (!$state && $record?->village_id) {
                            $village = Village::with('district')->find($record->village_id);
                            $component->state($village->district_id ?? null);
                        }
                    }),

                // DESA
                Select::make('village_id')
                    ->label('Kelurahan/Desa')
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->options(function (Get $get) {
                        return $get('district_id') ?
                            Village::where('district_id', $get('district_id'))->pluck('name', 'id')
                            : [];
                    })
                    ->hidden(fn(Get $get) => !$get('district_id')),

                TextInput::make('rt')
                    ->required()
                    ->hidden(fn(callable $get) => $get('village_id') == null),
                TextInput::make('rw')
                    ->required()
                    ->hidden(fn(callable $get) => $get('village_id') == null),
                Textarea::make('address')
                    ->columnSpanFull()
                    ->required()
                    ->reactive()
                    ->label('Alamat')
                    ->placeholder('Alamat')
                    ->hidden(fn(callable $get) => $get('village_id') == null),

            ]);
    }
}
