<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {

        Schema::dropIfExists('balances');
        Schema::create('balances', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('account_id')->nullable()->constrained()->nullOnDelete();
            $table->decimal('balance', 15, 2)->comment('Saldo berjalan akun');
            $table->date('balance_date')->comment('Tanggal terakhir pembaruan saldo');
            $table->timestamps();
            $table->softDeletes();
        });


        Schema::create('balance_details', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->constrained()->onDelete('cascade');
            $table->foreignUuid('account_id')->constrained()->onDelete('cascade');
            $table->foreignUuid('transaction_id')->constrained()->onDelete('cascade')->comment('Transaksi terkait');
            $table->decimal('amount', 15, 2)->comment('Jumlah transaksi');
            $table->string('type')->comment('Tipe: income, expense, transfer');
            $table->decimal('balance_before', 15, 2)->comment('Saldo sebelum transaksi');
            $table->decimal('balance_after', 15, 2)->comment('Saldo setelah transaksi');
            $table->date('balance_date')->comment('Tanggal transaksi');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['account_id', 'balance_date', 'transaction_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('balance_details');
        Schema::dropIfExists('balances');
        Schema::create('balances', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->constrained()->onDelete('cascade');
            $table->foreignUuid('account_id')->constrained()->onDelete('cascade');
            $table->decimal('balance', 15, 2)->comment('Saldo akun pada waktu tertentu');
            $table->date('balance_date')->comment('Tanggal pencatatan saldo');
            $table->foreignUuid('transaction_id')->nullable()->constrained()->onDelete('set null')->comment('Transaksi terkait');
            $table->timestamps();
            $table->softDeletes();
        });
    }
};
