<?php

namespace App\Models;

use App\Models\Concerns\TenantScoped;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Family extends Model
{
    use SoftDeletes, HasUuids, TenantScoped, LogsActivity;
    protected $keyType = 'string';
    public $incrementing = false;

    protected $guarded = [];

    public function subscriber(): BelongsTo
    {
        return $this->belongsTo(Subscriber::class);
    }

    public function residents(): HasMany
    {
        return $this->hasMany(Resident::class);
    }

    public function village(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Village::class);
    }

    public function headFamily(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Resident::class)->where('is_head_of_family', true);
    }

    public function familyMembers(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Resident::class, 'family_id', 'family_id')
            ->where('id', '!=', $this->id);
    }

    public function getDistrictAttribute()
    {
        return $this->village->district ?? null;
    }

    public function getRegencyAttribute()
    {
        return $this->village->district->regency ?? null;
    }

    public function getProvinceAttribute()
    {
        return $this->village->district->regency->province ?? null;
    }

    public function getAddressHierarchyAttribute(): string
    {
        return implode(', ', array_filter([
            $this->village->name ?? null,
            $this->village->district->name ?? null,
            $this->village->district->regency->name ?? null,
            $this->village->district->regency->province->name ?? null
        ]));
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded() // Mencatat semua kolom yang tidak di-guardeds
            ->setDescriptionForEvent(fn(string $eventName) => "Family has been {$eventName}")
            ->useLogName('Family');
    }
}
