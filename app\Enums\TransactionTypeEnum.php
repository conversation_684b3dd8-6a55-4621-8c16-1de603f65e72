<?php

namespace App\Enums;

enum TransactionTypeEnum: string
{
    case INCOME = 'income';
    case EXPENSE = 'expense';
    case TRANSFER = 'transfer';

    public function label(): string
    {
        return match ($this) {
            self::INCOME => 'Pendapatan',
            self::EXPENSE => 'Pengeluaran',
            self::TRANSFER => 'Transfer',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::INCOME => 'success',
            self::EXPENSE => 'danger',
            self::TRANSFER => 'info',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::INCOME => 'heroicon-o-arrow-down-tray',
            self::EXPENSE => 'heroicon-o-arrow-up-tray',
            self::TRANSFER => 'heroicon-o-arrows-right-left',
        };
    }

    public function direction(): string
    {
        return match ($this) {
            self::INCOME => 'in',
            self::EXPENSE => 'out',
            self::TRANSFER => 'transfer',
        };
    }

    public static function options(): array
    {
        return [
            self::INCOME->value => self::INCOME->label(),
            self::EXPENSE->value => self::EXPENSE->label(),
            self::TRANSFER->value => self::TRANSFER->label(),
        ];
    }

    public static function selectOptions(): array
    {
        return self::options();
    }

}
