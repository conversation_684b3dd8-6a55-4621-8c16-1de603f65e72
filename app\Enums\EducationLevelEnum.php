<?php

namespace App\Enums;

enum EducationLevelEnum: string
{
    case TIDAK_SEKOLAH = 'tidak_sekolah';
    case SD = 'sd';
    case SMP = 'smp';
    case SMA = 'sma';
    case D1 = 'd1';
    case D2 = 'd2';
    case D3 = 'd3';
    case D4 = 'd4';
    case S1 = 's1';
    case S2 = 's2';
    case S3 = 's3';
    case PAKET_A = 'paket_a';
    case PAKET_B = 'paket_b';
    case PAKET_C = 'paket_c';

    public function label(): string
    {
        return match ($this) {
            self::TIDAK_SEKOLAH => 'Tidak/Belum Sekolah',
            self::SD => 'SD/Sederajat',
            self::SMP => 'SMP/Sederajat',
            self::SMA => 'SMA/Sederajat',
            self::D1 => 'Diploma 1',
            self::D2 => 'Diploma 2',
            self::D3 => 'Diploma 3',
            self::D4 => 'Diploma 4',
            self::S1 => 'Strata 1',
            self::S2 => 'Strata 2',
            self::S3 => 'Strata 3',
            self::PAKET_A => 'Paket A (Setara SD)',
            self::PAKET_B => 'Paket B (Setara SMP)',
            self::PAKET_C => 'Paket C (Setara SMA)',
        };
    }

    public function shortLabel(): string
    {
        return match ($this) {
            self::TIDAK_SEKOLAH => 'Tidak Sekolah',
            self::SD => 'SD',
            self::SMP => 'SMP',
            self::SMA => 'SMA',
            self::D1 => 'D1',
            self::D2 => 'D2',
            self::D3 => 'D3',
            self::D4 => 'D4',
            self::S1 => 'S1',
            self::S2 => 'S2',
            self::S3 => 'S3',
            self::PAKET_A => 'Paket A',
            self::PAKET_B => 'Paket B',
            self::PAKET_C => 'Paket C',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::TIDAK_SEKOLAH => 'heroicon-o-x-circle',
            self::SD => 'heroicon-o-academic-cap',
            self::SMP => 'heroicon-o-academic-cap',
            self::SMA => 'heroicon-o-academic-cap',
            self::D1, self::D2, self::D3, self::D4 => 'heroicon-o-document-text',
            self::S1, self::S2, self::S3 => 'heroicon-o-book-open',
            self::PAKET_A, self::PAKET_B, self::PAKET_C => 'heroicon-o-clipboard-document-list',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::TIDAK_SEKOLAH => 'gray',
            self::SD => 'blue',
            self::SMP => 'indigo',
            self::SMA => 'purple',
            self::D1 => 'yellow',
            self::D2 => 'amber',
            self::D3 => 'orange',
            self::D4 => 'red',
            self::S1 => 'green',
            self::S2 => 'emerald',
            self::S3 => 'teal',
            self::PAKET_A => 'sky',
            self::PAKET_B => 'cyan',
            self::PAKET_C => 'violet',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return array_reduce(self::cases(), function ($options, $case) {
            $options[$case->value] = $case->label();
            return $options;
        }, []);
    }

    public static function selectOptions(): array
    {
        return [
            self::TIDAK_SEKOLAH->value => self::TIDAK_SEKOLAH->label(),
            self::SD->value => self::SD->label(),
            self::SMP->value => self::SMP->label(),
            self::SMA->value => self::SMA->label(),
            self::D1->value => self::D1->label(),
            self::D2->value => self::D2->label(),
            self::D3->value => self::D3->label(),
            self::D4->value => self::D4->label(),
            self::S1->value => self::S1->label(),
            self::S2->value => self::S2->label(),
            self::S3->value => self::S3->label(),
            self::PAKET_A->value => self::PAKET_A->label(),
            self::PAKET_B->value => self::PAKET_B->label(),
            self::PAKET_C->value => self::PAKET_C->label(),
        ];
    }

    public static function formalEducationOptions(): array
    {
        return [
            self::SD->value => self::SD->label(),
            self::SMP->value => self::SMP->label(),
            self::SMA->value => self::SMA->label(),
            self::D1->value => self::D1->label(),
            self::D2->value => self::D2->label(),
            self::D3->value => self::D3->label(),
            self::D4->value => self::D4->label(),
            self::S1->value => self::S1->label(),
            self::S2->value => self::S2->label(),
            self::S3->value => self::S3->label(),
        ];
    }

    public static function nonFormalEducationOptions(): array
    {
        return [
            self::PAKET_A->value => self::PAKET_A->label(),
            self::PAKET_B->value => self::PAKET_B->label(),
            self::PAKET_C->value => self::PAKET_C->label(),
        ];
    }
}
