<?php

namespace App\Filament\Resources\Provinces\RelationManagers;

use App\Filament\Resources\Regencies\RegencyResource;
use Filament\Actions\CreateAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class RegenciesRelationManager extends RelationManager
{
    protected static string $relationship = 'regencies';

    protected static ?string $relatedResource = RegencyResource::class;

    public function table(Table $table): Table
    {
        return $table
            ->headerActions([
                CreateAction::make(),
            ]);
    }
}
