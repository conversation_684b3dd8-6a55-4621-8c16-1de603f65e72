<?php

namespace App\Filament\Resources\Residents\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ResidentInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('subscriber.name'),
                TextEntry::make('family.id'),
                TextEntry::make('nik'),
                TextEntry::make('fullname'),
                TextEntry::make('gender'),
                TextEntry::make('birthplace'),
                TextEntry::make('birthdate')
                    ->date(),
                TextEntry::make('religion'),
                TextEntry::make('marital_status'),
                TextEntry::make('education'),
                TextEntry::make('job'),
                TextEntry::make('blood_type'),
                TextEntry::make('resident_status'),
                TextEntry::make('moving_date')
                    ->date(),
                TextEntry::make('death_date')
                    ->date(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                TextEntry::make('deleted_at')
                    ->dateTime(),
            ]);
    }
}
