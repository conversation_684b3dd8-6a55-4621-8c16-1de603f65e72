<?php

namespace App\Filament\Resources\TransactionCategories\Tables;

use App\Enums\COATypeEnum;
use App\Enums\DebitCreditEnum;
use App\Enums\TransactionTypeEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\RestoreAction;
use Filament\Actions\ViewAction;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class TransactionCategoriesTable
{
    public static function configure(Table $table): Table
    {
        return $table->columns([
            TextColumn::make('subscriber.name')
                ->label('Pelanggan')
                ->limit(25)
                ->searchable()
                ->visible(fn() => auth()->user()->isSuperAdmin()),
            TextColumn::make('name')
                ->label('Nama')
                ->searchable(),
            TextColumn::make('type')
                ->formatStateUsing(fn (?TransactionTypeEnum $state): string => $state?->label() ?? '-')
                ->icon(fn (?TransactionTypeEnum $state): string => $state?->icon() ?? '-')
                ->iconColor(fn (?TransactionTypeEnum $state): string => $state?->color() ?? '-')
                ->color(fn (?TransactionTypeEnum $state): string => $state?->color() ?? '-')
                ->searchable()
                ->label('Tipe'),
            TextColumn::make('description')
                ->label('Deskripsi')
                ->limit(50),
        ])
            ->filters([
                SelectFilter::make('type')
                    ->label('Tipe')
                    ->native(false)
                    ->options(TransactionTypeEnum::options()),
            ])
            ->recordActions([
                EditAction::make()
                    ->label(false)
                    ->tooltip('Edit data')
                    ->icon(Heroicon::PencilSquare),
                RestoreAction::make()
                    ->label(false)
                    ->tooltip('Pulihkan data')
                    ->icon(Heroicon::ArrowPath),
                DeleteAction::make()
                    ->label(false)
                    ->tooltip('Hapus data')
                    ->icon(Heroicon::Trash),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
