<?php

namespace App\Filament\Resources\Families\RelationManagers;

use App\Enums\BloodTypeEnum;
use App\Enums\EducationLevelEnum;
use App\Enums\FamilyRelationshipEnum;
use App\Enums\GenderEnum;
use App\Enums\MaritalStatusEnum;
use App\Enums\OccupationEnum;
use App\Enums\ReligionEnum;
use App\Enums\ResidentStatusEnum;
use App\Models\Family;
use Carbon\Carbon;
use Filament\Actions\AssociateAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DissociateAction;
use Filament\Actions\DissociateBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Support\Enums\Width;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class ResidentsRelationManager extends RelationManager
{
    protected static string $relationship = 'residents';


    public static function formatFamilyId($number): string
    {
        $str = (string)$number;
        $formatted = '';
        for ($i = 0; $i < strlen($str); $i += 4) {
            if ($i > 0) {
                $formatted .= '-';
            }
            $formatted .= substr($str, $i, 4);
        }
        return $formatted;
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('subscriber_id')
                    ->relationship('subscriber', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->lazy()
                    ->hidden(fn() => !auth()->user()?->isSuperadmin())
                    ->dehydrated(fn() => auth()->user()?->isSuperadmin()),

                Select::make('family_id')
                    ->label('No KK')
                    ->relationship('family', 'family_identifier')
                    ->searchable()
                    ->getOptionLabelUsing(function ($value) {
                        return static::formatFamilyId($value);
                    })
                    ->getSearchResultsUsing(function (string $search) {
                        $searchTerm = str_replace('-', '', $search);
                        return Family::where('family_identifier', 'like', "%{$searchTerm}%")
                            ->pluck('family_identifier', 'id')
                            ->mapWithKeys(function ($identifier, $id) {
                                return [$id => static::formatFamilyId($identifier)];
                            })
                            ->toArray();
                    })
                    ->options(function () {
                        return Family::pluck('family_identifier', 'id')
                            ->mapWithKeys(function ($identifier, $id) {
                                return [$id => static::formatFamilyId($identifier)];
                            })
                            ->toArray();
                    })
                    ->lazy(),
                Select::make('family_status')
                    ->label('Status Keluarga')
                    ->native(false)
                    ->options(FamilyRelationshipEnum::getGroupedOptions()),
                TextInput::make('nik')
                    ->label('NIK')
                    ->placeholder('1234xxx')
                    ->length(16)
                    ->unique(ignoreRecord: true)
                    ->validationMessages([
                        'unique' => 'The :attribute has already been registered.',
                    ])
                    ->validationAttribute('NIK')
                    ->required(),
                TextInput::make('fullname')
                    ->label('Nama Lengkap')
                    ->placeholder('Budi')
                    ->required(),
                Select::make('gender')
                    ->label('Jenis Kelamin')
                    ->native(false)
                    ->options(GenderEnum::select()),
                TextInput::make('birthplace')
                    ->label('Tempat Lahir')
                    ->placeholder('Jakarta')
                    ->required(),
                DatePicker::make('birthdate')
                    ->default(now())
                    ->label('Tanggal Lahir')
                    ->required(),
                Select::make('religion')
                    ->label('Agama')
                    ->native(false)
                    ->options(ReligionEnum::select()),
                Select::make('marital_status')
                    ->label('Status Pernikahan')
                    ->native(false)
                    ->options(MaritalStatusEnum::options()),
                Select::make('education')
                    ->label('Pendidikan Terakhir')
                    ->native(false)
                    ->options(EducationLevelEnum::options()),
                Select::make('job')
                    ->label('Pekerjaan')
                    ->native(false)
                    ->options(OccupationEnum::options()),
                Select::make('blood_type')
                    ->label('Golongan Darah')
                    ->native(false)
                    ->options(BloodTypeEnum::options()),
                Select::make('resident_status')
                    ->label('Status Warga')
                    ->native(false)
                    ->options(ResidentStatusEnum::residenceOptions()),
                Checkbox::make('is_head_of_family')
                    ->label('Kepala Keluarga?')
                    ->columnSpanFull()
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('fullname')
            ->columns([
                TextColumn::make('family.family_identifier')
                    ->label('No KK')
                    ->formatStateUsing(function ($state) {
                        return static::formatFamilyId($state);
                    })
                    ->searchable(),
                TextColumn::make('nik')
                    ->label('NIK')
                    ->formatStateUsing(function ($state) {
                        return static::formatFamilyId($state);
                    })
                    ->searchable(),
                TextColumn::make('fullname')
                    ->label('Nama Lengkap')
                    ->searchable(),
                TextColumn::make('age')
                    ->label('Usia')
                    ->getStateUsing(function ($record) {
                        if (!$record->birthdate) {
                            return '-';
                        }

                        $birthdate = Carbon::parse($record->birthdate);
                        $now = Carbon::now();

                        $years = (int)abs($now->diffInYears($birthdate));
                        $months = (int)abs($now->diffInMonths($birthdate) % 12);

                        return "{$years} tahun, {$months} bulan";
                    })
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('birthdate', $direction == 'asc' ? 'desc' : 'asc');
                    }),
                TextColumn::make('family_status')
                    ->label('Status Keluarga')
                    ->formatStateUsing(fn(?string $state): string => $state ? FamilyRelationshipEnum::from($state)->label() : '-')
                    ->searchable(),

                CheckboxColumn::make('is_head_of_family')
                    ->label('Kepala'),
                TextColumn::make('gender')
                    ->label('Jenis Kelamin')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(?string $state): string => $state ? GenderEnum::from($state)->label() : '-')
                    ->searchable(),
                TextColumn::make('birthplace')
                    ->label('Tempat Lahir')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('birthdate')
                    ->label('Tanggal Lahir')
                    ->date()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                TextColumn::make('religion')
                    ->label('Agama')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(?string $state): string => $state ? ReligionEnum::from($state)->label() : '-')
                    ->searchable(),
                TextColumn::make('marital_status')
                    ->label('Status Pernikahan')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(?string $state): string => $state ? MaritalStatusEnum::from($state)->label() : '-')
                    ->searchable(),
                TextColumn::make('education')
                    ->label('Pendidikan Terakhir')
                    ->formatStateUsing(fn(?string $state): string => $state ? EducationLevelEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('job')
                    ->label('Pekerjaan')
                    ->formatStateUsing(fn(?string $state): string => $state ? OccupationEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('blood_type')
                    ->label('Jenis Kelamin')
                    ->formatStateUsing(fn(?string $state): string => $state ? BloodTypeEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('resident_status')
                    ->label('Status Pernikahan')
                    ->formatStateUsing(fn(?string $state): string => $state ? ResidentStatusEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('Tambah')
                    ->modalHeading('Tambah Anggota Keluarga Baru')
                    ->closeModalByClickingAway(false)
                    ->modalWidth(Width::ThreeExtraLarge),

                AssociateAction::make()
                    ->label('Hubungkan Warga')
                    ->modalHeading('Hubungkan Warga ke Keluarga')
                    ->recordSelectOptionsQuery(fn($query) => $query->whereNull('family_id'))
                    ->preloadRecordSelect()
                    ->recordTitle(fn(?Model $record) => $record?->fullname ?? 'Unknown'),
            ])
            ->recordActions([
                EditAction::make()
                    ->modalHeading('Edit Data Anggota Keluarga'),
                DissociateAction::make()
                    ->modalHeading('Lepaskan Hubungan Keluarga'),
                DeleteAction::make()
                    ->modalHeading('Hapus Anggota Keluarga'),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DissociateBulkAction::make(),
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
