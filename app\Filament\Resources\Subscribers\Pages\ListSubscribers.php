<?php

namespace App\Filament\Resources\Subscribers\Pages;

use App\Filament\Resources\Subscribers\SubscriberResource;
use Carbon\Carbon;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Tabs\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListSubscribers extends ListRecords
{
    protected static string $resource = SubscriberResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }


    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            'active' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('created_at', Carbon::today())),
            'inactive' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('updated_at', Carbon::today())),
            'trial' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('updated_at', Carbon::today())),
            'pending' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('updated_at', Carbon::today())),
            'suspend' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('updated_at', Carbon::today())),
        ];
    }


}
