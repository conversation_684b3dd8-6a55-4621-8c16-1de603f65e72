<?php

namespace App\Filament\Resources\Families;

use App\Filament\Resources\Families\Pages\CreateFamily;
use App\Filament\Resources\Families\Pages\EditFamily;
use App\Filament\Resources\Families\Pages\ListFamilies;
use App\Filament\Resources\Families\Pages\ListFamilyActivities;
use App\Filament\Resources\Families\Pages\ViewFamily;
use App\Filament\Resources\Families\RelationManagers\ResidentsRelationManager;
use App\Filament\Resources\Families\Schemas\FamilyForm;
use App\Filament\Resources\Families\Schemas\FamilyInfolist;
use App\Filament\Resources\Families\Tables\FamiliesTable;
use App\Models\Family;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FamilyResource extends Resource
{
    protected static ?string $model = Family::class;

    protected static string|null|\UnitEnum $navigationGroup = 'Data Warga';

    protected static ?string $navigationLabel =  'Keluarga';
    protected static ?string $modelLabel = 'Keluarga';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::UserGroup;

    public static function getNavigationBadge(): ?string
    {
        $user = auth()->user();

        if (!$user || $user->isSuperAdmin()) {
            return (string) static::getModel()::count();
        }

        $activeSubscription = $user->activeSubscription();
        if (!$activeSubscription) {
            return null;
        }

        return (string) static::getModel()::where('subscriber_id', $activeSubscription->id)->count();
    }
    public static function form(Schema $schema): Schema
    {
        return FamilyForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return FamilyInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return FamiliesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            ResidentsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListFamilies::route('/'),
//            'create' => CreateFamily::route('/create'),
            'view' => ViewFamily::route('/{record}'),
            'activities' => ListFamilyActivities::route('/{record}/activities'),
            'edit' => EditFamily::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['village.district.regency.province'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
