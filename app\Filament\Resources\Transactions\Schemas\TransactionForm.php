<?php

namespace App\Filament\Resources\Transactions\Schemas;

use App\Enums\AccountTypeEnum;
use App\Enums\TransactionTypeEnum;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;

class TransactionForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([


                Select::make('subscriber_id')
                    ->relationship('subscriber', 'name')
                    ->label('Pelanggan')
                    ->native(false)
                    ->required()
                    ->visible(fn () => auth()->user()->isSuperAdmin())
                    ->default(auth()->user()->activeSubscription()->id ?? null),
                DatePicker::make('transaction_date')
                    ->label('Tanggal')
                    ->default(now())
                    ->required(),
                Select::make('account_id')
                    ->relationship('account', 'name')
                    ->label('Akun')
                    ->native(false)
                    ->required(),
                Select::make('transaction_category_id')
                    ->relationship('category', 'name')
                    ->label('Kategori')
                    ->native(false)
                    ->required(),
                Select::make('type')
                    ->label('Tipe')
                    ->options(TransactionTypeEnum::options())
                    ->native(false)
                    ->required(),
                Select::make('destination_account_id')
                    ->label('Tujuan akun')
                    ->relationship('destinationAccount', 'name')
                    ->nullable()
                    ->reactive()
                    ->native(false)
                    ->searchable()
                    ->preload()
                    ->visible(fn (Get $get) => $get('type') === 'transfer'),
                TextInput::make('amount')
                    ->label('Jumlah')
                    ->required()
                    ->placeholder('10000')
                    ->prefix('Rp'),
                TextInput::make('description')
                    ->label('Deskripsi')
                    ->afterLabel('(Opsional)')
                    ->placeholder('Masukkan Deskripsi')
                    ->maxLength(255),
                TextInput::make('reference_number')
                    ->label('Nomor Referensi')
                    ->afterLabel('(Opsional)')
                    ->maxLength(255)
                    ->nullable()
                    ->placeholder('Masukkan Nomor Referensi')
                    ->unique(),
            ]);
    }
}
