<?php

namespace App\Filament\Resources\Subscribers\Schemas;

use App\Enums\SubscriberType;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class SubscriberInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('type')
                    ->icon(fn(string $state): string => SubscriberType::tryFrom($state)?->icon() ?? $state)
                    ->iconColor(fn(string $state): string => SubscriberType::tryFrom($state)?->color() ?? $state)
                    ->formatStateUsing(fn(string $state): string => SubscriberType::tryFrom($state)?->label() ?? $state),
                TextEntry::make('name'),
                TextEntry::make('desc')
                    ->label('Deskripsi'),

                TextEntry::make('address_hierarchy')
                    ->label('Alamat')
                    ->helperText(fn($record) => "Alamat: " . $record->address ?? '-')
                    ->columnSpanFull(),

                TextEntry::make('created_at')
                    ->label('Dibuat')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->label('Diubah')
                    ->dateTime(),
                TextEntry::make('deleted_at')
                    ->label('Dihapus')
                    ->hidden(fn(callable $get) => $get('deleted_at') == null)
                    ->dateTime(),
            ]);
    }
}
