<?php

namespace App\Filament\Resources\TransactionCategories\Schemas;

use App\Enums\TransactionTypeEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class TransactionCategoryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('subscriber_id')
                    ->relationship('subscriber', 'name')
                    ->native(false)
                    ->label('Pelanggan')
                    ->required()
                    ->visible(fn () => auth()->user()->isSuperAdmin())
                    ->default(auth()->user()->activeSubscription()->id ?? null),
                TextInput::make('name')
                    ->label('Nama')
                    ->placeholder('Iuran Warga')
                    ->required()
                    ->maxLength(255),
                Select::make('type')
                    ->label('Tipe')
                    ->native(false)
                    ->options(TransactionTypeEnum::options())
                    ->required(),
                TextInput::make('description')
                    ->label('Deskrip<PERSON>')
                    ->placeholder('Iuran Warga bulan Oktober')
                    ->maxLength(65535),
            ]);
    }
}
