<?php

namespace App\Enums;

enum OccupationEnum: string
{
    case TIDAK_BEKERJA = 'tidak_bekerja';
    case PNS = 'pns';
    case TNI = 'tni';
    case POLRI = 'polri';
    case KARYAWAN_SWASTA = 'karyawan_swasta';
    case WIRASWASTA = 'wiraswasta';
    case PETANI = 'petani';
    case NELAYAN = 'nelayan';
    case PEDAGANG = 'pedagang';
    case GURU = 'guru';
    case DOKTER = 'dokter';
    case BIDAN = 'bidan';
    case PERAWAT = 'perawat';
    case PELAJAR = 'pelajar';
    case MAHASISWA = 'mahasiswa';
    case IBU_RUMAH_TANGGA = 'ibu_rumah_tangga';
    case ASISTEN_RUMAH_TANGGA = 'asisten_rumah_tangga'; // Ditambahkan
    case PENSIONER = 'pensioner';
    case LAINNYA = 'lainnya';

    public function label(): string
    {
        return match ($this) {
            self::TIDAK_BEKERJA => 'Tidak Bekerja',
            self::PNS => 'Pegawai Negeri Sipil',
            self::TNI => 'TNI',
            self::POLRI => 'POLRI',
            self::KARYAWAN_SWASTA => 'Karyawan Swasta',
            self::WIRASWASTA => 'Wiraswasta',
            self::PETANI => 'Petani',
            self::NELAYAN => 'Nelayan',
            self::PEDAGANG => 'Pedagang',
            self::GURU => 'Guru',
            self::DOKTER => 'Dokter',
            self::BIDAN => 'Bidan',
            self::PERAWAT => 'Perawat',
            self::PELAJAR => 'Pelajar',
            self::MAHASISWA => 'Mahasiswa',
            self::IBU_RUMAH_TANGGA => 'Ibu Rumah Tangga',
            self::ASISTEN_RUMAH_TANGGA => 'Asisten Rumah Tangga', // Ditambahkan
            self::PENSIONER => 'Pensiunan',
            self::LAINNYA => 'Lainnya',
        };
    }

    public function category(): string
    {
        return match ($this) {
            self::PNS, self::TNI, self::POLRI => 'Pegawai Pemerintah',
            self::KARYAWAN_SWASTA, self::WIRASWASTA => 'Swasta',
            self::PETANI, self::NELAYAN, self::PEDAGANG => 'Usaha Mandiri',
            self::GURU, self::DOKTER, self::BIDAN, self::PERAWAT => 'Profesional',
            self::PELAJAR, self::MAHASISWA => 'Pendidikan',
            self::IBU_RUMAH_TANGGA, self::ASISTEN_RUMAH_TANGGA, self::PENSIONER, self::TIDAK_BEKERJA => 'Non-Pekerja',
            default => 'Lainnya'
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::PNS => 'heroicon-o-building-office',
            self::TNI => 'heroicon-o-shield-check',
            self::POLRI => 'heroicon-o-shield-exclamation',
            self::KARYAWAN_SWASTA => 'heroicon-o-briefcase',
            self::WIRASWASTA => 'heroicon-o-sparkles',
            self::PETANI => 'heroicon-o-truck',
            self::NELAYAN => 'heroicon-o-boat',
            self::PEDAGANG => 'heroicon-o-shopping-bag',
            self::GURU => 'heroicon-o-academic-cap',
            self::DOKTER => 'heroicon-o-heart',
            self::BIDAN, self::PERAWAT => 'heroicon-o-plus-circle',
            self::PELAJAR, self::MAHASISWA => 'heroicon-o-book-open',
            self::IBU_RUMAH_TANGGA, self::ASISTEN_RUMAH_TANGGA => 'heroicon-o-home', // Ikon sama dengan Ibu Rumah Tangga
            self::PENSIONER => 'heroicon-o-user-circle',
            default => 'heroicon-o-question-mark-circle'
        };
    }

    public static function options(): array
    {
        return array_reduce(self::cases(), function ($options, $case) {
            $options[$case->value] = $case->label();
            return $options;
        }, []);
    }
}
