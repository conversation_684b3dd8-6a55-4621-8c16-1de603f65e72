function ie(n){this.content=n}ie.prototype={constructor:ie,find:function(n){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===n)return e;return-1},get:function(n){var e=this.find(n);return e==-1?void 0:this.content[e+1]},update:function(n,e,t){var r=t&&t!=n?this.remove(t):this,i=r.find(n),o=r.content.slice();return i==-1?o.push(t||n,e):(o[i+1]=e,t&&(o[i]=t)),new ie(o)},remove:function(n){var e=this.find(n);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new ie(t)},addToStart:function(n,e){return new ie([n,e].concat(this.remove(n).content))},addToEnd:function(n,e){var t=this.remove(n).content.slice();return t.push(n,e),new ie(t)},addBefore:function(n,e,t){var r=this.remove(e),i=r.content.slice(),o=r.find(n);return i.splice(o==-1?i.length:o,0,e,t),new ie(i)},forEach:function(n){for(var e=0;e<this.content.length;e+=2)n(this.content[e],this.content[e+1])},prepend:function(n){return n=ie.from(n),n.size?new ie(n.content.concat(this.subtract(n).content)):this},append:function(n){return n=ie.from(n),n.size?new ie(this.subtract(n).content.concat(n.content)):this},subtract:function(n){var e=this;n=ie.from(n);for(var t=0;t<n.content.length;t+=2)e=e.remove(n.content[t]);return e},toObject:function(){var n={};return this.forEach(function(e,t){n[e]=t}),n},get size(){return this.content.length>>1}};ie.from=function(n){if(n instanceof ie)return n;var e=[];if(n)for(var t in n)e.push(t,n[t]);return new ie(e)};var Hi=ie;function gl(n,e,t){for(let r=0;;r++){if(r==n.childCount||r==e.childCount)return n.childCount==e.childCount?null:t;let i=n.child(r),o=e.child(r);if(i==o){t+=i.nodeSize;continue}if(!i.sameMarkup(o))return t;if(i.isText&&i.text!=o.text){for(let s=0;i.text[s]==o.text[s];s++)t++;return t}if(i.content.size||o.content.size){let s=gl(i.content,o.content,t+1);if(s!=null)return s}t+=i.nodeSize}}function yl(n,e,t,r){for(let i=n.childCount,o=e.childCount;;){if(i==0||o==0)return i==o?null:{a:t,b:r};let s=n.child(--i),l=e.child(--o),a=s.nodeSize;if(s==l){t-=a,r-=a;continue}if(!s.sameMarkup(l))return{a:t,b:r};if(s.isText&&s.text!=l.text){let c=0,u=Math.min(s.text.length,l.text.length);for(;c<u&&s.text[s.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,r--;return{a:t,b:r}}if(s.content.size||l.content.size){let c=yl(s.content,l.content,t-1,r-1);if(c)return c}t-=a,r-=a}}var x=class n{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let r=0;r<e.length;r++)this.size+=e[r].nodeSize}nodesBetween(e,t,r,i=0,o){for(let s=0,l=0;l<t;s++){let a=this.content[s],c=l+a.nodeSize;if(c>e&&r(a,i+l,o||null,s)!==!1&&a.content.size){let u=l+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,t-u),r,i+u)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,r,i){let o="",s=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?i?typeof i=="function"?i(l):i:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&r&&(s?s=!1:o+=r),o+=c},0),o}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,r=e.firstChild,i=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(r)&&(i[i.length-1]=t.withText(t.text+r.text),o=1);o<e.content.length;o++)i.push(e.content[o]);return new n(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let r=[],i=0;if(t>e)for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;a>e&&((s<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-s),Math.min(l.text.length,t-s)):l=l.cut(Math.max(0,e-s-1),Math.min(l.content.size,t-s-1))),r.push(l),i+=l.nodeSize),s=a}return new n(r,i)}cutByIndex(e,t){return e==t?n.empty:e==0&&t==this.content.length?this:new n(this.content.slice(e,t))}replaceChild(e,t){let r=this.content[e];if(r==t)return this;let i=this.content.slice(),o=this.size+t.nodeSize-r.nodeSize;return i[e]=t,new n(i,o)}addToStart(e){return new n([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new n(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,r=0;t<this.content.length;t++){let i=this.content[t];e(i,r,t),r+=i.nodeSize}}findDiffStart(e,t=0){return gl(this,e,t)}findDiffEnd(e,t=this.size,r=e.size){return yl(this,e,t,r)}findIndex(e,t=-1){if(e==0)return er(0,e);if(e==this.size)return er(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let r=0,i=0;;r++){let o=this.child(r),s=i+o.nodeSize;if(s>=e)return s==e||t>0?er(r+1,s):er(r,i);i=s}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return n.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new n(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return n.empty;let t,r=0;for(let i=0;i<e.length;i++){let o=e[i];r+=o.nodeSize,i&&o.isText&&e[i-1].sameMarkup(o)?(t||(t=e.slice(0,i)),t[t.length-1]=o.withText(t[t.length-1].text+o.text)):t&&t.push(o)}return new n(t||e,r)}static from(e){if(!e)return n.empty;if(e instanceof n)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new n([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}};x.empty=new x([],0);var $i={index:0,offset:0};function er(n,e){return $i.index=n,$i.offset=e,$i}function nr(n,e){if(n===e)return!0;if(!(n&&typeof n=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(n);if(Array.isArray(e)!=t)return!1;if(t){if(n.length!=e.length)return!1;for(let r=0;r<n.length;r++)if(!nr(n[r],e[r]))return!1}else{for(let r in n)if(!(r in e)||!nr(n[r],e[r]))return!1;for(let r in e)if(!(r in n))return!1}return!0}var H=class n{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,r=!1;for(let i=0;i<e.length;i++){let o=e[i];if(this.eq(o))return e;if(this.type.excludes(o.type))t||(t=e.slice(0,i));else{if(o.type.excludes(this.type))return e;!r&&o.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),r=!0),t&&t.push(o)}}return t||(t=e.slice()),r||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&nr(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let r=e.marks[t.type];if(!r)throw new RangeError(`There is no mark type ${t.type} in this schema`);let i=r.create(t.attrs);return r.checkAttrs(i.attrs),i}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let r=0;r<e.length;r++)if(!e[r].eq(t[r]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return n.none;if(e instanceof n)return[e];let t=e.slice();return t.sort((r,i)=>r.type.rank-i.type.rank),t}};H.none=[];var kt=class extends Error{},w=class n{constructor(e,t,r){this.content=e,this.openStart=t,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let r=xl(this.content,e+this.openStart,t);return r&&new n(r,this.openStart,this.openEnd)}removeBetween(e,t){return new n(bl(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return n.empty;let r=t.openStart||0,i=t.openEnd||0;if(typeof r!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new n(x.fromJSON(e,t.content),r,i)}static maxOpen(e,t=!0){let r=0,i=0;for(let o=e.firstChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.firstChild)r++;for(let o=e.lastChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.lastChild)i++;return new n(e,r,i)}};w.empty=new w(x.empty,0,0);function bl(n,e,t){let{index:r,offset:i}=n.findIndex(e),o=n.maybeChild(r),{index:s,offset:l}=n.findIndex(t);if(i==e||o.isText){if(l!=t&&!n.child(s).isText)throw new RangeError("Removing non-flat range");return n.cut(0,e).append(n.cut(t))}if(r!=s)throw new RangeError("Removing non-flat range");return n.replaceChild(r,o.copy(bl(o.content,e-i-1,t-i-1)))}function xl(n,e,t,r){let{index:i,offset:o}=n.findIndex(e),s=n.maybeChild(i);if(o==e||s.isText)return r&&!r.canReplace(i,i,t)?null:n.cut(0,e).append(t).append(n.cut(e));let l=xl(s.content,e-o-1,t);return l&&n.replaceChild(i,s.copy(l))}function Yd(n,e,t){if(t.openStart>n.depth)throw new kt("Inserted content deeper than insertion position");if(n.depth-t.openStart!=e.depth-t.openEnd)throw new kt("Inconsistent open depths");return kl(n,e,t,0)}function kl(n,e,t,r){let i=n.index(r),o=n.node(r);if(i==e.index(r)&&r<n.depth-t.openStart){let s=kl(n,e,t,r+1);return o.copy(o.content.replaceChild(i,s))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&n.depth==r&&e.depth==r){let s=n.parent,l=s.content;return xt(s,l.cut(0,n.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:s,end:l}=Xd(t,n);return xt(o,wl(n,s,l,e,r))}else return xt(o,rr(n,e,r))}function Sl(n,e){if(!e.type.compatibleContent(n.type))throw new kt("Cannot join "+e.type.name+" onto "+n.type.name)}function ji(n,e,t){let r=n.node(t);return Sl(r,e.node(t)),r}function bt(n,e){let t=e.length-1;t>=0&&n.isText&&n.sameMarkup(e[t])?e[t]=n.withText(e[t].text+n.text):e.push(n)}function un(n,e,t,r){let i=(e||n).node(t),o=0,s=e?e.index(t):i.childCount;n&&(o=n.index(t),n.depth>t?o++:n.textOffset&&(bt(n.nodeAfter,r),o++));for(let l=o;l<s;l++)bt(i.child(l),r);e&&e.depth==t&&e.textOffset&&bt(e.nodeBefore,r)}function xt(n,e){return n.type.checkContent(e),n.copy(e)}function wl(n,e,t,r,i){let o=n.depth>i&&ji(n,e,i+1),s=r.depth>i&&ji(t,r,i+1),l=[];return un(null,n,i,l),o&&s&&e.index(i)==t.index(i)?(Sl(o,s),bt(xt(o,wl(n,e,t,r,i+1)),l)):(o&&bt(xt(o,rr(n,e,i+1)),l),un(e,t,i,l),s&&bt(xt(s,rr(t,r,i+1)),l)),un(r,null,i,l),new x(l)}function rr(n,e,t){let r=[];if(un(null,n,t,r),n.depth>t){let i=ji(n,e,t+1);bt(xt(i,rr(n,e,t+1)),r)}return un(e,null,t,r),new x(r)}function Xd(n,e){let t=e.depth-n.openStart,i=e.node(t).copy(n.content);for(let o=t-1;o>=0;o--)i=e.node(o).copy(x.from(i));return{start:i.resolveNoCache(n.openStart+t),end:i.resolveNoCache(i.content.size-n.openEnd-t)}}var ir=class n{constructor(e,t,r){this.pos=e,this.path=t,this.parentOffset=r,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let r=this.pos-this.path[this.path.length-1],i=e.child(t);return r?e.child(t).cut(r):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let r=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let o=0;o<e;o++)i+=r.child(o).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return H.none;if(this.textOffset)return e.child(t).marks;let r=e.maybeChild(t-1),i=e.maybeChild(t);if(!r){let l=r;r=i,i=l}let o=r.marks;for(var s=0;s<o.length;s++)o[s].type.spec.inclusive===!1&&(!i||!o[s].isInSet(i.marks))&&(o=o[s--].removeFromSet(o));return o}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let r=t.marks,i=e.parent.maybeChild(e.index());for(var o=0;o<r.length;o++)r[o].type.spec.inclusive===!1&&(!i||!r[o].isInSet(i.marks))&&(r=r[o--].removeFromSet(r));return r}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);r>=0;r--)if(e.pos<=this.end(r)&&(!t||t(this.node(r))))return new St(this,e,r);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let r=[],i=0,o=t;for(let s=e;;){let{index:l,offset:a}=s.content.findIndex(o),c=o-a;if(r.push(s,l,i+a),!c||(s=s.child(l),s.isText))break;o=c-1,i+=a+1}return new n(t,r,o)}static resolveCached(e,t){let r=ll.get(e);if(r)for(let o=0;o<r.elts.length;o++){let s=r.elts[o];if(s.pos==t)return s}else ll.set(e,r=new Wi);let i=r.elts[r.i]=n.resolve(e,t);return r.i=(r.i+1)%Qd,i}},Wi=class{constructor(){this.elts=[],this.i=0}},Qd=12,ll=new WeakMap,St=class{constructor(e,t,r){this.$from=e,this.$to=t,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}},Zd=Object.create(null),Q=class n{constructor(e,t,r,i=H.none){this.type=e,this.attrs=t,this.marks=i,this.content=r||x.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,r,i=0){this.content.nodesBetween(e,t,r,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,r,i){return this.content.textBetween(e,t,r,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,r){return this.type==e&&nr(this.attrs,t||e.defaultAttrs||Zd)&&H.sameSet(this.marks,r||H.none)}copy(e=null){return e==this.content?this:new n(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new n(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,r=!1){if(e==t)return w.empty;let i=this.resolve(e),o=this.resolve(t),s=r?0:i.sharedDepth(t),l=i.start(s),c=i.node(s).content.cut(i.pos-l,o.pos-l);return new w(c,i.depth-s,o.depth-s)}replace(e,t,r){return Yd(this.resolve(e),this.resolve(t),r)}nodeAt(e){for(let t=this;;){let{index:r,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(r),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:r}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:r}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:r}=this.content.findIndex(e);if(r<e)return{node:this.content.child(t),index:t,offset:r};let i=this.content.child(t-1);return{node:i,index:t-1,offset:r-i.nodeSize}}resolve(e){return ir.resolveCached(this,e)}resolveNoCache(e){return ir.resolve(this,e)}rangeHasMark(e,t,r){let i=!1;return t>e&&this.nodesBetween(e,t,o=>(r.isInSet(o.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),Ml(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,r=x.empty,i=0,o=r.childCount){let s=this.contentMatchAt(e).matchFragment(r,i,o),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<o;a++)if(!this.type.allowsMarks(r.child(a).marks))return!1;return!0}canReplaceWith(e,t,r,i){if(i&&!this.type.allowsMarks(i))return!1;let o=this.contentMatchAt(e).matchType(r),s=o&&o.matchFragment(this.content,t);return s?s.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=H.none;for(let t=0;t<this.marks.length;t++){let r=this.marks[t];r.type.checkAttrs(r.attrs),e=r.addToSet(e)}if(!H.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let r;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");r=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,r)}let i=x.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,i,r);return o.type.checkAttrs(o.attrs),o}};Q.prototype.text=void 0;var Ki=class n extends Q{constructor(e,t,r,i){if(super(e,t,null,i),!r)throw new RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):Ml(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new n(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new n(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}};function Ml(n,e){for(let t=n.length-1;t>=0;t--)e=n[t].type.name+"("+e+")";return e}var wt=class n{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let r=new _i(e,t);if(r.next==null)return n.empty;let i=Cl(r);r.next&&r.err("Unexpected trailing text");let o=lf(sf(i));return af(o,r),o}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,r=e.childCount){let i=this;for(let o=t;i&&o<r;o++)i=i.matchType(e.child(o).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let r=0;r<e.next.length;r++)if(this.next[t].type==e.next[r].type)return!0;return!1}fillBefore(e,t=!1,r=0){let i=[this];function o(s,l){let a=s.matchFragment(e,r);if(a&&(!t||a.validEnd))return x.from(l.map(c=>c.createAndFill()));for(let c=0;c<s.next.length;c++){let{type:u,next:d}=s.next[c];if(!(u.isText||u.hasRequiredAttrs())&&i.indexOf(d)==-1){i.push(d);let f=o(d,l.concat(u));if(f)return f}}return null}return o(this,[])}findWrapping(e){for(let r=0;r<this.wrapCache.length;r+=2)if(this.wrapCache[r]==e)return this.wrapCache[r+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let i=r.shift(),o=i.match;if(o.matchType(e)){let s=[];for(let l=i;l.type;l=l.via)s.push(l.type);return s.reverse()}for(let s=0;s<o.next.length;s++){let{type:l,next:a}=o.next[s];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(r.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(r){e.push(r);for(let i=0;i<r.next.length;i++)e.indexOf(r.next[i].next)==-1&&t(r.next[i].next)}return t(this),e.map((r,i)=>{let o=i+(r.validEnd?"*":" ")+" ";for(let s=0;s<r.next.length;s++)o+=(s?", ":"")+r.next[s].type.name+"->"+e.indexOf(r.next[s].next);return o}).join(`
`)}};wt.empty=new wt(!0);var _i=class{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}};function Cl(n){let e=[];do e.push(ef(n));while(n.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function ef(n){let e=[];do e.push(tf(n));while(n.next&&n.next!=")"&&n.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function tf(n){let e=of(n);for(;;)if(n.eat("+"))e={type:"plus",expr:e};else if(n.eat("*"))e={type:"star",expr:e};else if(n.eat("?"))e={type:"opt",expr:e};else if(n.eat("{"))e=nf(n,e);else break;return e}function al(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let e=Number(n.next);return n.pos++,e}function nf(n,e){let t=al(n),r=t;return n.eat(",")&&(n.next!="}"?r=al(n):r=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:t,max:r,expr:e}}function rf(n,e){let t=n.nodeTypes,r=t[e];if(r)return[r];let i=[];for(let o in t){let s=t[o];s.isInGroup(e)&&i.push(s)}return i.length==0&&n.err("No node type or group '"+e+"' found"),i}function of(n){if(n.eat("(")){let e=Cl(n);return n.eat(")")||n.err("Missing closing paren"),e}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let e=rf(n,n.next).map(t=>(n.inline==null?n.inline=t.isInline:n.inline!=t.isInline&&n.err("Mixing inline and block content"),{type:"name",value:t}));return n.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function sf(n){let e=[[]];return i(o(n,0),t()),e;function t(){return e.push([])-1}function r(s,l,a){let c={term:a,to:l};return e[s].push(c),c}function i(s,l){s.forEach(a=>a.to=l)}function o(s,l){if(s.type=="choice")return s.exprs.reduce((a,c)=>a.concat(o(c,l)),[]);if(s.type=="seq")for(let a=0;;a++){let c=o(s.exprs[a],l);if(a==s.exprs.length-1)return c;i(c,l=t())}else if(s.type=="star"){let a=t();return r(l,a),i(o(s.expr,a),a),[r(a)]}else if(s.type=="plus"){let a=t();return i(o(s.expr,l),a),i(o(s.expr,a),a),[r(a)]}else{if(s.type=="opt")return[r(l)].concat(o(s.expr,l));if(s.type=="range"){let a=l;for(let c=0;c<s.min;c++){let u=t();i(o(s.expr,a),u),a=u}if(s.max==-1)i(o(s.expr,a),a);else for(let c=s.min;c<s.max;c++){let u=t();r(a,u),i(o(s.expr,a),u),a=u}return[r(a)]}else{if(s.type=="name")return[r(l,void 0,s.value)];throw new Error("Unknown expr type")}}}}function vl(n,e){return e-n}function cl(n,e){let t=[];return r(e),t.sort(vl);function r(i){let o=n[i];if(o.length==1&&!o[0].term)return r(o[0].to);t.push(i);for(let s=0;s<o.length;s++){let{term:l,to:a}=o[s];!l&&t.indexOf(a)==-1&&r(a)}}}function lf(n){let e=Object.create(null);return t(cl(n,0));function t(r){let i=[];r.forEach(s=>{n[s].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let u=0;u<i.length;u++)i[u][0]==l&&(c=i[u][1]);cl(n,a).forEach(u=>{c||i.push([l,c=[]]),c.indexOf(u)==-1&&c.push(u)})})});let o=e[r.join(",")]=new wt(r.indexOf(n.length-1)>-1);for(let s=0;s<i.length;s++){let l=i[s][1].sort(vl);o.next.push({type:i[s][0],next:e[l.join(",")]||t(l)})}return o}}function af(n,e){for(let t=0,r=[n];t<r.length;t++){let i=r[t],o=!i.validEnd,s=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];s.push(a.name),o&&!(a.isText||a.hasRequiredAttrs())&&(o=!1),r.indexOf(c)==-1&&r.push(c)}o&&e.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function Tl(n){let e=Object.create(null);for(let t in n){let r=n[t];if(!r.hasDefault)return null;e[t]=r.default}return e}function El(n,e){let t=Object.create(null);for(let r in n){let i=e&&e[r];if(i===void 0){let o=n[r];if(o.hasDefault)i=o.default;else throw new RangeError("No value supplied for attribute "+r)}t[r]=i}return t}function Al(n,e,t,r){for(let i in e)if(!(i in n))throw new RangeError(`Unsupported attribute ${i} for ${t} of type ${i}`);for(let i in n){let o=n[i];o.validate&&o.validate(e[i])}}function Ol(n,e){let t=Object.create(null);if(e)for(let r in e)t[r]=new Ui(n,r,e[r]);return t}var or=class n{constructor(e,t,r){this.name=e,this.schema=t,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=Ol(e,r.attrs),this.defaultAttrs=Tl(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==wt.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:El(this.attrs,e)}create(e=null,t,r){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Q(this,this.computeAttrs(e),x.from(t),H.setFrom(r))}createChecked(e=null,t,r){return t=x.from(t),this.checkContent(t),new Q(this,this.computeAttrs(e),t,H.setFrom(r))}createAndFill(e=null,t,r){if(e=this.computeAttrs(e),t=x.from(t),t.size){let s=this.contentMatch.fillBefore(t);if(!s)return null;t=s.append(t)}let i=this.contentMatch.matchFragment(t),o=i&&i.fillBefore(x.empty,!0);return o?new Q(this,e,t.append(o),H.setFrom(r)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let r=0;r<e.childCount;r++)if(!this.allowsMarks(e.child(r).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){Al(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let r=0;r<e.length;r++)this.allowsMarkType(e[r].type)?t&&t.push(e[r]):t||(t=e.slice(0,r));return t?t.length?t:H.none:e}static compile(e,t){let r=Object.create(null);e.forEach((o,s)=>r[o]=new n(o,t,s));let i=t.spec.topNode||"doc";if(!r[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let o in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};function cf(n,e,t){let r=t.split("|");return i=>{let o=i===null?"null":typeof i;if(r.indexOf(o)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${n}, got ${o}`)}}var Ui=class{constructor(e,t,r){this.hasDefault=Object.prototype.hasOwnProperty.call(r,"default"),this.default=r.default,this.validate=typeof r.validate=="string"?cf(e,t,r.validate):r.validate}get isRequired(){return!this.hasDefault}},fn=class n{constructor(e,t,r,i){this.name=e,this.rank=t,this.schema=r,this.spec=i,this.attrs=Ol(e,i.attrs),this.excluded=null;let o=Tl(this.attrs);this.instance=o?new H(this,o):null}create(e=null){return!e&&this.instance?this.instance:new H(this,El(this.attrs,e))}static compile(e,t){let r=Object.create(null),i=0;return e.forEach((o,s)=>r[o]=new n(o,i++,t,s)),r}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){Al(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}},Wt=class{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let i in e)t[i]=e[i];t.nodes=Hi.from(e.nodes),t.marks=Hi.from(e.marks||{}),this.nodes=or.compile(this.spec.nodes,this),this.marks=fn.compile(this.spec.marks,this);let r=Object.create(null);for(let i in this.nodes){if(i in this.marks)throw new RangeError(i+" can not be both a node and a mark");let o=this.nodes[i],s=o.spec.content||"",l=o.spec.marks;if(o.contentMatch=r[s]||(r[s]=wt.parse(s,this.nodes)),o.inlineContent=o.contentMatch.inlineContent,o.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!o.isInline||!o.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=o}o.markSet=l=="_"?null:l?ul(this,l.split(" ")):l==""||!o.inlineContent?[]:null}for(let i in this.marks){let o=this.marks[i],s=o.spec.excludes;o.excluded=s==null?[o]:s==""?[]:ul(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,r,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof or){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,r,i)}text(e,t){let r=this.nodes.text;return new Ki(r,r.defaultAttrs,e,H.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Q.fromJSON(this,e)}markFromJSON(e){return H.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}};function ul(n,e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],o=n.marks[i],s=o;if(o)t.push(o);else for(let l in n.marks){let a=n.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(s=a)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return t}function uf(n){return n.tag!=null}function df(n){return n.style!=null}var Le=class n{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let r=this.matchedStyles=[];t.forEach(i=>{if(uf(i))this.tags.push(i);else if(df(i)){let o=/[^=]*/.exec(i.style)[0];r.indexOf(o)<0&&r.push(o),this.styles.push(i)}}),this.normalizeLists=!this.tags.some(i=>{if(!/^(ul|ol)\b/.test(i.tag)||!i.node)return!1;let o=e.nodes[i.node];return o.contentMatch.matchType(o)})}parse(e,t={}){let r=new sr(this,t,!1);return r.addAll(e,H.none,t.from,t.to),r.finish()}parseSlice(e,t={}){let r=new sr(this,t,!0);return r.addAll(e,H.none,t.from,t.to),w.maxOpen(r.finish())}matchTag(e,t,r){for(let i=r?this.tags.indexOf(r)+1:0;i<this.tags.length;i++){let o=this.tags[i];if(pf(e,o.tag)&&(o.namespace===void 0||e.namespaceURI==o.namespace)&&(!o.context||t.matchesContext(o.context))){if(o.getAttrs){let s=o.getAttrs(e);if(s===!1)continue;o.attrs=s||void 0}return o}}}matchStyle(e,t,r,i){for(let o=i?this.styles.indexOf(i)+1:0;o<this.styles.length;o++){let s=this.styles[o],l=s.style;if(!(l.indexOf(e)!=0||s.context&&!r.matchesContext(s.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(s.getAttrs){let a=s.getAttrs(t);if(a===!1)continue;s.attrs=a||void 0}return s}}}static schemaRules(e){let t=[];function r(i){let o=i.priority==null?50:i.priority,s=0;for(;s<t.length;s++){let l=t[s];if((l.priority==null?50:l.priority)<o)break}t.splice(s,0,i)}for(let i in e.marks){let o=e.marks[i].spec.parseDOM;o&&o.forEach(s=>{r(s=fl(s)),s.mark||s.ignore||s.clearMark||(s.mark=i)})}for(let i in e.nodes){let o=e.nodes[i].spec.parseDOM;o&&o.forEach(s=>{r(s=fl(s)),s.node||s.ignore||s.mark||(s.node=i)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new n(e,n.schemaRules(e)))}},Nl={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},ff={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Dl={ol:!0,ul:!0},hn=1,Ji=2,dn=4;function dl(n,e,t){return e!=null?(e?hn:0)|(e==="full"?Ji:0):n&&n.whitespace=="pre"?hn|Ji:t&~dn}var jt=class{constructor(e,t,r,i,o,s){this.type=e,this.attrs=t,this.marks=r,this.solid=i,this.options=s,this.content=[],this.activeMarks=H.none,this.match=o||(s&dn?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(x.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let r=this.type.contentMatch,i;return(i=r.findWrapping(e.type))?(this.match=r,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&hn)){let r=this.content[this.content.length-1],i;if(r&&r.isText&&(i=/[ \t\r\n\u000c]+$/.exec(r.text))){let o=r;r.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=o.withText(o.text.slice(0,o.text.length-i[0].length))}}let t=x.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(x.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Nl.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}},sr=class{constructor(e,t,r){this.parser=e,this.options=t,this.isOpen=r,this.open=0,this.localPreserveWS=!1;let i=t.topNode,o,s=dl(null,t.preserveWhitespace,0)|(r?dn:0);i?o=new jt(i.type,i.attrs,H.none,!0,t.topMatch||i.type.contentMatch,s):r?o=new jt(null,null,H.none,!0,null,s):o=new jt(e.schema.topNodeType,null,H.none,!0,null,s),this.nodes=[o],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let r=e.nodeValue,i=this.top,o=i.options&Ji?"full":this.localPreserveWS||(i.options&hn)>0;if(o==="full"||i.inlineContext(e)||/[^ \t\r\n\u000c]/.test(r)){if(o)o!=="full"?r=r.replace(/\r?\n|\r/g," "):r=r.replace(/\r\n?/g,`
`);else if(r=r.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(r)&&this.open==this.nodes.length-1){let s=i.content[i.content.length-1],l=e.previousSibling;(!s||l&&l.nodeName=="BR"||s.isText&&/[ \t\r\n\u000c]$/.test(s.text))&&(r=r.slice(1))}r&&this.insertNode(this.parser.schema.text(r),t,!/\S/.test(r)),this.findInText(e)}else this.findInside(e)}addElement(e,t,r){let i=this.localPreserveWS,o=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let s=e.nodeName.toLowerCase(),l;Dl.hasOwnProperty(s)&&this.parser.normalizeLists&&hf(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,r));e:if(a?a.ignore:ff.hasOwnProperty(s))this.findInside(e),this.ignoreFallback(e,t);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,u=this.needsBlock;if(Nl.hasOwnProperty(s))o.content.length&&o.content[0].isInline&&this.open&&(this.open--,o=this.top),c=!0,o.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let d=a&&a.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),c&&this.sync(o),this.needsBlock=u}else{let c=this.readStyles(e,t);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=i}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let r=e.style;if(r&&r.length)for(let i=0;i<this.parser.matchedStyles.length;i++){let o=this.parser.matchedStyles[i],s=r.getPropertyValue(o);if(s)for(let l=void 0;;){let a=this.parser.matchStyle(o,s,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?t=t.filter(c=>!a.clearMark(c)):t=t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return t}addElementByRule(e,t,r,i){let o,s;if(t.node)if(s=this.parser.schema.nodes[t.node],s.isLeaf)this.insertNode(s.create(t.attrs),r,e.nodeName=="BR")||this.leafFallback(e,r);else{let a=this.enter(s,t.attrs||null,r,t.preserveWhitespace);a&&(o=!0,r=a)}else{let a=this.parser.schema.marks[t.mark];r=r.concat(a.create(t.attrs))}let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(i)this.addElement(e,r,i);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,r,!1));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,r),this.findAround(e,a,!1)}o&&this.sync(l)&&this.open--}addAll(e,t,r,i){let o=r||0;for(let s=r?e.childNodes[r]:e.firstChild,l=i==null?null:e.childNodes[i];s!=l;s=s.nextSibling,++o)this.findAtPoint(e,o),this.addDOM(s,t);this.findAtPoint(e,o)}findPlace(e,t,r){let i,o;for(let s=this.open,l=0;s>=0;s--){let a=this.nodes[s],c=a.findWrapping(e);if(c&&(!i||i.length>c.length+l)&&(i=c,o=a,!c.length))break;if(a.solid){if(r)break;l+=2}}if(!i)return null;this.sync(o);for(let s=0;s<i.length;s++)t=this.enterInner(i[s],null,t,!1);return t}insertNode(e,t,r){if(e.isInline&&this.needsBlock&&!this.top.type){let o=this.textblockFromContext();o&&(t=this.enterInner(o,null,t))}let i=this.findPlace(e,t,r);if(i){this.closeExtra();let o=this.top;o.match&&(o.match=o.match.matchType(e.type));let s=H.none;for(let l of i.concat(e.marks))(o.type?o.type.allowsMarkType(l.type):hl(l.type,e.type))&&(s=l.addToSet(s));return o.content.push(e.mark(s)),!0}return!1}enter(e,t,r,i){let o=this.findPlace(e.create(t),r,!1);return o&&(o=this.enterInner(e,t,r,!0,i)),o}enterInner(e,t,r,i=!1,o){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let l=dl(e,o,s.options);s.options&dn&&s.content.length==0&&(l|=dn);let a=H.none;return r=r.filter(c=>(s.type?s.type.allowsMarkType(c.type):hl(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new jt(e,t,a,i,null,l)),this.open++,r}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=hn)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let r=this.nodes[t].content;for(let i=r.length-1;i>=0;i--)e+=r[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==e&&this.find[r].offset==t&&(this.find[r].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,r){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(r?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),r=this.options.context,i=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),o=-(r?r.depth+1:0)+(i?0:1),s=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=o;a--)if(s(l-1,a))return!0;return!1}else{let u=a>0||a==0&&i?this.nodes[a].type:r&&a>=o?r.node(a-o).type:null;if(!u||u.name!=c&&!u.isInGroup(c))return!1;a--}}return!0};return s(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let r=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let t in this.parser.schema.nodes){let r=this.parser.schema.nodes[t];if(r.isTextblock&&r.defaultAttrs)return r}}};function hf(n){for(let e=n.firstChild,t=null;e;e=e.nextSibling){let r=e.nodeType==1?e.nodeName.toLowerCase():null;r&&Dl.hasOwnProperty(r)&&t?(t.appendChild(e),e=t):r=="li"?t=e:r&&(t=null)}}function pf(n,e){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,e)}function fl(n){let e={};for(let t in n)e[t]=n[t];return e}function hl(n,e){let t=e.schema.nodes;for(let r in t){let i=t[r];if(!i.allowsMarkType(n))continue;let o=[],s=l=>{o.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:u}=l.edge(a);if(c==e||o.indexOf(u)<0&&s(u))return!0}};if(s(i.contentMatch))return!0}}var _e=class n{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},r){r||(r=Vi(t).createDocumentFragment());let i=r,o=[];return e.forEach(s=>{if(o.length||s.marks.length){let l=0,a=0;for(;l<o.length&&a<s.marks.length;){let c=s.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(o[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<o.length;)i=o.pop()[1];for(;a<s.marks.length;){let c=s.marks[a++],u=this.serializeMark(c,s.isInline,t);u&&(o.push([c,i]),i.appendChild(u.dom),i=u.contentDOM||u.dom)}}i.appendChild(this.serializeNodeInner(s,t))}),r}serializeNodeInner(e,t){let{dom:r,contentDOM:i}=tr(Vi(t),this.nodes[e.type.name](e),null,e.attrs);if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return r}serializeNode(e,t={}){let r=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let o=this.serializeMark(e.marks[i],e.isInline,t);o&&((o.contentDOM||o.dom).appendChild(r),r=o.dom)}return r}serializeMark(e,t,r={}){let i=this.marks[e.type.name];return i&&tr(Vi(r),i(e,t),null,e.attrs)}static renderSpec(e,t,r=null,i){return tr(e,t,r,i)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new n(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=pl(e.nodes);return t.text||(t.text=r=>r.text),t}static marksFromSchema(e){return pl(e.marks)}};function pl(n){let e={};for(let t in n){let r=n[t].spec.toDOM;r&&(e[t]=r)}return e}function Vi(n){return n.document||window.document}var ml=new WeakMap;function mf(n){let e=ml.get(n);return e===void 0&&ml.set(n,e=gf(n)),e}function gf(n){let e=null;function t(r){if(r&&typeof r=="object")if(Array.isArray(r))if(typeof r[0]=="string")e||(e=[]),e.push(r);else for(let i=0;i<r.length;i++)t(r[i]);else for(let i in r)t(r[i])}return t(n),e}function tr(n,e,t,r){if(typeof e=="string")return{dom:n.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let i=e[0],o;if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(r&&(o=mf(r))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s=i.indexOf(" ");s>0&&(t=i.slice(0,s),i=i.slice(s+1));let l,a=t?n.createElementNS(t,i):n.createElement(i),c=e[1],u=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){u=2;for(let d in c)if(c[d]!=null){let f=d.indexOf(" ");f>0?a.setAttributeNS(d.slice(0,f),d.slice(f+1),c[d]):a.setAttribute(d,c[d])}}for(let d=u;d<e.length;d++){let f=e[d];if(f===0){if(d<e.length-1||d>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:h,contentDOM:p}=tr(n,f,t,r);if(a.appendChild(h),p){if(l)throw new RangeError("Multiple content holes");l=p}}}return{dom:a,contentDOM:l}}var Il=65535,Ll=Math.pow(2,16);function yf(n,e){return n+e*Ll}function Rl(n){return n&Il}function bf(n){return(n-(n&Il))/Ll}var Bl=1,Fl=2,lr=4,zl=8,gn=class{constructor(e,t,r){this.pos=e,this.delInfo=t,this.recover=r}get deleted(){return(this.delInfo&zl)>0}get deletedBefore(){return(this.delInfo&(Bl|lr))>0}get deletedAfter(){return(this.delInfo&(Fl|lr))>0}get deletedAcross(){return(this.delInfo&lr)>0}},Ue=class n{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&n.empty)return n.empty}recover(e){let t=0,r=Rl(e);if(!this.inverted)for(let i=0;i<r;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[r*3]+t+bf(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,r){let i=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+o],u=this.ranges[l+s],d=a+c;if(e<=d){let f=c?e==a?-1:e==d?1:t:t,h=a+i+(f<0?0:u);if(r)return h;let p=e==(t<0?a:d)?null:yf(l/3,e-a),m=e==a?Fl:e==d?Bl:lr;return(t<0?e!=a:e!=d)&&(m|=zl),new gn(h,m,p)}i+=u-c}return r?e+i:new gn(e+i,0,null)}touches(e,t){let r=0,i=Rl(t),o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+o],u=a+c;if(e<=u&&l==i*3)return!0;r+=this.ranges[l+s]-c}return!1}forEach(e){let t=this.inverted?2:1,r=this.inverted?1:2;for(let i=0,o=0;i<this.ranges.length;i+=3){let s=this.ranges[i],l=s-(this.inverted?o:0),a=s+(this.inverted?0:o),c=this.ranges[i+t],u=this.ranges[i+r];e(l,l+c,a,a+u),o+=u-c}}invert(){return new n(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?n.empty:new n(e<0?[0,-e,0]:[0,0,e])}};Ue.empty=new Ue([]);var yn=class n{constructor(e,t,r=0,i=e?e.length:0){this.mirror=t,this.from=r,this.to=i,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new n(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),t!=null&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,r=this._maps.length;t<e._maps.length;t++){let i=e.getMirror(t);this.appendMap(e._maps[t],i!=null&&i<t?r+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,r=this._maps.length+e._maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e._maps[t].invert(),i!=null&&i>t?r-i-1:void 0)}}invert(){let e=new n;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let r=this.from;r<this.to;r++)e=this._maps[r].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,r){let i=0;for(let o=this.from;o<this.to;o++){let s=this._maps[o],l=s.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(o);if(a!=null&&a>o&&a<this.to){o=a,e=this._maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return r?e:new gn(e,i,null)}},qi=Object.create(null),Z=class{getMap(){return Ue.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let r=qi[t.stepType];if(!r)throw new RangeError(`No step type ${t.stepType} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in qi)throw new RangeError("Duplicate use of step JSON ID "+e);return qi[e]=t,t.prototype.jsonID=e,t}},te=class n{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new n(e,null)}static fail(e){return new n(null,e)}static fromReplace(e,t,r,i){try{return n.ok(e.replace(t,r,i))}catch(o){if(o instanceof kt)return n.fail(o.message);throw o}}};function Zi(n,e,t){let r=[];for(let i=0;i<n.childCount;i++){let o=n.child(i);o.content.size&&(o=o.copy(Zi(o.content,e,o))),o.isInline&&(o=e(o,t,i)),r.push(o)}return x.fromArray(r)}var bn=class n extends Z{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=e.resolve(this.from),i=r.node(r.sharedDepth(this.to)),o=new w(Zi(t.content,(s,l)=>!s.isAtom||!l.type.allowsMarkType(this.mark.type)?s:s.mark(this.mark.addToSet(s.marks)),i),t.openStart,t.openEnd);return te.fromReplace(e,this.from,this.to,o)}invert(){return new Je(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new n(t.pos,r.pos,this.mark)}merge(e){return e instanceof n&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new n(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new n(t.from,t.to,e.markFromJSON(t.mark))}};Z.jsonID("addMark",bn);var Je=class n extends Z{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=new w(Zi(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return te.fromReplace(e,this.from,this.to,r)}invert(){return new bn(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new n(t.pos,r.pos,this.mark)}merge(e){return e instanceof n&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new n(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new n(t.from,t.to,e.markFromJSON(t.mark))}};Z.jsonID("removeMark",Je);var xn=class n extends Z{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return te.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return te.fromReplace(e,this.pos,this.pos+1,new w(x.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let r=this.mark.addToSet(t.marks);if(r.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(r))return new n(this.pos,t.marks[i]);return new n(this.pos,this.mark)}}return new Kt(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new n(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new n(t.pos,e.markFromJSON(t.mark))}};Z.jsonID("addNodeMark",xn);var Kt=class n extends Z{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return te.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return te.fromReplace(e,this.pos,this.pos+1,new w(x.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new xn(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new n(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new n(t.pos,e.markFromJSON(t.mark))}};Z.jsonID("removeNodeMark",Kt);var oe=class n extends Z{constructor(e,t,r,i=!1){super(),this.from=e,this.to=t,this.slice=r,this.structure=i}apply(e){return this.structure&&Xi(e,this.from,this.to)?te.fail("Structure replace would overwrite content"):te.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new Ue([this.from,this.to-this.from,this.slice.size])}invert(e){return new n(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deletedAcross&&r.deletedAcross?null:new n(t.pos,Math.max(t.pos,r.pos),this.slice,this.structure)}merge(e){if(!(e instanceof n)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?w.empty:new w(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new n(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?w.empty:new w(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new n(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new n(t.from,t.to,w.fromJSON(e,t.slice),!!t.structure)}};Z.jsonID("replace",oe);var q=class n extends Z{constructor(e,t,r,i,o,s,l=!1){super(),this.from=e,this.to=t,this.gapFrom=r,this.gapTo=i,this.slice=o,this.insert=s,this.structure=l}apply(e){if(this.structure&&(Xi(e,this.from,this.gapFrom)||Xi(e,this.gapTo,this.to)))return te.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return te.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,t.content);return r?te.fromReplace(e,this.from,this.to,r):te.fail("Content does not fit in gap")}getMap(){return new Ue([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new n(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),o=this.to==this.gapTo?r.pos:e.map(this.gapTo,1);return t.deletedAcross&&r.deletedAcross||i<t.pos||o>r.pos?null:new n(t.pos,r.pos,i,o,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new n(t.from,t.to,t.gapFrom,t.gapTo,w.fromJSON(e,t.slice),t.insert,!!t.structure)}};Z.jsonID("replaceAround",q);function Xi(n,e,t){let r=n.resolve(e),i=t-e,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let s=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,i--}}return!1}function xf(n,e,t,r){let i=[],o=[],s,l;n.doc.nodesBetween(e,t,(a,c,u)=>{if(!a.isInline)return;let d=a.marks;if(!r.isInSet(d)&&u.type.allowsMarkType(r.type)){let f=Math.max(c,e),h=Math.min(c+a.nodeSize,t),p=r.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(s&&s.to==f&&s.mark.eq(d[m])?s.to=h:i.push(s=new Je(f,h,d[m])));l&&l.to==f?l.to=h:o.push(l=new bn(f,h,r))}}),i.forEach(a=>n.step(a)),o.forEach(a=>n.step(a))}function kf(n,e,t,r){let i=[],o=0;n.doc.nodesBetween(e,t,(s,l)=>{if(!s.isInline)return;o++;let a=null;if(r instanceof fn){let c=s.marks,u;for(;u=r.isInSet(c);)(a||(a=[])).push(u),c=u.removeFromSet(c)}else r?r.isInSet(s.marks)&&(a=[r]):a=s.marks;if(a&&a.length){let c=Math.min(l+s.nodeSize,t);for(let u=0;u<a.length;u++){let d=a[u],f;for(let h=0;h<i.length;h++){let p=i[h];p.step==o-1&&d.eq(i[h].style)&&(f=p)}f?(f.to=c,f.step=o):i.push({style:d,from:Math.max(l,e),to:c,step:o})}}}),i.forEach(s=>n.step(new Je(s.from,s.to,s.style)))}function eo(n,e,t,r=t.contentMatch,i=!0){let o=n.doc.nodeAt(e),s=[],l=e+1;for(let a=0;a<o.childCount;a++){let c=o.child(a),u=l+c.nodeSize,d=r.matchType(c.type);if(!d)s.push(new oe(l,u,w.empty));else{r=d;for(let f=0;f<c.marks.length;f++)t.allowsMarkType(c.marks[f].type)||n.step(new Je(l,u,c.marks[f]));if(i&&c.isText&&t.whitespace!="pre"){let f,h=/\r?\n|\r/g,p;for(;f=h.exec(c.text);)p||(p=new w(x.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),s.push(new oe(l+f.index,l+f.index+f[0].length,p))}}l=u}if(!r.validEnd){let a=r.fillBefore(x.empty,!0);n.replace(l,l,new w(a,0,0))}for(let a=s.length-1;a>=0;a--)n.step(s[a])}function Sf(n,e,t){return(e==0||n.canReplace(e,n.childCount))&&(t==n.childCount||n.canReplace(0,t))}function qe(n){let t=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let r=n.depth;;--r){let i=n.$from.node(r),o=n.$from.index(r),s=n.$to.indexAfter(r);if(r<n.depth&&i.canReplace(o,s,t))return r;if(r==0||i.type.spec.isolating||!Sf(i,o,s))break}return null}function wf(n,e,t){let{$from:r,$to:i,depth:o}=e,s=r.before(o+1),l=i.after(o+1),a=s,c=l,u=x.empty,d=0;for(let p=o,m=!1;p>t;p--)m||r.index(p)>0?(m=!0,u=x.from(r.node(p).copy(u)),d++):a--;let f=x.empty,h=0;for(let p=o,m=!1;p>t;p--)m||i.after(p+1)<i.end(p)?(m=!0,f=x.from(i.node(p).copy(f)),h++):c++;n.step(new q(a,c,s,l,new w(u.append(f),d,h),u.size-d,!0))}function Jt(n,e,t=null,r=n){let i=Mf(n,e),o=i&&Cf(r,e);return o?i.map(Pl).concat({type:e,attrs:t}).concat(o.map(Pl)):null}function Pl(n){return{type:n,attrs:null}}function Mf(n,e){let{parent:t,startIndex:r,endIndex:i}=n,o=t.contentMatchAt(r).findWrapping(e);if(!o)return null;let s=o.length?o[0]:e;return t.canReplaceWith(r,i,s)?o:null}function Cf(n,e){let{parent:t,startIndex:r,endIndex:i}=n,o=t.child(r),s=e.contentMatch.findWrapping(o.type);if(!s)return null;let a=(s.length?s[s.length-1]:e).contentMatch;for(let c=r;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:s}function vf(n,e,t){let r=x.empty;for(let s=t.length-1;s>=0;s--){if(r.size){let l=t[s].type.contentMatch.matchFragment(r);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=x.from(t[s].type.create(t[s].attrs,r))}let i=e.start,o=e.end;n.step(new q(i,o,i,o,new w(r,0,0),t.length,!0))}function Tf(n,e,t,r,i){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let o=n.steps.length;n.doc.nodesBetween(e,t,(s,l)=>{let a=typeof i=="function"?i(s):i;if(s.isTextblock&&!s.hasMarkup(r,a)&&Ef(n.doc,n.mapping.slice(o).map(l),r)){let c=null;if(r.schema.linebreakReplacement){let h=r.whitespace=="pre",p=!!r.contentMatch.matchType(r.schema.linebreakReplacement);h&&!p?c=!1:!h&&p&&(c=!0)}c===!1&&$l(n,s,l,o),eo(n,n.mapping.slice(o).map(l,1),r,void 0,c===null);let u=n.mapping.slice(o),d=u.map(l,1),f=u.map(l+s.nodeSize,1);return n.step(new q(d,f,d+1,f-1,new w(x.from(r.create(a,null,s.marks)),0,0),1,!0)),c===!0&&Hl(n,s,l,o),!1}})}function Hl(n,e,t,r){e.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let a=n.mapping.slice(r).map(t+1+o+s.index);n.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function $l(n,e,t,r){e.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let s=n.mapping.slice(r).map(t+1+o);n.replaceWith(s,s+1,e.type.schema.text(`
`))}})}function Ef(n,e,t){let r=n.resolve(e),i=r.index();return r.parent.canReplaceWith(i,i+1,t)}function Af(n,e,t,r,i){let o=n.doc.nodeAt(e);if(!o)throw new RangeError("No node at given position");t||(t=o.type);let s=t.create(r,null,i||o.marks);if(o.isLeaf)return n.replaceWith(e,e+o.nodeSize,s);if(!t.validContent(o.content))throw new RangeError("Invalid content for node type "+t.name);n.step(new q(e,e+o.nodeSize,e+1,e+o.nodeSize-1,new w(x.from(s),0,0),1,!0))}function ke(n,e,t=1,r){let i=n.resolve(e),o=i.depth-t,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,u=t-2;c>o;c--,u--){let d=i.node(c),f=i.index(c);if(d.type.spec.isolating)return!1;let h=d.content.cutByIndex(f,d.childCount),p=r&&r[u+1];p&&(h=h.replaceChild(0,p.type.create(p.attrs)));let m=r&&r[u]||d;if(!d.canReplace(f+1,d.childCount)||!m.type.validContent(h))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function Of(n,e,t=1,r){let i=n.doc.resolve(e),o=x.empty,s=x.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){o=x.from(i.node(l).copy(o));let u=r&&r[c];s=x.from(u?u.type.create(u.attrs,s):i.node(l).copy(s))}n.step(new oe(e,e,new w(o.append(s),t,t),!0))}function Me(n,e){let t=n.resolve(e),r=t.index();return Vl(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(r,r+1)}function Nf(n,e){e.content.size||n.type.compatibleContent(e.type);let t=n.contentMatchAt(n.childCount),{linebreakReplacement:r}=n.type.schema;for(let i=0;i<e.childCount;i++){let o=e.child(i),s=o.type==r?n.type.schema.nodes.text:o.type;if(t=t.matchType(s),!t||!n.type.allowsMarks(o.marks))return!1}return t.validEnd}function Vl(n,e){return!!(n&&e&&!n.isLeaf&&Nf(n,e))}function Mt(n,e,t=-1){let r=n.resolve(e);for(let i=r.depth;;i--){let o,s,l=r.index(i);if(i==r.depth?(o=r.nodeBefore,s=r.nodeAfter):t>0?(o=r.node(i+1),l++,s=r.node(i).maybeChild(l)):(o=r.node(i).maybeChild(l-1),s=r.node(i+1)),o&&!o.isTextblock&&Vl(o,s)&&r.node(i).canReplace(l,l+1))return e;if(i==0)break;e=t<0?r.before(i):r.after(i)}}function Df(n,e,t){let r=null,{linebreakReplacement:i}=n.doc.type.schema,o=n.doc.resolve(e-t),s=o.node().type;if(i&&s.inlineContent){let u=s.whitespace=="pre",d=!!s.contentMatch.matchType(i);u&&!d?r=!1:!u&&d&&(r=!0)}let l=n.steps.length;if(r===!1){let u=n.doc.resolve(e+t);$l(n,u.node(),u.before(),l)}s.inlineContent&&eo(n,e+t-1,s,o.node().contentMatchAt(o.index()),r==null);let a=n.mapping.slice(l),c=a.map(e-t);if(n.step(new oe(c,a.map(e+t,-1),w.empty,!0)),r===!0){let u=n.doc.resolve(c);Hl(n,u.node(),u.before(),n.steps.length)}return n}function Rf(n,e,t){let r=n.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),t))return e;if(r.parentOffset==0)for(let i=r.depth-1;i>=0;i--){let o=r.index(i);if(r.node(i).canReplaceWith(o,o,t))return r.before(i+1);if(o>0)return null}if(r.parentOffset==r.parent.content.size)for(let i=r.depth-1;i>=0;i--){let o=r.indexAfter(i);if(r.node(i).canReplaceWith(o,o,t))return r.after(i+1);if(o<r.node(i).childCount)return null}return null}function ur(n,e,t){let r=n.resolve(e);if(!t.content.size)return e;let i=t.content;for(let o=0;o<t.openStart;o++)i=i.firstChild.content;for(let o=1;o<=(t.openStart==0&&t.size?2:1);o++)for(let s=r.depth;s>=0;s--){let l=s==r.depth?0:r.pos<=(r.start(s+1)+r.end(s+1))/2?-1:1,a=r.index(s)+(l>0?1:0),c=r.node(s),u=!1;if(o==1)u=c.canReplace(a,a,i);else{let d=c.contentMatchAt(a).findWrapping(i.firstChild.type);u=d&&c.canReplaceWith(a,a,d[0])}if(u)return l==0?r.pos:l<0?r.before(s+1):r.after(s+1)}return null}function kn(n,e,t=e,r=w.empty){if(e==t&&!r.size)return null;let i=n.resolve(e),o=n.resolve(t);return jl(i,o,r)?new oe(e,t,r):new Qi(i,o,r).fit()}function jl(n,e,t){return!t.openStart&&!t.openEnd&&n.start()==e.start()&&n.parent.canReplace(n.index(),e.index(),t.content)}var Qi=class{constructor(e,t,r){this.$from=e,this.$to=t,this.unplaced=r,this.frontier=[],this.placed=x.empty;for(let i=0;i<=e.depth;i++){let o=e.node(i);this.frontier.push({type:o.type,match:o.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=x.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,r=this.$from,i=this.close(e<0?this.$to:r.doc.resolve(e));if(!i)return null;let o=this.placed,s=r.depth,l=i.depth;for(;s&&l&&o.childCount==1;)o=o.firstChild.content,s--,l--;let a=new w(o,s,l);return e>-1?new q(r.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||r.pos!=this.$to.pos?new oe(r.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,r=0,i=this.unplaced.openEnd;r<e;r++){let o=t.firstChild;if(t.childCount>1&&(i=0),o.type.spec.isolating&&i<=r){e=r;break}t=o.content}for(let t=1;t<=2;t++)for(let r=t==1?e:this.unplaced.openStart;r>=0;r--){let i,o=null;r?(o=Gi(this.unplaced.content,r-1).firstChild,i=o.content):i=this.unplaced.content;let s=i.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],u,d=null;if(t==1&&(s?c.matchType(s.type)||(d=c.fillBefore(x.from(s),!1)):o&&a.compatibleContent(o.type)))return{sliceDepth:r,frontierDepth:l,parent:o,inject:d};if(t==2&&s&&(u=c.findWrapping(s.type)))return{sliceDepth:r,frontierDepth:l,parent:o,wrap:u};if(o&&c.matchType(o.type))break}}}openMore(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=Gi(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new w(e,t+1,Math.max(r,i.size+t>=e.size-r?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=Gi(e,t);if(i.childCount<=1&&t>0){let o=e.size-t<=t+i.size;this.unplaced=new w(pn(e,t-1,1),t-1,o?t-1:r)}else this.unplaced=new w(pn(e,t,1),t,r)}placeNodes({sliceDepth:e,frontierDepth:t,parent:r,inject:i,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let m=0;m<o.length;m++)this.openFrontierNode(o[m]);let s=this.unplaced,l=r?r.content:s.content,a=s.openStart-e,c=0,u=[],{match:d,type:f}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)u.push(i.child(m));d=d.matchFragment(i)}let h=l.size+e-(s.content.size-s.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,u.push(Wl(m.mark(f.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=mn(this.placed,t,x.from(u)),this.frontier[t].match=d,p&&h<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?e==0?w.empty:new w(pn(s.content,e-1,1),e-1,h<0?s.openEnd:e-1):new w(pn(s.content,e,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!Yi(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:r}=this.$to,i=this.$to.after(r);for(;r>1&&i==this.$to.end(--r);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:r,type:i}=this.frontier[t],o=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),s=Yi(e,t,i,r,o);if(s){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],u=Yi(e,l,c,a,!0);if(!u||u.childCount)continue e}return{depth:t,fit:s,move:o?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=mn(this.placed,t.depth,t.fit)),e=t.move;for(let r=t.depth+1;r<=e.depth;r++){let i=e.node(r),o=i.type.contentMatch.fillBefore(i.content,!0,e.index(r));this.openFrontierNode(i.type,i.attrs,o)}return e}openFrontierNode(e,t=null,r){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=mn(this.placed,this.depth,x.from(e.create(t,r))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(x.empty,!0);t.childCount&&(this.placed=mn(this.placed,this.frontier.length,t))}};function pn(n,e,t){return e==0?n.cutByIndex(t,n.childCount):n.replaceChild(0,n.firstChild.copy(pn(n.firstChild.content,e-1,t)))}function mn(n,e,t){return e==0?n.append(t):n.replaceChild(n.childCount-1,n.lastChild.copy(mn(n.lastChild.content,e-1,t)))}function Gi(n,e){for(let t=0;t<e;t++)n=n.firstChild.content;return n}function Wl(n,e,t){if(e<=0)return n;let r=n.content;return e>1&&(r=r.replaceChild(0,Wl(r.firstChild,e-1,r.childCount==1?t-1:0))),e>0&&(r=n.type.contentMatch.fillBefore(r).append(r),t<=0&&(r=r.append(n.type.contentMatch.matchFragment(r).fillBefore(x.empty,!0)))),n.copy(r)}function Yi(n,e,t,r,i){let o=n.node(e),s=i?n.indexAfter(e):n.index(e);if(s==o.childCount&&!t.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!Pf(t,o.content,s)?l:null}function Pf(n,e,t){for(let r=t;r<e.childCount;r++)if(!n.allowsMarks(e.child(r).marks))return!0;return!1}function If(n){return n.spec.defining||n.spec.definingForContent}function Lf(n,e,t,r){if(!r.size)return n.deleteRange(e,t);let i=n.doc.resolve(e),o=n.doc.resolve(t);if(jl(i,o,r))return n.step(new oe(e,t,r));let s=_l(i,n.doc.resolve(t));s[s.length-1]==0&&s.pop();let l=-(i.depth+1);s.unshift(l);for(let f=i.depth,h=i.pos-1;f>0;f--,h--){let p=i.node(f).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;s.indexOf(f)>-1?l=f:i.before(f)==h&&s.splice(1,0,-f)}let a=s.indexOf(l),c=[],u=r.openStart;for(let f=r.content,h=0;;h++){let p=f.firstChild;if(c.push(p),h==r.openStart)break;f=p.content}for(let f=u-1;f>=0;f--){let h=c[f],p=If(h.type);if(p&&!h.sameMarkup(i.node(Math.abs(l)-1)))u=f;else if(p||!h.type.isTextblock)break}for(let f=r.openStart;f>=0;f--){let h=(f+u+1)%(r.openStart+1),p=c[h];if(p)for(let m=0;m<s.length;m++){let g=s[(m+a)%s.length],y=!0;g<0&&(y=!1,g=-g);let b=i.node(g-1),S=i.index(g-1);if(b.canReplaceWith(S,S,p.type,p.marks))return n.replace(i.before(g),y?o.after(g):t,new w(Kl(r.content,0,r.openStart,h),h,r.openEnd))}}let d=n.steps.length;for(let f=s.length-1;f>=0&&(n.replace(e,t,r),!(n.steps.length>d));f--){let h=s[f];h<0||(e=i.before(h),t=o.after(h))}}function Kl(n,e,t,r,i){if(e<t){let o=n.firstChild;n=n.replaceChild(0,o.copy(Kl(o.content,e+1,t,r,o)))}if(e>r){let o=i.contentMatchAt(0),s=o.fillBefore(n).append(n);n=s.append(o.matchFragment(s).fillBefore(x.empty,!0))}return n}function Bf(n,e,t,r){if(!r.isInline&&e==t&&n.doc.resolve(e).parent.content.size){let i=Rf(n.doc,e,r.type);i!=null&&(e=t=i)}n.replaceRange(e,t,new w(x.from(r),0,0))}function Ff(n,e,t){let r=n.doc.resolve(e),i=n.doc.resolve(t),o=_l(r,i);for(let s=0;s<o.length;s++){let l=o[s],a=s==o.length-1;if(a&&l==0||r.node(l).type.contentMatch.validEnd)return n.delete(r.start(l),i.end(l));if(l>0&&(a||r.node(l-1).canReplace(r.index(l-1),i.indexAfter(l-1))))return n.delete(r.before(l),i.after(l))}for(let s=1;s<=r.depth&&s<=i.depth;s++)if(e-r.start(s)==r.depth-s&&t>r.end(s)&&i.end(s)-t!=i.depth-s&&r.start(s-1)==i.start(s-1)&&r.node(s-1).canReplace(r.index(s-1),i.index(s-1)))return n.delete(r.before(s),t);n.delete(e,t)}function _l(n,e){let t=[],r=Math.min(n.depth,e.depth);for(let i=r;i>=0;i--){let o=n.start(i);if(o<n.pos-(n.depth-i)||e.end(i)>e.pos+(e.depth-i)||n.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(o==e.start(i)||i==n.depth&&i==e.depth&&n.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==o-1)&&t.push(i)}return t}var ar=class n extends Z{constructor(e,t,r){super(),this.pos=e,this.attr=t,this.value=r}apply(e){let t=e.nodeAt(this.pos);if(!t)return te.fail("No node at attribute step's position");let r=Object.create(null);for(let o in t.attrs)r[o]=t.attrs[o];r[this.attr]=this.value;let i=t.type.create(r,null,t.marks);return te.fromReplace(e,this.pos,this.pos+1,new w(x.from(i),0,t.isLeaf?0:1))}getMap(){return Ue.empty}invert(e){return new n(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new n(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new n(t.pos,t.attr,t.value)}};Z.jsonID("attr",ar);var cr=class n extends Z{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let i in e.attrs)t[i]=e.attrs[i];t[this.attr]=this.value;let r=e.type.create(t,e.content,e.marks);return te.ok(r)}getMap(){return Ue.empty}invert(e){return new n(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new n(t.attr,t.value)}};Z.jsonID("docAttr",cr);var _t=class extends Error{};_t=function n(e){let t=Error.call(this,e);return t.__proto__=n.prototype,t};_t.prototype=Object.create(Error.prototype);_t.prototype.constructor=_t;_t.prototype.name="TransformError";var Ut=class{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new yn}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new _t(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,r=w.empty){let i=kn(this.doc,e,t,r);return i&&this.step(i),this}replaceWith(e,t,r){return this.replace(e,t,new w(x.from(r),0,0))}delete(e,t){return this.replace(e,t,w.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,r){return Lf(this,e,t,r),this}replaceRangeWith(e,t,r){return Bf(this,e,t,r),this}deleteRange(e,t){return Ff(this,e,t),this}lift(e,t){return wf(this,e,t),this}join(e,t=1){return Df(this,e,t),this}wrap(e,t){return vf(this,e,t),this}setBlockType(e,t=e,r,i=null){return Tf(this,e,t,r,i),this}setNodeMarkup(e,t,r=null,i){return Af(this,e,t,r,i),this}setNodeAttribute(e,t,r){return this.step(new ar(e,t,r)),this}setDocAttribute(e,t){return this.step(new cr(e,t)),this}addNodeMark(e,t){return this.step(new xn(e,t)),this}removeNodeMark(e,t){let r=this.doc.nodeAt(e);if(!r)throw new RangeError("No node at position "+e);if(t instanceof H)t.isInSet(r.marks)&&this.step(new Kt(e,t));else{let i=r.marks,o,s=[];for(;o=t.isInSet(i);)s.push(new Kt(e,o)),i=o.removeFromSet(i);for(let l=s.length-1;l>=0;l--)this.step(s[l])}return this}split(e,t=1,r){return Of(this,e,t,r),this}addMark(e,t,r){return xf(this,e,t,r),this}removeMark(e,t,r){return kf(this,e,t,r),this}clearIncompatible(e,t,r){return eo(this,e,t,r),this}};var to=Object.create(null),D=class{constructor(e,t,r){this.$anchor=e,this.$head=t,this.ranges=r||[new fr(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=w.empty){let r=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=r,r=r.lastChild;let o=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:c}=s[l],u=e.mapping.slice(o);e.replaceRange(u.map(a.pos),u.map(c.pos),l?w.empty:t),l==0&&ql(e,o,(r?r.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let r=e.steps.length,i=this.ranges;for(let o=0;o<i.length;o++){let{$from:s,$to:l}=i[o],a=e.mapping.slice(r),c=a.map(s.pos),u=a.map(l.pos);o?e.deleteRange(c,u):(e.replaceRangeWith(c,u,t),ql(e,r,t.isInline?-1:1))}}static findFrom(e,t,r=!1){let i=e.parent.inlineContent?new A(e):qt(e.node(0),e.parent,e.pos,e.index(),t,r);if(i)return i;for(let o=e.depth-1;o>=0;o--){let s=t<0?qt(e.node(0),e.node(o),e.before(o+1),e.index(o),t,r):qt(e.node(0),e.node(o),e.after(o+1),e.index(o)+1,t,r);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new de(e.node(0))}static atStart(e){return qt(e,e,0,0,1)||new de(e)}static atEnd(e){return qt(e,e,e.content.size,e.childCount,-1)||new de(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let r=to[t.type];if(!r)throw new RangeError(`No selection type ${t.type} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in to)throw new RangeError("Duplicate use of selection JSON ID "+e);return to[e]=t,t.prototype.jsonID=e,t}getBookmark(){return A.between(this.$anchor,this.$head).getBookmark()}};D.prototype.visible=!0;var fr=class{constructor(e,t){this.$from=e,this.$to=t}},Ul=!1;function Jl(n){!Ul&&!n.parent.inlineContent&&(Ul=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+n.parent.type.name+")"))}var A=class n extends D{constructor(e,t=e){Jl(e),Jl(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let r=e.resolve(t.map(this.head));if(!r.parent.inlineContent)return D.near(r);let i=e.resolve(t.map(this.anchor));return new n(i.parent.inlineContent?i:r,r)}replace(e,t=w.empty){if(super.replace(e,t),t==w.empty){let r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}eq(e){return e instanceof n&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new hr(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new n(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,r=t){let i=e.resolve(t);return new this(i,r==t?i:e.resolve(r))}static between(e,t,r){let i=e.pos-t.pos;if((!r||i)&&(r=i>=0?1:-1),!t.parent.inlineContent){let o=D.findFrom(t,r,!0)||D.findFrom(t,-r,!0);if(o)t=o.$head;else return D.near(t,r)}return e.parent.inlineContent||(i==0?e=t:(e=(D.findFrom(e,-r,!0)||D.findFrom(e,r,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new n(e,t)}};D.jsonID("text",A);var hr=class n{constructor(e,t){this.anchor=e,this.head=t}map(e){return new n(e.map(this.anchor),e.map(this.head))}resolve(e){return A.between(e.resolve(this.anchor),e.resolve(this.head))}},v=class n extends D{constructor(e){let t=e.nodeAfter,r=e.node(0).resolve(e.pos+t.nodeSize);super(e,r),this.node=t}map(e,t){let{deleted:r,pos:i}=t.mapResult(this.anchor),o=e.resolve(i);return r?D.near(o):new n(o)}content(){return new w(x.from(this.node),0,0)}eq(e){return e instanceof n&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new ro(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new n(e.resolve(t.anchor))}static create(e,t){return new n(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}};v.prototype.visible=!1;D.jsonID("node",v);var ro=class n{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:r}=e.mapResult(this.anchor);return t?new hr(r,r):new n(r)}resolve(e){let t=e.resolve(this.anchor),r=t.nodeAfter;return r&&v.isSelectable(r)?new v(t):D.near(t)}},de=class n extends D{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=w.empty){if(t==w.empty){e.delete(0,e.doc.content.size);let r=D.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new n(e)}map(e){return new n(e)}eq(e){return e instanceof n}getBookmark(){return zf}};D.jsonID("all",de);var zf={map(){return this},resolve(n){return new de(n)}};function qt(n,e,t,r,i,o=!1){if(e.inlineContent)return A.create(n,t);for(let s=r-(i>0?0:1);i>0?s<e.childCount:s>=0;s+=i){let l=e.child(s);if(l.isAtom){if(!o&&v.isSelectable(l))return v.create(n,t-(i<0?l.nodeSize:0))}else{let a=qt(n,l,t+i,i<0?l.childCount:0,i,o);if(a)return a}t+=l.nodeSize*i}return null}function ql(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof oe||i instanceof q))return;let o=n.mapping.maps[r],s;o.forEach((l,a,c,u)=>{s==null&&(s=u)}),n.setSelection(D.near(n.doc.resolve(s),t))}var Gl=1,dr=2,Yl=4,io=class extends Ut{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|Gl)&~dr,this.storedMarks=null,this}get selectionSet(){return(this.updated&Gl)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=dr,this}ensureMarks(e){return H.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&dr)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&~dr,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let r=this.selection;return t&&(e=e.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||H.none))),r.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,r){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(r==null&&(r=t),r=r??t,!e)return this.deleteRange(t,r);let o=this.storedMarks;if(!o){let s=this.doc.resolve(t);o=r==t?s.marks():s.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(t,r,i.text(e,o)),this.selection.empty||this.setSelection(D.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=Yl,this}get scrolledIntoView(){return(this.updated&Yl)>0}};function Xl(n,e){return!e||!n?n:n.bind(e)}var Ct=class{constructor(e,t,r){this.name=e,this.init=Xl(t.init,r),this.apply=Xl(t.apply,r)}},Hf=[new Ct("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new Ct("selection",{init(n,e){return n.selection||D.atStart(e.doc)},apply(n){return n.selection}}),new Ct("storedMarks",{init(n){return n.storedMarks||null},apply(n,e,t,r){return r.selection.$cursor?n.storedMarks:null}}),new Ct("scrollToSelection",{init(){return 0},apply(n,e){return n.scrolledIntoView?e+1:e}})],Sn=class{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Hf.slice(),t&&t.forEach(r=>{if(this.pluginsByKey[r.key])throw new RangeError("Adding different instances of a keyed plugin ("+r.key+")");this.plugins.push(r),this.pluginsByKey[r.key]=r,r.spec.state&&this.fields.push(new Ct(r.key,r.spec.state,r))})}},pr=class n{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=t){let i=this.config.plugins[r];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],r=this.applyInner(e),i=null;for(;;){let o=!1;for(let s=0;s<this.config.plugins.length;s++){let l=this.config.plugins[s];if(l.spec.appendTransaction){let a=i?i[s].n:0,c=i?i[s].state:this,u=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,r);if(u&&r.filterTransaction(u,s)){if(u.setMeta("appendedTransaction",e),!i){i=[];for(let d=0;d<this.config.plugins.length;d++)i.push(d<s?{state:r,n:t.length}:{state:this,n:0})}t.push(u),r=r.applyInner(u),o=!0}i&&(i[s]={state:r,n:t.length})}}if(!o)return{state:r,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new n(this.config),r=this.config.fields;for(let i=0;i<r.length;i++){let o=r[i];t[o.name]=o.apply(e,this[o.name],this,t)}return t}get tr(){return new io(this)}static create(e){let t=new Sn(e.doc?e.doc.type.schema:e.schema,e.plugins),r=new n(t);for(let i=0;i<t.fields.length;i++)r[t.fields[i].name]=t.fields[i].init(e,r);return r}reconfigure(e){let t=new Sn(this.schema,e.plugins),r=t.fields,i=new n(t);for(let o=0;o<r.length;o++){let s=r[o].name;i[s]=this.hasOwnProperty(s)?this[s]:r[o].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(r=>r.toJSON())),e&&typeof e=="object")for(let r in e){if(r=="doc"||r=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[r],o=i.spec.state;o&&o.toJSON&&(t[r]=o.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,r){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new Sn(e.schema,e.plugins),o=new n(i);return i.fields.forEach(s=>{if(s.name=="doc")o.doc=Q.fromJSON(e.schema,t.doc);else if(s.name=="selection")o.selection=D.fromJSON(o.doc,t.selection);else if(s.name=="storedMarks")t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(r)for(let l in r){let a=r[l],c=a.spec.state;if(a.key==s.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){o[s.name]=c.fromJSON.call(a,e,t[l],o);return}}o[s.name]=s.init(e,o)}}),o}};function Ql(n,e,t){for(let r in n){let i=n[r];i instanceof Function?i=i.bind(e):r=="handleDOMEvents"&&(i=Ql(i,e,{})),t[r]=i}return t}var O=class{constructor(e){this.spec=e,this.props={},e.props&&Ql(e.props,this,this.props),this.key=e.key?e.key.key:Zl("plugin")}getState(e){return e[this.key]}},no=Object.create(null);function Zl(n){return n in no?n+"$"+ ++no[n]:(no[n]=0,n+"$")}var P=class{constructor(e="key"){this.key=Zl(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}};var ne=function(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e},Zt=function(n){let e=n.assignedSlot||n.parentNode;return e&&e.nodeType==11?e.host:e},uo=null,Ye=function(n,e,t){let r=uo||(uo=document.createRange());return r.setEnd(n,t??n.nodeValue.length),r.setStart(n,e||0),r},$f=function(){uo=null},Dt=function(n,e,t,r){return t&&(ea(n,e,t,r,-1)||ea(n,e,t,r,1))},Vf=/^(img|br|input|textarea|hr)$/i;function ea(n,e,t,r,i){for(var o;;){if(n==t&&e==r)return!0;if(e==(i<0?0:ve(n))){let s=n.parentNode;if(!s||s.nodeType!=1||On(n)||Vf.test(n.nodeName)||n.contentEditable=="false")return!1;e=ne(n)+(i<0?0:1),n=s}else if(n.nodeType==1){let s=n.childNodes[e+(i<0?-1:0)];if(s.nodeType==1&&s.contentEditable=="false")if(!((o=s.pmViewDesc)===null||o===void 0)&&o.ignoreForSelection)e+=i;else return!1;else n=s,e=i<0?ve(n):0}else return!1}}function ve(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function jf(n,e){for(;;){if(n.nodeType==3&&e)return n;if(n.nodeType==1&&e>0){if(n.contentEditable=="false")return null;n=n.childNodes[e-1],e=ve(n)}else if(n.parentNode&&!On(n))e=ne(n),n=n.parentNode;else return null}}function Wf(n,e){for(;;){if(n.nodeType==3&&e<n.nodeValue.length)return n;if(n.nodeType==1&&e<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[e],e=0}else if(n.parentNode&&!On(n))e=ne(n)+1,n=n.parentNode;else return null}}function Kf(n,e,t){for(let r=e==0,i=e==ve(n);r||i;){if(n==t)return!0;let o=ne(n);if(n=n.parentNode,!n)return!1;r=r&&o==0,i=i&&o==ve(n)}}function On(n){let e;for(let t=n;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==n||e.contentDOM==n)}var vr=function(n){return n.focusNode&&Dt(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function vt(n,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=n,t.key=t.code=e,t}function _f(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function Uf(n,e,t){if(n.caretPositionFromPoint)try{let r=n.caretPositionFromPoint(e,t);if(r)return{node:r.offsetNode,offset:Math.min(ve(r.offsetNode),r.offset)}}catch{}if(n.caretRangeFromPoint){let r=n.caretRangeFromPoint(e,t);if(r)return{node:r.startContainer,offset:Math.min(ve(r.startContainer),r.startOffset)}}}var Be=typeof navigator<"u"?navigator:null,ta=typeof document<"u"?document:null,dt=Be&&Be.userAgent||"",fo=/Edge\/(\d+)/.exec(dt),Pa=/MSIE \d/.exec(dt),ho=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(dt),ge=!!(Pa||ho||fo),ct=Pa?document.documentMode:ho?+ho[1]:fo?+fo[1]:0,Re=!ge&&/gecko\/(\d+)/i.test(dt);Re&&+(/Firefox\/(\d+)/.exec(dt)||[0,0])[1];var po=!ge&&/Chrome\/(\d+)/.exec(dt),le=!!po,Ia=po?+po[1]:0,fe=!ge&&!!Be&&/Apple Computer/.test(Be.vendor),en=fe&&(/Mobile\/\w+/.test(dt)||!!Be&&Be.maxTouchPoints>2),Ce=en||(Be?/Mac/.test(Be.platform):!1),Jf=Be?/Win/.test(Be.platform):!1,Xe=/Android \d/.test(dt),Nn=!!ta&&"webkitFontSmoothing"in ta.documentElement.style,qf=Nn?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Gf(n){let e=n.defaultView&&n.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function Ge(n,e){return typeof n=="number"?n:n[e]}function Yf(n){let e=n.getBoundingClientRect(),t=e.width/n.offsetWidth||1,r=e.height/n.offsetHeight||1;return{left:e.left,right:e.left+n.clientWidth*t,top:e.top,bottom:e.top+n.clientHeight*r}}function na(n,e,t){let r=n.someProp("scrollThreshold")||0,i=n.someProp("scrollMargin")||5,o=n.dom.ownerDocument;for(let s=t||n.dom;s;){if(s.nodeType!=1){s=Zt(s);continue}let l=s,a=l==o.body,c=a?Gf(o):Yf(l),u=0,d=0;if(e.top<c.top+Ge(r,"top")?d=-(c.top-e.top+Ge(i,"top")):e.bottom>c.bottom-Ge(r,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+Ge(i,"top")-c.top:e.bottom-c.bottom+Ge(i,"bottom")),e.left<c.left+Ge(r,"left")?u=-(c.left-e.left+Ge(i,"left")):e.right>c.right-Ge(r,"right")&&(u=e.right-c.right+Ge(i,"right")),u||d)if(a)o.defaultView.scrollBy(u,d);else{let h=l.scrollLeft,p=l.scrollTop;d&&(l.scrollTop+=d),u&&(l.scrollLeft+=u);let m=l.scrollLeft-h,g=l.scrollTop-p;e={left:e.left-m,top:e.top-g,right:e.right-m,bottom:e.bottom-g}}let f=a?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(f))break;s=f=="absolute"?s.offsetParent:Zt(s)}}function Xf(n){let e=n.dom.getBoundingClientRect(),t=Math.max(0,e.top),r,i;for(let o=(e.left+e.right)/2,s=t+1;s<Math.min(innerHeight,e.bottom);s+=5){let l=n.root.elementFromPoint(o,s);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){r=l,i=a.top;break}}return{refDOM:r,refTop:i,stack:La(n.dom)}}function La(n){let e=[],t=n.ownerDocument;for(let r=n;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),n!=t);r=Zt(r));return e}function Qf({refDOM:n,refTop:e,stack:t}){let r=n?n.getBoundingClientRect().top:0;Ba(t,r==0?0:r-e)}function Ba(n,e){for(let t=0;t<n.length;t++){let{dom:r,top:i,left:o}=n[t];r.scrollTop!=i+e&&(r.scrollTop=i+e),r.scrollLeft!=o&&(r.scrollLeft=o)}}var Gt=null;function Zf(n){if(n.setActive)return n.setActive();if(Gt)return n.focus(Gt);let e=La(n);n.focus(Gt==null?{get preventScroll(){return Gt={preventScroll:!0},!0}}:void 0),Gt||(Gt=!1,Ba(e,0))}function Fa(n,e){let t,r=2e8,i,o=0,s=e.top,l=e.top,a,c;for(let u=n.firstChild,d=0;u;u=u.nextSibling,d++){let f;if(u.nodeType==1)f=u.getClientRects();else if(u.nodeType==3)f=Ye(u).getClientRects();else continue;for(let h=0;h<f.length;h++){let p=f[h];if(p.top<=s&&p.bottom>=l){s=Math.max(p.bottom,s),l=Math.min(p.top,l);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<r){t=u,r=m,i=m&&t.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,u.nodeType==1&&m&&(o=d+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!a&&p.left<=e.left&&p.right>=e.left&&(a=u,c={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!t&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(o=d+1)}}return!t&&a&&(t=a,i=c,r=0),t&&t.nodeType==3?eh(t,i):!t||r&&t.nodeType==1?{node:n,offset:o}:Fa(t,i)}function eh(n,e){let t=n.nodeValue.length,r=document.createRange();for(let i=0;i<t;i++){r.setEnd(n,i+1),r.setStart(n,i);let o=st(r,1);if(o.top!=o.bottom&&Oo(e,o))return{node:n,offset:i+(e.left>=(o.left+o.right)/2?1:0)}}return{node:n,offset:0}}function Oo(n,e){return n.left>=e.left-1&&n.left<=e.right+1&&n.top>=e.top-1&&n.top<=e.bottom+1}function th(n,e){let t=n.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<n.getBoundingClientRect().left?t:n}function nh(n,e,t){let{node:r,offset:i}=Fa(e,t),o=-1;if(r.nodeType==1&&!r.firstChild){let s=r.getBoundingClientRect();o=s.left!=s.right&&t.left>(s.left+s.right)/2?1:-1}return n.docView.posFromDOM(r,i,o)}function rh(n,e,t,r){let i=-1;for(let o=e,s=!1;o!=n.dom;){let l=n.docView.nearestDesc(o,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!s&&a.left>r.left||a.top>r.top?i=l.posBefore:(!s&&a.right<r.left||a.bottom<r.top)&&(i=l.posAfter),s=!0),!l.contentDOM&&i<0&&!l.node.isText))return(l.node.isBlock?r.top<(a.top+a.bottom)/2:r.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;o=l.dom.parentNode}return i>-1?i:n.docView.posFromDOM(e,t,-1)}function za(n,e,t){let r=n.childNodes.length;if(r&&t.top<t.bottom)for(let i=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-t.top)/(t.bottom-t.top))-2)),o=i;;){let s=n.childNodes[o];if(s.nodeType==1){let l=s.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(Oo(e,c))return za(s,e,c)}}if((o=(o+1)%r)==i)break}return n}function ih(n,e){let t=n.dom.ownerDocument,r,i=0,o=Uf(t,e.left,e.top);o&&({node:r,offset:i}=o);let s=(n.root.elementFromPoint?n.root:t).elementFromPoint(e.left,e.top),l;if(!s||!n.dom.contains(s.nodeType!=1?s.parentNode:s)){let c=n.dom.getBoundingClientRect();if(!Oo(e,c)||(s=za(n.dom,e,c),!s))return null}if(fe)for(let c=s;r&&c;c=Zt(c))c.draggable&&(r=void 0);if(s=th(s,e),r){if(Re&&r.nodeType==1&&(i=Math.min(i,r.childNodes.length),i<r.childNodes.length)){let u=r.childNodes[i],d;u.nodeName=="IMG"&&(d=u.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&i++}let c;Nn&&i&&r.nodeType==1&&(c=r.childNodes[i-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&i--,r==n.dom&&i==r.childNodes.length-1&&r.lastChild.nodeType==1&&e.top>r.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(i==0||r.nodeType!=1||r.childNodes[i-1].nodeName!="BR")&&(l=rh(n,r,i,e))}l==null&&(l=nh(n,s,e));let a=n.docView.nearestDesc(s,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function ra(n){return n.top<n.bottom||n.left<n.right}function st(n,e){let t=n.getClientRects();if(t.length){let r=t[e<0?0:t.length-1];if(ra(r))return r}return Array.prototype.find.call(t,ra)||n.getBoundingClientRect()}var oh=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Ha(n,e,t){let{node:r,offset:i,atom:o}=n.docView.domFromPos(e,t<0?-1:1),s=Nn||Re;if(r.nodeType==3)if(s&&(oh.test(r.nodeValue)||(t<0?!i:i==r.nodeValue.length))){let a=st(Ye(r,i,i),t);if(Re&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let c=st(Ye(r,i-1,i-1),-1);if(c.top==a.top){let u=st(Ye(r,i,i+1),-1);if(u.top!=a.top)return wn(u,u.left<c.left)}}return a}else{let a=i,c=i,u=t<0?1:-1;return t<0&&!i?(c++,u=-1):t>=0&&i==r.nodeValue.length?(a--,u=1):t<0?a--:c++,wn(st(Ye(r,a,c),u),u<0)}if(!n.state.doc.resolve(e-(o||0)).parent.inlineContent){if(o==null&&i&&(t<0||i==ve(r))){let a=r.childNodes[i-1];if(a.nodeType==1)return oo(a.getBoundingClientRect(),!1)}if(o==null&&i<ve(r)){let a=r.childNodes[i];if(a.nodeType==1)return oo(a.getBoundingClientRect(),!0)}return oo(r.getBoundingClientRect(),t>=0)}if(o==null&&i&&(t<0||i==ve(r))){let a=r.childNodes[i-1],c=a.nodeType==3?Ye(a,ve(a)-(s?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return wn(st(c,1),!1)}if(o==null&&i<ve(r)){let a=r.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?Ye(a,0,s?0:1):a.nodeType==1?a:null:null;if(c)return wn(st(c,-1),!0)}return wn(st(r.nodeType==3?Ye(r):r,-t),t>=0)}function wn(n,e){if(n.width==0)return n;let t=e?n.left:n.right;return{top:n.top,bottom:n.bottom,left:t,right:t}}function oo(n,e){if(n.height==0)return n;let t=e?n.top:n.bottom;return{top:t,bottom:t,left:n.left,right:n.right}}function $a(n,e,t){let r=n.state,i=n.root.activeElement;r!=e&&n.updateState(e),i!=n.dom&&n.focus();try{return t()}finally{r!=e&&n.updateState(r),i!=n.dom&&i&&i.focus()}}function sh(n,e,t){let r=e.selection,i=t=="up"?r.$from:r.$to;return $a(n,e,()=>{let{node:o}=n.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(o,!0);if(!l)break;if(l.node.isBlock){o=l.contentDOM||l.dom;break}o=l.dom.parentNode}let s=Ha(n,i.pos,1);for(let l=o.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=Ye(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let u=a[c];if(u.bottom>u.top+1&&(t=="up"?s.top-u.top>(u.bottom-s.top)*2:u.bottom-s.bottom>(s.bottom-u.top)*2))return!1}}return!0})}var lh=/[\u0590-\u08ac]/;function ah(n,e,t){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,o=!i,s=i==r.parent.content.size,l=n.domSelection();return l?!lh.test(r.parent.textContent)||!l.modify?t=="left"||t=="backward"?o:s:$a(n,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:u,anchorOffset:d}=n.domSelectionRange(),f=l.caretBidiLevel;l.modify("move",t,"character");let h=r.depth?n.docView.domAfterPos(r.before()):n.dom,{focusNode:p,focusOffset:m}=n.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(u,d),a&&(a!=u||c!=d)&&l.extend&&l.extend(a,c)}catch{}return f!=null&&(l.caretBidiLevel=f),g}):r.pos==r.start()||r.pos==r.end()}var ia=null,oa=null,sa=!1;function ch(n,e,t){return ia==e&&oa==t?sa:(ia=e,oa=t,sa=t=="up"||t=="down"?sh(n,e,t):ah(n,e,t))}var Te=0,la=1,Tt=2,Fe=3,Rt=class{constructor(e,t,r,i){this.parent=e,this.children=t,this.dom=r,this.contentDOM=i,this.dirty=Te,r.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,r){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,r=this.posAtStart;;t++){let i=this.children[t];if(i==e)return r;r+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,r){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(r<0){let o,s;if(e==this.contentDOM)o=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.previousSibling}for(;o&&!((s=o.pmViewDesc)&&s.parent==this);)o=o.previousSibling;return o?this.posBeforeChild(s)+s.size:this.posAtStart}else{let o,s;if(e==this.contentDOM)o=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.nextSibling}for(;o&&!((s=o.pmViewDesc)&&s.parent==this);)o=o.nextSibling;return o?this.posBeforeChild(s):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>ne(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let o=e;;o=o.parentNode){if(o==this.dom){i=!1;break}if(o.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let o=e;;o=o.parentNode){if(o==this.dom){i=!0;break}if(o.nextSibling)break}}return i??r>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let r=!0,i=e;i;i=i.parentNode){let o=this.getDesc(i),s;if(o&&(!t||o.node))if(r&&(s=o.nodeDOM)&&!(s.nodeType==1?s.contains(e.nodeType==1?e:e.parentNode):s==e))r=!1;else return o}}getDesc(e){let t=e.pmViewDesc;for(let r=t;r;r=r.parent)if(r==this)return t}posFromDOM(e,t,r){for(let i=e;i;i=i.parentNode){let o=this.getDesc(i);if(o)return o.localPosFromDOM(e,t,r)}return-1}descAt(e){for(let t=0,r=0;t<this.children.length;t++){let i=this.children[t],o=r+i.size;if(r==e&&o!=r){for(;!i.border&&i.children.length;)for(let s=0;s<i.children.length;s++){let l=i.children[s];if(l.size){i=l;break}}return i}if(e<o)return i.descAt(e-r-i.border);r=o}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let r=0,i=0;for(let o=0;r<this.children.length;r++){let s=this.children[r],l=o+s.size;if(l>e||s instanceof yr){i=e-o;break}o=l}if(i)return this.children[r].domFromPos(i-this.children[r].border,t);for(let o;r&&!(o=this.children[r-1]).size&&o instanceof mr&&o.side>=0;r--);if(t<=0){let o,s=!0;for(;o=r?this.children[r-1]:null,!(!o||o.dom.parentNode==this.contentDOM);r--,s=!1);return o&&t&&s&&!o.border&&!o.domAtom?o.domFromPos(o.size,t):{node:this.contentDOM,offset:o?ne(o.dom)+1:0}}else{let o,s=!0;for(;o=r<this.children.length?this.children[r]:null,!(!o||o.dom.parentNode==this.contentDOM);r++,s=!1);return o&&s&&!o.border&&!o.domAtom?o.domFromPos(0,t):{node:this.contentDOM,offset:o?ne(o.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,r=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,o=-1;for(let s=r,l=0;;l++){let a=this.children[l],c=s+a.size;if(i==-1&&e<=c){let u=s+a.border;if(e>=u&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,u);e=s;for(let d=l;d>0;d--){let f=this.children[d-1];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(1)){i=ne(f.dom)+1;break}e-=f.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let u=l+1;u<this.children.length;u++){let d=this.children[u];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){o=ne(d.dom);break}t+=d.size}o==-1&&(o=this.contentDOM.childNodes.length);break}s=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:o}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:r}=this.domFromPos(e,0);if(t.nodeType!=1||r==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[r]}setSelection(e,t,r,i=!1){let o=Math.min(e,t),s=Math.max(e,t);for(let h=0,p=0;h<this.children.length;h++){let m=this.children[h],g=p+m.size;if(o>p&&s<g)return m.setSelection(e-p-m.border,t-p-m.border,r,i);p=g}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=r.root.getSelection(),u=r.domSelectionRange(),d=!1;if((Re||fe)&&e==t){let{node:h,offset:p}=l;if(h.nodeType==3){if(d=!!(p&&h.nodeValue[p-1]==`
`),d&&p==h.nodeValue.length)for(let m=h,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:ne(g)+1});break}let y=m.pmViewDesc;if(y&&y.node&&y.node.isBlock)break}}else{let m=h.childNodes[p-1];d=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(Re&&u.focusNode&&u.focusNode!=a.node&&u.focusNode.nodeType==1){let h=u.focusNode.childNodes[u.focusOffset];h&&h.contentEditable=="false"&&(i=!0)}if(!(i||d&&fe)&&Dt(l.node,l.offset,u.anchorNode,u.anchorOffset)&&Dt(a.node,a.offset,u.focusNode,u.focusOffset))return;let f=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),f=!0}catch{}}if(!f){if(e>t){let p=l;l=a,a=p}let h=document.createRange();h.setEnd(a.node,a.offset),h.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(h)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let r=0,i=0;i<this.children.length;i++){let o=this.children[i],s=r+o.size;if(r==s?e<=s&&t>=r:e<s&&t>r){let l=r+o.border,a=s-o.border;if(e>=l&&t<=a){this.dirty=e==r||t==s?Tt:la,e==l&&t==a&&(o.contentLost||o.dom.parentNode!=this.contentDOM)?o.dirty=Fe:o.markDirty(e-l,t-l);return}else o.dirty=o.dom==o.contentDOM&&o.dom.parentNode==this.contentDOM&&!o.children.length?Tt:Fe}r=s}this.dirty=Tt}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let r=e==1?Tt:la;t.dirty<r&&(t.dirty=r)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}},mr=class extends Rt{constructor(e,t,r,i){let o,s=t.type.toDOM;if(typeof s=="function"&&(s=s(r,()=>{if(!o)return i;if(o.parent)return o.parent.posBeforeChild(o)})),!t.type.spec.raw){if(s.nodeType!=1){let l=document.createElement("span");l.appendChild(s),s=l}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=t,this.widget=t,o=this}matchesWidget(e){return this.dirty==Te&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}},mo=class extends Rt{constructor(e,t,r,i){super(e,[],t,null),this.textDOM=r,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}},tn=class n extends Rt{constructor(e,t,r,i,o){super(e,[],r,i),this.mark=t,this.spec=o}static create(e,t,r,i){let o=i.nodeViews[t.type.name],s=o&&o(t,i,r);return(!s||!s.dom)&&(s=_e.renderSpec(document,t.type.spec.toDOM(t,r),null,t.attrs)),new n(e,t,s.dom,s.contentDOM||s.dom,s)}parseRule(){return this.dirty&Fe||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=Fe&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=Te){let r=this.parent;for(;!r.node;)r=r.parent;r.dirty<this.dirty&&(r.dirty=this.dirty),this.dirty=Te}}slice(e,t,r){let i=n.create(this.parent,this.mark,!0,r),o=this.children,s=this.size;t<s&&(o=xo(o,t,s,r)),e>0&&(o=xo(o,0,e,r));for(let l=0;l<o.length;l++)o[l].parent=i;return i.children=o,i}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}},ut=class n extends Rt{constructor(e,t,r,i,o,s,l,a,c){super(e,[],o,s),this.node=t,this.outerDeco=r,this.innerDeco=i,this.nodeDOM=l}static create(e,t,r,i,o,s){let l=o.nodeViews[t.type.name],a,c=l&&l(t,o,()=>{if(!a)return s;if(a.parent)return a.parent.posBeforeChild(a)},r,i),u=c&&c.dom,d=c&&c.contentDOM;if(t.isText){if(!u)u=document.createTextNode(t.text);else if(u.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else u||({dom:u,contentDOM:d}=_e.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!d&&!t.isText&&u.nodeName!="BR"&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),t.type.spec.draggable&&(u.draggable=!0));let f=u;return u=Wa(u,r,t),c?a=new go(e,t,r,i,u,d||null,f,c,o,s+1):t.isText?new gr(e,t,r,i,u,f,o):new n(e,t,r,i,u,d||null,f,o,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let r=this.children[t];if(this.dom.contains(r.dom.parentNode)){e.contentElement=r.dom.parentNode;break}}e.contentElement||(e.getContent=()=>x.empty)}return e}matchesNode(e,t,r){return this.dirty==Te&&e.eq(this.node)&&br(t,this.outerDeco)&&r.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let r=this.node.inlineContent,i=t,o=e.composing?this.localCompositionInfo(e,t):null,s=o&&o.pos>-1?o:null,l=o&&o.pos<0,a=new bo(this,s&&s.node,e);hh(this.node,this.innerDeco,(c,u,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,r,e):c.type.side>=0&&!d&&a.syncToMarks(u==this.node.childCount?H.none:this.node.child(u).marks,r,e),a.placeWidget(c,e,i)},(c,u,d,f)=>{a.syncToMarks(c.marks,r,e);let h;a.findNodeMatch(c,u,d,f)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(h=a.findIndexWithChild(o.node))>-1&&a.updateNodeAt(c,u,d,h,e)||a.updateNextNode(c,u,d,e,f,i)||a.addNode(c,u,d,e,i),i+=c.nodeSize}),a.syncToMarks([],r,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==Tt)&&(s&&this.protectLocalComposition(e,s),Va(this.contentDOM,this.children,e),en&&ph(this.dom))}localCompositionInfo(e,t){let{from:r,to:i}=e.state.selection;if(!(e.state.selection instanceof A)||r<t||i>t+this.node.content.size)return null;let o=e.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let s=o.nodeValue,l=mh(this.node.content,s,r-t,i-t);return l<0?null:{node:o,pos:l,text:s}}else return{node:o,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:r,text:i}){if(this.getDesc(t))return;let o=t;for(;o.parentNode!=this.contentDOM;o=o.parentNode){for(;o.previousSibling;)o.parentNode.removeChild(o.previousSibling);for(;o.nextSibling;)o.parentNode.removeChild(o.nextSibling);o.pmViewDesc&&(o.pmViewDesc=void 0)}let s=new mo(this,o,t,i);e.input.compositionNodes.push(s),this.children=xo(this.children,r,r+i.length,e,s)}update(e,t,r,i){return this.dirty==Fe||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,r,i),!0)}updateInner(e,t,r,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=r,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=Te}updateOuterDeco(e){if(br(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,r=this.dom;this.dom=ja(this.dom,this.nodeDOM,yo(this.outerDeco,this.node,t),yo(e,this.node,t)),this.dom!=r&&(r.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}};function aa(n,e,t,r,i){Wa(r,e,n);let o=new ut(void 0,n,e,t,r,r,r,i,0);return o.contentDOM&&o.updateChildren(i,0),o}var gr=class n extends ut{constructor(e,t,r,i,o,s,l){super(e,t,r,i,o,null,s,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,r,i){return this.dirty==Fe||this.dirty!=Te&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=Te||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=Te,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,r){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,r)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,r){let i=this.node.cut(e,t),o=document.createTextNode(i.text);return new n(this.parent,i,this.outerDeco,this.innerDeco,o,o,r)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=Fe)}get domAtom(){return!1}isText(e){return this.node.text==e}},yr=class extends Rt{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==Te&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}},go=class extends ut{constructor(e,t,r,i,o,s,l,a,c,u){super(e,t,r,i,o,s,l,c,u),this.spec=a}update(e,t,r,i){if(this.dirty==Fe)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let o=this.spec.update(e,t,r);return o&&this.updateInner(e,t,r,i),o}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,r,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,r,i){this.spec.setSelection?this.spec.setSelection(e,t,r.root):super.setSelection(e,t,r,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}};function Va(n,e,t){let r=n.firstChild,i=!1;for(let o=0;o<e.length;o++){let s=e[o],l=s.dom;if(l.parentNode==n){for(;l!=r;)r=ca(r),i=!0;r=r.nextSibling}else i=!0,n.insertBefore(l,r);if(s instanceof tn){let a=r?r.previousSibling:n.lastChild;Va(s.contentDOM,s.children,t),r=a?a.nextSibling:n.firstChild}}for(;r;)r=ca(r),i=!0;i&&t.trackWrites==n&&(t.trackWrites=null)}var Mn=function(n){n&&(this.nodeName=n)};Mn.prototype=Object.create(null);var Et=[new Mn];function yo(n,e,t){if(n.length==0)return Et;let r=t?Et[0]:new Mn,i=[r];for(let o=0;o<n.length;o++){let s=n[o].type.attrs;if(s){s.nodeName&&i.push(r=new Mn(s.nodeName));for(let l in s){let a=s[l];a!=null&&(t&&i.length==1&&i.push(r=new Mn(e.isInline?"span":"div")),l=="class"?r.class=(r.class?r.class+" ":"")+a:l=="style"?r.style=(r.style?r.style+";":"")+a:l!="nodeName"&&(r[l]=a))}}}return i}function ja(n,e,t,r){if(t==Et&&r==Et)return e;let i=e;for(let o=0;o<r.length;o++){let s=r[o],l=t[o];if(o){let a;l&&l.nodeName==s.nodeName&&i!=n&&(a=i.parentNode)&&a.nodeName.toLowerCase()==s.nodeName||(a=document.createElement(s.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=Et[0]),i=a}uh(i,l||Et[0],s)}return i}function uh(n,e,t){for(let r in e)r!="class"&&r!="style"&&r!="nodeName"&&!(r in t)&&n.removeAttribute(r);for(let r in t)r!="class"&&r!="style"&&r!="nodeName"&&t[r]!=e[r]&&n.setAttribute(r,t[r]);if(e.class!=t.class){let r=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let o=0;o<r.length;o++)i.indexOf(r[o])==-1&&n.classList.remove(r[o]);for(let o=0;o<i.length;o++)r.indexOf(i[o])==-1&&n.classList.add(i[o]);n.classList.length==0&&n.removeAttribute("class")}if(e.style!=t.style){if(e.style){let r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=r.exec(e.style);)n.style.removeProperty(i[1])}t.style&&(n.style.cssText+=t.style)}}function Wa(n,e,t){return ja(n,n,Et,yo(e,t,n.nodeType!=1))}function br(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].type.eq(e[t].type))return!1;return!0}function ca(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}var bo=class{constructor(e,t,r){this.lock=t,this.view=r,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=dh(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let r=e;r<t;r++)this.top.children[r].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,r){let i=0,o=this.stack.length>>1,s=Math.min(o,e.length);for(;i<s&&(i==o-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<o;)this.destroyRest(),this.top.dirty=Te,this.index=this.stack.pop(),this.top=this.stack.pop(),o--;for(;o<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[o])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=tn.create(this.top,e[o],t,r);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,o++}}findNodeMatch(e,t,r,i){let o=-1,s;if(i>=this.preMatch.index&&(s=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&s.matchesNode(e,t,r))o=this.top.children.indexOf(s,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,r)&&!this.preMatch.matched.has(c)){o=l;break}}return o<0?!1:(this.destroyBetween(this.index,o),this.index++,!0)}updateNodeAt(e,t,r,i,o){let s=this.top.children[i];return s.dirty==Fe&&s.dom==s.contentDOM&&(s.dirty=Tt),s.update(e,t,r,o)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let r=e.pmViewDesc;if(r){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==r)return i}return-1}e=t}}updateNextNode(e,t,r,i,o,s){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof ut){let c=this.preMatch.matched.get(a);if(c!=null&&c!=o)return!1;let u=a.dom,d,f=this.isLocked(u)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=Fe&&br(t,a.outerDeco));if(!f&&a.update(e,t,r,i))return this.destroyBetween(this.index,l),a.dom!=u&&(this.changed=!0),this.index++,!0;if(!f&&(d=this.recreateWrapper(a,e,t,r,i,s)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=Tt,d.updateChildren(i,s+1),d.dirty=Te),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,r,i,o,s){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!br(r,e.outerDeco)||!i.eq(e.innerDeco))return null;let l=ut.create(this.top,t,r,i,o,s);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,r,i,o){let s=ut.create(this.top,e,t,r,i,o);s.contentDOM&&s.updateChildren(i,o+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,t,r){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let o=new mr(this.top,e,t,r);this.top.children.splice(this.index++,0,o),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof tn;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof gr)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((fe||le)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let r=document.createElement(e);e=="IMG"&&(r.className="ProseMirror-separator",r.alt=""),e=="BR"&&(r.className="ProseMirror-trailingBreak");let i=new yr(this.top,[],r,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}};function dh(n,e){let t=e,r=t.children.length,i=n.childCount,o=new Map,s=[];e:for(;i>0;){let l;for(;;)if(r){let c=t.children[r-1];if(c instanceof tn)t=c,r=c.children.length;else{l=c,r--;break}}else{if(t==e)break e;r=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=n.child(i-1))break;--i,o.set(l,i),s.push(l)}}return{index:i,matched:o,matches:s.reverse()}}function fh(n,e){return n.type.side-e.type.side}function hh(n,e,t,r){let i=e.locals(n),o=0;if(i.length==0){for(let c=0;c<n.childCount;c++){let u=n.child(c);r(u,i,e.forChild(o,u),c),o+=u.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let u,d;for(;s<i.length&&i[s].to==o;){let g=i[s++];g.widget&&(u?(d||(d=[u])).push(g):u=g)}if(u)if(d){d.sort(fh);for(let g=0;g<d.length;g++)t(d[g],c,!!a)}else t(u,c,!!a);let f,h;if(a)h=-1,f=a,a=null;else if(c<n.childCount)h=c,f=n.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=o&&l.splice(g--,1);for(;s<i.length&&i[s].from<=o&&i[s].to>o;)l.push(i[s++]);let p=o+f.nodeSize;if(f.isText){let g=p;s<i.length&&i[s].from<g&&(g=i[s].from);for(let y=0;y<l.length;y++)l[y].to<g&&(g=l[y].to);g<p&&(a=f.cut(g-o),f=f.cut(0,g-o),p=g,h=-1)}else for(;s<i.length&&i[s].to<p;)s++;let m=f.isInline&&!f.isLeaf?l.filter(g=>!g.inline):l.slice();r(f,m,e.forChild(o,f),h),o=p}}function ph(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let e=n.style.cssText;n.style.cssText=e+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=e}}function mh(n,e,t,r){for(let i=0,o=0;i<n.childCount&&o<=r;){let s=n.child(i++),l=o;if(o+=s.nodeSize,!s.isText)continue;let a=s.text;for(;i<n.childCount;){let c=n.child(i++);if(o+=c.nodeSize,!c.isText)break;a+=c.text}if(o>=t){if(o>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let c=l<r?a.lastIndexOf(e,r-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}function xo(n,e,t,r,i){let o=[];for(let s=0,l=0;s<n.length;s++){let a=n[s],c=l,u=l+=a.size;c>=t||u<=e?o.push(a):(c<e&&o.push(a.slice(0,e-c,r)),i&&(o.push(i),i=void 0),u>t&&o.push(a.slice(t-c,a.size,r)))}return o}function No(n,e=null){let t=n.domSelectionRange(),r=n.state.doc;if(!t.focusNode)return null;let i=n.docView.nearestDesc(t.focusNode),o=i&&i.size==0,s=n.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(s<0)return null;let l=r.resolve(s),a,c;if(vr(t)){for(a=s;i&&!i.node;)i=i.parent;let d=i.node;if(i&&d.isAtom&&v.isSelectable(d)&&i.parent&&!(d.isInline&&Kf(t.focusNode,t.focusOffset,i.dom))){let f=i.posBefore;c=new v(s==f?l:r.resolve(f))}}else{if(t instanceof n.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let d=s,f=s;for(let h=0;h<t.rangeCount;h++){let p=t.getRangeAt(h);d=Math.min(d,n.docView.posFromDOM(p.startContainer,p.startOffset,1)),f=Math.max(f,n.docView.posFromDOM(p.endContainer,p.endOffset,-1))}if(d<0)return null;[a,s]=f==n.state.selection.anchor?[f,d]:[d,f],l=r.resolve(s)}else a=n.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(a<0)return null}let u=r.resolve(a);if(!c){let d=e=="pointer"||n.state.selection.head<l.pos&&!o?1:-1;c=Do(n,u,l,d)}return c}function Ka(n){return n.editable?n.hasFocus():Ua(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function Qe(n,e=!1){let t=n.state.selection;if(_a(n,t),!!Ka(n)){if(!e&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&le){let r=n.domSelectionRange(),i=n.domObserver.currentSelection;if(r.anchorNode&&i.anchorNode&&Dt(r.anchorNode,r.anchorOffset,i.anchorNode,i.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)yh(n);else{let{anchor:r,head:i}=t,o,s;ua&&!(t instanceof A)&&(t.$from.parent.inlineContent||(o=da(n,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(s=da(n,t.to))),n.docView.setSelection(r,i,n,e),ua&&(o&&fa(o),s&&fa(s)),t.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&gh(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}var ua=fe||le&&Ia<63;function da(n,e){let{node:t,offset:r}=n.docView.domFromPos(e,0),i=r<t.childNodes.length?t.childNodes[r]:null,o=r?t.childNodes[r-1]:null;if(fe&&i&&i.contentEditable=="false")return so(i);if((!i||i.contentEditable=="false")&&(!o||o.contentEditable=="false")){if(i)return so(i);if(o)return so(o)}}function so(n){return n.contentEditable="true",fe&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function fa(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function gh(n){let e=n.dom.ownerDocument;e.removeEventListener("selectionchange",n.input.hideSelectionGuard);let t=n.domSelectionRange(),r=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(t.anchorNode!=r||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Ka(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function yh(n){let e=n.domSelection(),t=document.createRange();if(!e)return;let r=n.cursorWrapper.dom,i=r.nodeName=="IMG";i?t.setStart(r.parentNode,ne(r)+1):t.setStart(r,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!i&&!n.state.selection.visible&&ge&&ct<=11&&(r.disabled=!0,r.disabled=!1)}function _a(n,e){if(e instanceof v){let t=n.docView.descAt(e.from);t!=n.lastSelectedViewDesc&&(ha(n),t&&t.selectNode(),n.lastSelectedViewDesc=t)}else ha(n)}function ha(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function Do(n,e,t,r){return n.someProp("createSelectionBetween",i=>i(n,e,t))||A.between(e,t,r)}function pa(n){return n.editable&&!n.hasFocus()?!1:Ua(n)}function Ua(n){let e=n.domSelectionRange();if(!e.anchorNode)return!1;try{return n.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(n.editable||n.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function bh(n){let e=n.docView.domFromPos(n.state.selection.anchor,0),t=n.domSelectionRange();return Dt(e.node,e.offset,t.anchorNode,t.anchorOffset)}function ko(n,e){let{$anchor:t,$head:r}=n.selection,i=e>0?t.max(r):t.min(r),o=i.parent.inlineContent?i.depth?n.doc.resolve(e>0?i.after():i.before()):null:i;return o&&D.findFrom(o,e)}function lt(n,e){return n.dispatch(n.state.tr.setSelection(e).scrollIntoView()),!0}function ma(n,e,t){let r=n.state.selection;if(r instanceof A)if(t.indexOf("s")>-1){let{$head:i}=r,o=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let s=n.state.doc.resolve(i.pos+o.nodeSize*(e<0?-1:1));return lt(n,new A(r.$anchor,s))}else if(r.empty){if(n.endOfTextblock(e>0?"forward":"backward")){let i=ko(n.state,e);return i&&i instanceof v?lt(n,i):!1}else if(!(Ce&&t.indexOf("m")>-1)){let i=r.$head,o=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,s;if(!o||o.isText)return!1;let l=e<0?i.pos-o.nodeSize:i.pos;return o.isAtom||(s=n.docView.descAt(l))&&!s.contentDOM?v.isSelectable(o)?lt(n,new v(e<0?n.state.doc.resolve(i.pos-o.nodeSize):i)):Nn?lt(n,new A(n.state.doc.resolve(e<0?l:l+o.nodeSize))):!1:!1}}else return!1;else{if(r instanceof v&&r.node.isInline)return lt(n,new A(e>0?r.$to:r.$from));{let i=ko(n.state,e);return i?lt(n,i):!1}}}function xr(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function Cn(n,e){let t=n.pmViewDesc;return t&&t.size==0&&(e<0||n.nextSibling||n.nodeName!="BR")}function Yt(n,e){return e<0?xh(n):kh(n)}function xh(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i,o,s=!1;for(Re&&t.nodeType==1&&r<xr(t)&&Cn(t.childNodes[r],-1)&&(s=!0);;)if(r>0){if(t.nodeType!=1)break;{let l=t.childNodes[r-1];if(Cn(l,-1))i=t,o=--r;else if(l.nodeType==3)t=l,r=t.nodeValue.length;else break}}else{if(Ja(t))break;{let l=t.previousSibling;for(;l&&Cn(l,-1);)i=t.parentNode,o=ne(l),l=l.previousSibling;if(l)t=l,r=xr(t);else{if(t=t.parentNode,t==n.dom)break;r=0}}}s?So(n,t,r):i&&So(n,i,o)}function kh(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i=xr(t),o,s;for(;;)if(r<i){if(t.nodeType!=1)break;let l=t.childNodes[r];if(Cn(l,1))o=t,s=++r;else break}else{if(Ja(t))break;{let l=t.nextSibling;for(;l&&Cn(l,1);)o=l.parentNode,s=ne(l)+1,l=l.nextSibling;if(l)t=l,r=0,i=xr(t);else{if(t=t.parentNode,t==n.dom)break;r=i=0}}}o&&So(n,o,s)}function Ja(n){let e=n.pmViewDesc;return e&&e.node&&e.node.isBlock}function Sh(n,e){for(;n&&e==n.childNodes.length&&!On(n);)e=ne(n)+1,n=n.parentNode;for(;n&&e<n.childNodes.length;){let t=n.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=0}}function wh(n,e){for(;n&&!e&&!On(n);)e=ne(n),n=n.parentNode;for(;n&&e;){let t=n.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=n.childNodes.length}}function So(n,e,t){if(e.nodeType!=3){let o,s;(s=Sh(e,t))?(e=s,t=0):(o=wh(e,t))&&(e=o,t=o.nodeValue.length)}let r=n.domSelection();if(!r)return;if(vr(r)){let o=document.createRange();o.setEnd(e,t),o.setStart(e,t),r.removeAllRanges(),r.addRange(o)}else r.extend&&r.extend(e,t);n.domObserver.setCurSelection();let{state:i}=n;setTimeout(()=>{n.state==i&&Qe(n)},50)}function ga(n,e){let t=n.state.doc.resolve(e);if(!(le||Jf)&&t.parent.inlineContent){let i=n.coordsAtPos(e);if(e>t.start()){let o=n.coordsAtPos(e-1),s=(o.top+o.bottom)/2;if(s>i.top&&s<i.bottom&&Math.abs(o.left-i.left)>1)return o.left<i.left?"ltr":"rtl"}if(e<t.end()){let o=n.coordsAtPos(e+1),s=(o.top+o.bottom)/2;if(s>i.top&&s<i.bottom&&Math.abs(o.left-i.left)>1)return o.left>i.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function ya(n,e,t){let r=n.state.selection;if(r instanceof A&&!r.empty||t.indexOf("s")>-1||Ce&&t.indexOf("m")>-1)return!1;let{$from:i,$to:o}=r;if(!i.parent.inlineContent||n.endOfTextblock(e<0?"up":"down")){let s=ko(n.state,e);if(s&&s instanceof v)return lt(n,s)}if(!i.parent.inlineContent){let s=e<0?i:o,l=r instanceof de?D.near(s,e):D.findFrom(s,e);return l?lt(n,l):!1}return!1}function ba(n,e){if(!(n.state.selection instanceof A))return!0;let{$head:t,$anchor:r,empty:i}=n.state.selection;if(!t.sameParent(r))return!0;if(!i)return!1;if(n.endOfTextblock(e>0?"forward":"backward"))return!0;let o=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(o&&!o.isText){let s=n.state.tr;return e<0?s.delete(t.pos-o.nodeSize,t.pos):s.delete(t.pos,t.pos+o.nodeSize),n.dispatch(s),!0}return!1}function xa(n,e,t){n.domObserver.stop(),e.contentEditable=t,n.domObserver.start()}function Mh(n){if(!fe||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=n.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let r=e.firstChild;xa(n,r,"true"),setTimeout(()=>xa(n,r,"false"),20)}return!1}function Ch(n){let e="";return n.ctrlKey&&(e+="c"),n.metaKey&&(e+="m"),n.altKey&&(e+="a"),n.shiftKey&&(e+="s"),e}function vh(n,e){let t=e.keyCode,r=Ch(e);if(t==8||Ce&&t==72&&r=="c")return ba(n,-1)||Yt(n,-1);if(t==46&&!e.shiftKey||Ce&&t==68&&r=="c")return ba(n,1)||Yt(n,1);if(t==13||t==27)return!0;if(t==37||Ce&&t==66&&r=="c"){let i=t==37?ga(n,n.state.selection.from)=="ltr"?-1:1:-1;return ma(n,i,r)||Yt(n,i)}else if(t==39||Ce&&t==70&&r=="c"){let i=t==39?ga(n,n.state.selection.from)=="ltr"?1:-1:1;return ma(n,i,r)||Yt(n,i)}else{if(t==38||Ce&&t==80&&r=="c")return ya(n,-1,r)||Yt(n,-1);if(t==40||Ce&&t==78&&r=="c")return Mh(n)||ya(n,1,r)||Yt(n,1);if(r==(Ce?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function Ro(n,e){n.someProp("transformCopied",h=>{e=h(e,n)});let t=[],{content:r,openStart:i,openEnd:o}=e;for(;i>1&&o>1&&r.childCount==1&&r.firstChild.childCount==1;){i--,o--;let h=r.firstChild;t.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),r=h.content}let s=n.someProp("clipboardSerializer")||_e.fromSchema(n.state.schema),l=Za(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c=a.firstChild,u,d=0;for(;c&&c.nodeType==1&&(u=Qa[c.nodeName.toLowerCase()]);){for(let h=u.length-1;h>=0;h--){let p=l.createElement(u[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${o}${d?` -${d}`:""} ${JSON.stringify(t)}`);let f=n.someProp("clipboardTextSerializer",h=>h(e,n))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:f,slice:e}}function qa(n,e,t,r,i){let o=i.parent.type.spec.code,s,l;if(!t&&!e)return null;let a=e&&(r||o||!t);if(a){if(n.someProp("transformPastedText",f=>{e=f(e,o||r,n)}),o)return e?new w(x.from(n.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):w.empty;let d=n.someProp("clipboardTextParser",f=>f(e,i,r,n));if(d)l=d;else{let f=i.marks(),{schema:h}=n.state,p=_e.fromSchema(h);s=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=s.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,f)))})}}else n.someProp("transformPastedHTML",d=>{t=d(t,n)}),s=Oh(t),Nn&&Nh(s);let c=s&&s.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let f=s.firstChild;for(;f&&f.nodeType!=1;)f=f.nextSibling;if(!f)break;s=f}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||Le.fromSchema(n.state.schema)).parseSlice(s,{preserveWhitespace:!!(a||u),context:i,ruleFromNode(f){return f.nodeName=="BR"&&!f.nextSibling&&f.parentNode&&!Th.test(f.parentNode.nodeName)?{ignore:!0}:null}})),u)l=Dh(ka(l,+u[1],+u[2]),u[4]);else if(l=w.maxOpen(Eh(l.content,i),!0),l.openStart||l.openEnd){let d=0,f=0;for(let h=l.content.firstChild;d<l.openStart&&!h.type.spec.isolating;d++,h=h.firstChild);for(let h=l.content.lastChild;f<l.openEnd&&!h.type.spec.isolating;f++,h=h.lastChild);l=ka(l,d,f)}return n.someProp("transformPasted",d=>{l=d(l,n)}),l}var Th=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Eh(n,e){if(n.childCount<2)return n;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),o,s=[];if(n.forEach(l=>{if(!s)return;let a=i.findWrapping(l.type),c;if(!a)return s=null;if(c=s.length&&o.length&&Ya(a,o,l,s[s.length-1],0))s[s.length-1]=c;else{s.length&&(s[s.length-1]=Xa(s[s.length-1],o.length));let u=Ga(l,a);s.push(u),i=i.matchType(u.type),o=a}}),s)return x.from(s)}return n}function Ga(n,e,t=0){for(let r=e.length-1;r>=t;r--)n=e[r].create(null,x.from(n));return n}function Ya(n,e,t,r,i){if(i<n.length&&i<e.length&&n[i]==e[i]){let o=Ya(n,e,t,r.lastChild,i+1);if(o)return r.copy(r.content.replaceChild(r.childCount-1,o));if(r.contentMatchAt(r.childCount).matchType(i==n.length-1?t.type:n[i+1]))return r.copy(r.content.append(x.from(Ga(t,n,i+1))))}}function Xa(n,e){if(e==0)return n;let t=n.content.replaceChild(n.childCount-1,Xa(n.lastChild,e-1)),r=n.contentMatchAt(n.childCount).fillBefore(x.empty,!0);return n.copy(t.append(r))}function wo(n,e,t,r,i,o){let s=e<0?n.firstChild:n.lastChild,l=s.content;return n.childCount>1&&(o=0),i<r-1&&(l=wo(l,e,t,r,i+1,o)),i>=t&&(l=e<0?s.contentMatchAt(0).fillBefore(l,o<=i).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(x.empty,!0))),n.replaceChild(e<0?0:n.childCount-1,s.copy(l))}function ka(n,e,t){return e<n.openStart&&(n=new w(wo(n.content,-1,e,n.openStart,0,n.openEnd),e,n.openEnd)),t<n.openEnd&&(n=new w(wo(n.content,1,t,n.openEnd,0,0),n.openStart,t)),n}var Qa={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},Sa=null;function Za(){return Sa||(Sa=document.implementation.createHTMLDocument("title"))}var lo=null;function Ah(n){let e=window.trustedTypes;return e?(lo||(lo=e.defaultPolicy||e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),lo.createHTML(n)):n}function Oh(n){let e=/^(\s*<meta [^>]*>)*/.exec(n);e&&(n=n.slice(e[0].length));let t=Za().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(n),i;if((i=r&&Qa[r[1].toLowerCase()])&&(n=i.map(o=>"<"+o+">").join("")+n+i.map(o=>"</"+o+">").reverse().join("")),t.innerHTML=Ah(n),i)for(let o=0;o<i.length;o++)t=t.querySelector(i[o])||t;return t}function Nh(n){let e=n.querySelectorAll(le?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let r=e[t];r.childNodes.length==1&&r.textContent=="\xA0"&&r.parentNode&&r.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),r)}}function Dh(n,e){if(!n.size)return n;let t=n.content.firstChild.type.schema,r;try{r=JSON.parse(e)}catch{return n}let{content:i,openStart:o,openEnd:s}=n;for(let l=r.length-2;l>=0;l-=2){let a=t.nodes[r[l]];if(!a||a.hasRequiredAttrs())break;i=x.from(a.create(r[l+1],i)),o++,s++}return new w(i,o,s)}var he={},pe={},Rh={touchstart:!0,touchmove:!0},Mo=class{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}};function Ph(n){for(let e in he){let t=he[e];n.dom.addEventListener(e,n.input.eventHandlers[e]=r=>{Lh(n,r)&&!Po(n,r)&&(n.editable||!(r.type in pe))&&t(n,r)},Rh[e]?{passive:!0}:void 0)}fe&&n.dom.addEventListener("input",()=>null),Co(n)}function at(n,e){n.input.lastSelectionOrigin=e,n.input.lastSelectionTime=Date.now()}function Ih(n){n.domObserver.stop();for(let e in n.input.eventHandlers)n.dom.removeEventListener(e,n.input.eventHandlers[e]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function Co(n){n.someProp("handleDOMEvents",e=>{for(let t in e)n.input.eventHandlers[t]||n.dom.addEventListener(t,n.input.eventHandlers[t]=r=>Po(n,r))})}function Po(n,e){return n.someProp("handleDOMEvents",t=>{let r=t[e.type];return r?r(n,e)||e.defaultPrevented:!1})}function Lh(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=n.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function Bh(n,e){!Po(n,e)&&he[e.type]&&(n.editable||!(e.type in pe))&&he[e.type](n,e)}pe.keydown=(n,e)=>{let t=e;if(n.input.shiftKey=t.keyCode==16||t.shiftKey,!tc(n,t)&&(n.input.lastKeyCode=t.keyCode,n.input.lastKeyCodeTime=Date.now(),!(Xe&&le&&t.keyCode==13)))if(t.keyCode!=229&&n.domObserver.forceFlush(),en&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let r=Date.now();n.input.lastIOSEnter=r,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==r&&(n.someProp("handleKeyDown",i=>i(n,vt(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",r=>r(n,t))||vh(n,t)?t.preventDefault():at(n,"key")};pe.keyup=(n,e)=>{e.keyCode==16&&(n.input.shiftKey=!1)};pe.keypress=(n,e)=>{let t=e;if(tc(n,t)||!t.charCode||t.ctrlKey&&!t.altKey||Ce&&t.metaKey)return;if(n.someProp("handleKeyPress",i=>i(n,t))){t.preventDefault();return}let r=n.state.selection;if(!(r instanceof A)||!r.$from.sameParent(r.$to)){let i=String.fromCharCode(t.charCode),o=()=>n.state.tr.insertText(i).scrollIntoView();!/[\r\n]/.test(i)&&!n.someProp("handleTextInput",s=>s(n,r.$from.pos,r.$to.pos,i,o))&&n.dispatch(o()),t.preventDefault()}};function Tr(n){return{left:n.clientX,top:n.clientY}}function Fh(n,e){let t=e.x-n.clientX,r=e.y-n.clientY;return t*t+r*r<100}function Io(n,e,t,r,i){if(r==-1)return!1;let o=n.state.doc.resolve(r);for(let s=o.depth+1;s>0;s--)if(n.someProp(e,l=>s>o.depth?l(n,t,o.nodeAfter,o.before(s),i,!0):l(n,t,o.node(s),o.before(s),i,!1)))return!0;return!1}function Qt(n,e,t){if(n.focused||n.focus(),n.state.selection.eq(e))return;let r=n.state.tr.setSelection(e);t=="pointer"&&r.setMeta("pointer",!0),n.dispatch(r)}function zh(n,e){if(e==-1)return!1;let t=n.state.doc.resolve(e),r=t.nodeAfter;return r&&r.isAtom&&v.isSelectable(r)?(Qt(n,new v(t),"pointer"),!0):!1}function Hh(n,e){if(e==-1)return!1;let t=n.state.selection,r,i;t instanceof v&&(r=t.node);let o=n.state.doc.resolve(e);for(let s=o.depth+1;s>0;s--){let l=s>o.depth?o.nodeAfter:o.node(s);if(v.isSelectable(l)){r&&t.$from.depth>0&&s>=t.$from.depth&&o.before(t.$from.depth+1)==t.$from.pos?i=o.before(t.$from.depth):i=o.before(s);break}}return i!=null?(Qt(n,v.create(n.state.doc,i),"pointer"),!0):!1}function $h(n,e,t,r,i){return Io(n,"handleClickOn",e,t,r)||n.someProp("handleClick",o=>o(n,e,r))||(i?Hh(n,t):zh(n,t))}function Vh(n,e,t,r){return Io(n,"handleDoubleClickOn",e,t,r)||n.someProp("handleDoubleClick",i=>i(n,e,r))}function jh(n,e,t,r){return Io(n,"handleTripleClickOn",e,t,r)||n.someProp("handleTripleClick",i=>i(n,e,r))||Wh(n,t,r)}function Wh(n,e,t){if(t.button!=0)return!1;let r=n.state.doc;if(e==-1)return r.inlineContent?(Qt(n,A.create(r,0,r.content.size),"pointer"),!0):!1;let i=r.resolve(e);for(let o=i.depth+1;o>0;o--){let s=o>i.depth?i.nodeAfter:i.node(o),l=i.before(o);if(s.inlineContent)Qt(n,A.create(r,l+1,l+1+s.content.size),"pointer");else if(v.isSelectable(s))Qt(n,v.create(r,l),"pointer");else continue;return!0}}function Lo(n){return kr(n)}var ec=Ce?"metaKey":"ctrlKey";he.mousedown=(n,e)=>{let t=e;n.input.shiftKey=t.shiftKey;let r=Lo(n),i=Date.now(),o="singleClick";i-n.input.lastClick.time<500&&Fh(t,n.input.lastClick)&&!t[ec]&&n.input.lastClick.button==t.button&&(n.input.lastClick.type=="singleClick"?o="doubleClick":n.input.lastClick.type=="doubleClick"&&(o="tripleClick")),n.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:o,button:t.button};let s=n.posAtCoords(Tr(t));s&&(o=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new vo(n,s,t,!!r)):(o=="doubleClick"?Vh:jh)(n,s.pos,s.inside,t)?t.preventDefault():at(n,"pointer"))};var vo=class{constructor(e,t,r,i){this.view=e,this.pos=t,this.event=r,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!r[ec],this.allowDefault=r.shiftKey;let o,s;if(t.inside>-1)o=e.state.doc.nodeAt(t.inside),s=t.inside;else{let u=e.state.doc.resolve(t.pos);o=u.parent,s=u.depth?u.before():0}let l=i?null:r.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(r.button==0&&o.type.spec.draggable&&o.type.spec.selectable!==!1||c instanceof v&&c.from<=s&&c.to>s)&&(this.mightDrag={node:o,pos:s,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&Re&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),at(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Qe(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(Tr(e))),this.updateAllowDefault(e),this.allowDefault||!t?at(this.view,"pointer"):$h(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||fe&&this.mightDrag&&!this.mightDrag.node.isAtom||le&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Qt(this.view,D.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):at(this.view,"pointer")}move(e){this.updateAllowDefault(e),at(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}};he.touchstart=n=>{n.input.lastTouch=Date.now(),Lo(n),at(n,"pointer")};he.touchmove=n=>{n.input.lastTouch=Date.now(),at(n,"pointer")};he.contextmenu=n=>Lo(n);function tc(n,e){return n.composing?!0:fe&&Math.abs(e.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}var Kh=Xe?5e3:-1;pe.compositionstart=pe.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:e}=n,t=e.selection.$to;if(e.selection instanceof A&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(r=>r.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||t.marks(),kr(n,!0),n.markCursor=null;else if(kr(n,!e.selection.empty),Re&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let r=n.domSelectionRange();for(let i=r.focusNode,o=r.focusOffset;i&&i.nodeType==1&&o!=0;){let s=o<0?i.lastChild:i.childNodes[o-1];if(!s)break;if(s.nodeType==3){let l=n.domSelection();l&&l.collapse(s,s.nodeValue.length);break}else i=s,o=-1}}n.input.composing=!0}nc(n,Kh)};pe.compositionend=(n,e)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=e.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,nc(n,20))};function nc(n,e){clearTimeout(n.input.composingTimeout),e>-1&&(n.input.composingTimeout=setTimeout(()=>kr(n),e))}function rc(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=Uh());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function _h(n){let e=n.domSelectionRange();if(!e.focusNode)return null;let t=jf(e.focusNode,e.focusOffset),r=Wf(e.focusNode,e.focusOffset);if(t&&r&&t!=r){let i=r.pmViewDesc,o=n.domObserver.lastChangedTextNode;if(t==o||r==o)return o;if(!i||!i.isText(r.nodeValue))return r;if(n.input.compositionNode==r){let s=t.pmViewDesc;if(!(!s||!s.isText(t.nodeValue)))return r}}return t||r}function Uh(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function kr(n,e=!1){if(!(Xe&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),rc(n),e||n.docView&&n.docView.dirty){let t=No(n),r=n.state.selection;return t&&!t.eq(r)?n.dispatch(n.state.tr.setSelection(t)):(n.markCursor||e)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?n.dispatch(n.state.tr.deleteSelection()):n.updateState(n.state),!0}return!1}}function Jh(n,e){if(!n.dom.parentNode)return;let t=n.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(e),n.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),n.focus()},50)}var vn=ge&&ct<15||en&&qf<604;he.copy=pe.cut=(n,e)=>{let t=e,r=n.state.selection,i=t.type=="cut";if(r.empty)return;let o=vn?null:t.clipboardData,s=r.content(),{dom:l,text:a}=Ro(n,s);o?(t.preventDefault(),o.clearData(),o.setData("text/html",l.innerHTML),o.setData("text/plain",a)):Jh(n,l),i&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function qh(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function Gh(n,e){if(!n.dom.parentNode)return;let t=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,r=n.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),r.parentNode&&r.parentNode.removeChild(r),t?Tn(n,r.value,null,i,e):Tn(n,r.textContent,r.innerHTML,i,e)},50)}function Tn(n,e,t,r,i){let o=qa(n,e,t,r,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,i,o||w.empty)))return!0;if(!o)return!1;let s=qh(o),l=s?n.state.tr.replaceSelectionWith(s,r):n.state.tr.replaceSelection(o);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function ic(n){let e=n.getData("text/plain")||n.getData("Text");if(e)return e;let t=n.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}pe.paste=(n,e)=>{let t=e;if(n.composing&&!Xe)return;let r=vn?null:t.clipboardData,i=n.input.shiftKey&&n.input.lastKeyCode!=45;r&&Tn(n,ic(r),r.getData("text/html"),i,t)?t.preventDefault():Gh(n,t)};var Sr=class{constructor(e,t,r){this.slice=e,this.move=t,this.node=r}},Yh=Ce?"altKey":"ctrlKey";function oc(n,e){let t=n.someProp("dragCopies",r=>!r(e));return t??!e[Yh]}he.dragstart=(n,e)=>{let t=e,r=n.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=n.state.selection,o=i.empty?null:n.posAtCoords(Tr(t)),s;if(!(o&&o.pos>=i.from&&o.pos<=(i instanceof v?i.to-1:i.to))){if(r&&r.mightDrag)s=v.create(n.state.doc,r.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=n.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=n.docView&&(s=v.create(n.state.doc,d.posBefore))}}let l=(s||n.state.selection).content(),{dom:a,text:c,slice:u}=Ro(n,l);(!t.dataTransfer.files.length||!le||Ia>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(vn?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",vn||t.dataTransfer.setData("text/plain",c),n.dragging=new Sr(u,oc(n,t),s)};he.dragend=n=>{let e=n.dragging;window.setTimeout(()=>{n.dragging==e&&(n.dragging=null)},50)};pe.dragover=pe.dragenter=(n,e)=>e.preventDefault();pe.drop=(n,e)=>{let t=e,r=n.dragging;if(n.dragging=null,!t.dataTransfer)return;let i=n.posAtCoords(Tr(t));if(!i)return;let o=n.state.doc.resolve(i.pos),s=r&&r.slice;s?n.someProp("transformPasted",p=>{s=p(s,n)}):s=qa(n,ic(t.dataTransfer),vn?null:t.dataTransfer.getData("text/html"),!1,o);let l=!!(r&&oc(n,t));if(n.someProp("handleDrop",p=>p(n,t,s||w.empty,l))){t.preventDefault();return}if(!s)return;t.preventDefault();let a=s?ur(n.state.doc,o.pos,s):o.pos;a==null&&(a=o.pos);let c=n.state.tr;if(l){let{node:p}=r;p?p.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=s.openStart==0&&s.openEnd==0&&s.content.childCount==1,f=c.doc;if(d?c.replaceRangeWith(u,u,s.content.firstChild):c.replaceRange(u,u,s),c.doc.eq(f))return;let h=c.doc.resolve(u);if(d&&v.isSelectable(s.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new v(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,b)=>p=b),c.setSelection(Do(n,h,c.doc.resolve(p)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};he.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&Qe(n)},20))};he.blur=(n,e)=>{let t=e;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),t.relatedTarget&&n.dom.contains(t.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};he.beforeinput=(n,e)=>{if(le&&Xe&&e.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:r}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=r||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",o=>o(n,vt(8,"Backspace")))))return;let{$cursor:i}=n.state.selection;i&&i.pos>0&&n.dispatch(n.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let n in pe)he[n]=pe[n];function En(n,e){if(n==e)return!0;for(let t in n)if(n[t]!==e[t])return!1;for(let t in e)if(!(t in n))return!1;return!0}var wr=class n{constructor(e,t){this.toDOM=e,this.spec=t||Ot,this.side=this.spec.side||0}map(e,t,r,i){let{pos:o,deleted:s}=e.mapResult(t.from+i,this.side<0?-1:1);return s?null:new ee(o-r,o-r,this)}valid(){return!0}eq(e){return this==e||e instanceof n&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&En(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}},At=class n{constructor(e,t){this.attrs=e,this.spec=t||Ot}map(e,t,r,i){let o=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-r,s=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-r;return o>=s?null:new ee(o,s,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof n&&En(this.attrs,e.attrs)&&En(this.spec,e.spec)}static is(e){return e.type instanceof n}destroy(){}},To=class n{constructor(e,t){this.attrs=e,this.spec=t||Ot}map(e,t,r,i){let o=e.mapResult(t.from+i,1);if(o.deleted)return null;let s=e.mapResult(t.to+i,-1);return s.deleted||s.pos<=o.pos?null:new ee(o.pos-r,s.pos-r,this)}valid(e,t){let{index:r,offset:i}=e.content.findIndex(t.from),o;return i==t.from&&!(o=e.child(r)).isText&&i+o.nodeSize==t.to}eq(e){return this==e||e instanceof n&&En(this.attrs,e.attrs)&&En(this.spec,e.spec)}destroy(){}},ee=class n{constructor(e,t,r){this.from=e,this.to=t,this.type=r}copy(e,t){return new n(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,r){return this.type.map(e,this,t,r)}static widget(e,t,r){return new n(e,e,new wr(t,r))}static inline(e,t,r,i){return new n(e,t,new At(r,i))}static node(e,t,r,i){return new n(e,t,new To(r,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof At}get widget(){return this.type instanceof wr}},Xt=[],Ot={},W=class n{constructor(e,t){this.local=e.length?e:Xt,this.children=t.length?t:Xt}static create(e,t){return t.length?Cr(t,e,0,Ot):se}find(e,t,r){let i=[];return this.findInner(e??0,t??1e9,i,0,r),i}findInner(e,t,r,i,o){for(let s=0;s<this.local.length;s++){let l=this.local[s];l.from<=t&&l.to>=e&&(!o||o(l.spec))&&r.push(l.copy(l.from+i,l.to+i))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<t&&this.children[s+1]>e){let l=this.children[s]+1;this.children[s+2].findInner(e-l,t-l,r,i+l,o)}}map(e,t,r){return this==se||e.maps.length==0?this:this.mapInner(e,t,0,0,r||Ot)}mapInner(e,t,r,i,o){let s;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,r,i);a&&a.type.valid(t,a)?(s||(s=[])).push(a):o.onRemove&&o.onRemove(this.local[l].spec)}return this.children.length?Xh(this.children,s||[],e,t,r,i,o):s?new n(s.sort(Nt),Xt):se}add(e,t){return t.length?this==se?n.create(e,t):this.addInner(e,t,0):this}addInner(e,t,r){let i,o=0;e.forEach((l,a)=>{let c=a+r,u;if(u=lc(t,l,c)){for(i||(i=this.children.slice());o<i.length&&i[o]<a;)o+=3;i[o]==a?i[o+2]=i[o+2].addInner(l,u,c+1):i.splice(o,0,a,a+l.nodeSize,Cr(u,l,c+1,Ot)),o+=3}});let s=sc(o?ac(t):t,-r);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||s.splice(l--,1);return new n(s.length?this.local.concat(s).sort(Nt):this.local,i||this.children)}remove(e){return e.length==0||this==se?this:this.removeInner(e,0)}removeInner(e,t){let r=this.children,i=this.local;for(let o=0;o<r.length;o+=3){let s,l=r[o]+t,a=r[o+1]+t;for(let u=0,d;u<e.length;u++)(d=e[u])&&d.from>l&&d.to<a&&(e[u]=null,(s||(s=[])).push(d));if(!s)continue;r==this.children&&(r=this.children.slice());let c=r[o+2].removeInner(s,l+1);c!=se?r[o+2]=c:(r.splice(o,3),o-=3)}if(i.length){for(let o=0,s;o<e.length;o++)if(s=e[o])for(let l=0;l<i.length;l++)i[l].eq(s,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return r==this.children&&i==this.local?this:i.length||r.length?new n(i,r):se}forChild(e,t){if(this==se)return this;if(t.isLeaf)return n.empty;let r,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(r=this.children[l+2]);break}let o=e+1,s=o+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<s&&a.to>o&&a.type instanceof At){let c=Math.max(o,a.from)-o,u=Math.min(s,a.to)-o;c<u&&(i||(i=[])).push(a.copy(c,u))}}if(i){let l=new n(i.sort(Nt),Xt);return r?new Mr([l,r]):l}return r||se}eq(e){if(this==e)return!0;if(!(e instanceof n)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Bo(this.localsInner(e))}localsInner(e){if(this==se)return Xt;if(e.inlineContent||!this.local.some(At.is))return this.local;let t=[];for(let r=0;r<this.local.length;r++)this.local[r].type instanceof At||t.push(this.local[r]);return t}forEachSet(e){e(this)}};W.empty=new W([],[]);W.removeOverlap=Bo;var se=W.empty,Mr=class n{constructor(e){this.members=e}map(e,t){let r=this.members.map(i=>i.map(e,t,Ot));return n.from(r)}forChild(e,t){if(t.isLeaf)return W.empty;let r=[];for(let i=0;i<this.members.length;i++){let o=this.members[i].forChild(e,t);o!=se&&(o instanceof n?r=r.concat(o.members):r.push(o))}return n.from(r)}eq(e){if(!(e instanceof n)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,r=!0;for(let i=0;i<this.members.length;i++){let o=this.members[i].localsInner(e);if(o.length)if(!t)t=o;else{r&&(t=t.slice(),r=!1);for(let s=0;s<o.length;s++)t.push(o[s])}}return t?Bo(r?t:t.sort(Nt)):Xt}static from(e){switch(e.length){case 0:return se;case 1:return e[0];default:return new n(e.every(t=>t instanceof W)?e:e.reduce((t,r)=>t.concat(r instanceof W?r:r.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}};function Xh(n,e,t,r,i,o,s){let l=n.slice();for(let c=0,u=o;c<t.maps.length;c++){let d=0;t.maps[c].forEach((f,h,p,m)=>{let g=m-p-(h-f);for(let y=0;y<l.length;y+=3){let b=l[y+1];if(b<0||f>b+u-d)continue;let S=l[y]+u-d;h>=S?l[y+1]=f<=S?-2:-1:f>=u&&g&&(l[y]+=g,l[y+1]+=g)}d+=g}),u=t.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let u=t.map(n[c]+o),d=u-i;if(d<0||d>=r.content.size){a=!0;continue}let f=t.map(n[c+1]+o,-1),h=f-i,{index:p,offset:m}=r.content.findIndex(d),g=r.maybeChild(p);if(g&&m==d&&m+g.nodeSize==h){let y=l[c+2].mapInner(t,g,u+1,n[c]+o+1,s);y!=se?(l[c]=d,l[c+1]=h,l[c+2]=y):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=Qh(l,n,e,t,i,o,s),u=Cr(c,r,0,s);e=u.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,f=0;d<u.children.length;d+=3){let h=u.children[d];for(;f<l.length&&l[f]<h;)f+=3;l.splice(f,0,u.children[d],u.children[d+1],u.children[d+2])}}return new W(e.sort(Nt),l)}function sc(n,e){if(!e||!n.length)return n;let t=[];for(let r=0;r<n.length;r++){let i=n[r];t.push(new ee(i.from+e,i.to+e,i.type))}return t}function Qh(n,e,t,r,i,o,s){function l(a,c){for(let u=0;u<a.local.length;u++){let d=a.local[u].map(r,i,c);d?t.push(d):s.onRemove&&s.onRemove(a.local[u].spec)}for(let u=0;u<a.children.length;u+=3)l(a.children[u+2],a.children[u]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],e[a]+o+1);return t}function lc(n,e,t){if(e.isLeaf)return null;let r=t+e.nodeSize,i=null;for(let o=0,s;o<n.length;o++)(s=n[o])&&s.from>t&&s.to<r&&((i||(i=[])).push(s),n[o]=null);return i}function ac(n){let e=[];for(let t=0;t<n.length;t++)n[t]!=null&&e.push(n[t]);return e}function Cr(n,e,t,r){let i=[],o=!1;e.forEach((l,a)=>{let c=lc(n,l,a+t);if(c){o=!0;let u=Cr(c,l,t+a+1,r);u!=se&&i.push(a,a+l.nodeSize,u)}});let s=sc(o?ac(n):n,-t).sort(Nt);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||(r.onRemove&&r.onRemove(s[l].spec),s.splice(l--,1));return s.length||i.length?new W(s,i):se}function Nt(n,e){return n.from-e.from||n.to-e.to}function Bo(n){let e=n;for(let t=0;t<e.length-1;t++){let r=e[t];if(r.from!=r.to)for(let i=t+1;i<e.length;i++){let o=e[i];if(o.from==r.from){o.to!=r.to&&(e==n&&(e=n.slice()),e[i]=o.copy(o.from,r.to),wa(e,i+1,o.copy(r.to,o.to)));continue}else{o.from<r.to&&(e==n&&(e=n.slice()),e[t]=r.copy(r.from,o.from),wa(e,i,r.copy(o.from,r.to)));break}}}return e}function wa(n,e,t){for(;e<n.length&&Nt(t,n[e])>0;)e++;n.splice(e,0,t)}function ao(n){let e=[];return n.someProp("decorations",t=>{let r=t(n.state);r&&r!=se&&e.push(r)}),n.cursorWrapper&&e.push(W.create(n.state.doc,[n.cursorWrapper.deco])),Mr.from(e)}var Zh={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},ep=ge&&ct<=11,Eo=class{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}},Ao=class{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Eo,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(r=>{for(let i=0;i<r.length;i++)this.queue.push(r[i]);ge&&ct<=11&&r.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),ep&&(this.onCharData=r=>{this.queue.push({target:r.target,type:"characterData",oldValue:r.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Zh)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(pa(this.view)){if(this.suppressingSelectionUpdates)return Qe(this.view);if(ge&&ct<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Dt(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,r;for(let o=e.focusNode;o;o=Zt(o))t.add(o);for(let o=e.anchorNode;o;o=Zt(o))if(t.has(o)){r=o;break}let i=r&&this.view.docView.nearestDesc(r);if(i&&i.ignoreMutation({type:"selection",target:r.nodeType==3?r.parentNode:r}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let r=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&pa(e)&&!this.ignoreSelectionChange(r),o=-1,s=-1,l=!1,a=[];if(e.editable)for(let u=0;u<t.length;u++){let d=this.registerMutation(t[u],a);d&&(o=o<0?d.from:Math.min(d.from,o),s=s<0?d.to:Math.max(d.to,s),d.typeOver&&(l=!0))}if(Re&&a.length){let u=a.filter(d=>d.nodeName=="BR");if(u.length==2){let[d,f]=u;d.parentNode&&d.parentNode.parentNode==f.parentNode?f.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let f of u){let h=f.parentNode;h&&h.nodeName=="LI"&&(!d||rp(e,d)!=h)&&f.remove()}}}let c=null;o<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&vr(r)&&(c=No(e))&&c.eq(D.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Qe(e),this.currentSelection.set(r),e.scrollToSelection()):(o>-1||i)&&(o>-1&&(e.docView.markDirty(o,s),tp(e)),this.handleDOMChange(o,s,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(r)||Qe(e),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let r=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(r==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!r||r.ignoreMutation(e))return null;if(e.type=="childList"){for(let u=0;u<e.addedNodes.length;u++){let d=e.addedNodes[u];t.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(r.contentDOM&&r.contentDOM!=r.dom&&!r.contentDOM.contains(e.target))return{from:r.posBefore,to:r.posAfter};let i=e.previousSibling,o=e.nextSibling;if(ge&&ct<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:d,nextSibling:f}=e.addedNodes[u];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(i=d),(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(o=f)}let s=i&&i.parentNode==e.target?ne(i)+1:0,l=r.localPosFromDOM(e.target,s,-1),a=o&&o.parentNode==e.target?ne(o):e.target.childNodes.length,c=r.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:r.posAtStart-r.border,to:r.posAtEnd+r.border}:(this.lastChangedTextNode=e.target,{from:r.posAtStart,to:r.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}},Ma=new WeakMap,Ca=!1;function tp(n){if(!Ma.has(n)&&(Ma.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=Re,Ca)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ca=!0}}function va(n,e){let t=e.startContainer,r=e.startOffset,i=e.endContainer,o=e.endOffset,s=n.domAtPos(n.state.selection.anchor);return Dt(s.node,s.offset,i,o)&&([t,r,i,o]=[i,o,t,r]),{anchorNode:t,anchorOffset:r,focusNode:i,focusOffset:o}}function np(n,e){if(e.getComposedRanges){let i=e.getComposedRanges(n.root)[0];if(i)return va(n,i)}let t;function r(i){i.preventDefault(),i.stopImmediatePropagation(),t=i.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",r,!0),t?va(n,t):null}function rp(n,e){for(let t=e.parentNode;t&&t!=n.dom;t=t.parentNode){let r=n.docView.nearestDesc(t,!0);if(r&&r.node.isBlock)return t}return null}function ip(n,e,t){let{node:r,fromOffset:i,toOffset:o,from:s,to:l}=n.docView.parseRange(e,t),a=n.domSelectionRange(),c,u=a.anchorNode;if(u&&n.dom.contains(u.nodeType==1?u:u.parentNode)&&(c=[{node:u,offset:a.anchorOffset}],vr(a)||c.push({node:a.focusNode,offset:a.focusOffset})),le&&n.input.lastKeyCode===8)for(let g=o;g>i;g--){let y=r.childNodes[g-1],b=y.pmViewDesc;if(y.nodeName=="BR"&&!b){o=g;break}if(!b||b.size)break}let d=n.state.doc,f=n.someProp("domParser")||Le.fromSchema(n.state.schema),h=d.resolve(s),p=null,m=f.parse(r,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:o,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:op,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,y=c[1]&&c[1].pos;y==null&&(y=g),p={anchor:g+s,head:y+s}}return{doc:m,sel:p,from:s,to:l}}function op(n){let e=n.pmViewDesc;if(e)return e.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(fe&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(n.parentNode.lastChild==n||fe&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}var sp=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function lp(n,e,t,r,i){let o=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,e<0){let E=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,F=No(n,E);if(F&&!n.state.selection.eq(F)){if(le&&Xe&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",J=>J(n,vt(13,"Enter"))))return;let R=n.state.tr.setSelection(F);E=="pointer"?R.setMeta("pointer",!0):E=="key"&&R.scrollIntoView(),o&&R.setMeta("composition",o),n.dispatch(R)}return}let s=n.state.doc.resolve(e),l=s.sharedDepth(t);e=s.before(l+1),t=n.state.doc.resolve(t).after(l+1);let a=n.state.selection,c=ip(n,e,t),u=n.state.doc,d=u.slice(c.from,c.to),f,h;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(f=n.state.selection.to,h="end"):(f=n.state.selection.from,h="start"),n.input.lastKeyCode=null;let p=up(d.content,c.doc.content,c.from,f,h);if(p&&n.input.domChangeCount++,(en&&n.input.lastIOSEnter>Date.now()-225||Xe)&&i.some(E=>E.nodeType==1&&!sp.test(E.nodeName))&&(!p||p.endA>=p.endB)&&n.someProp("handleKeyDown",E=>E(n,vt(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!p)if(r&&a instanceof A&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let E=Ta(n,n.state.doc,c.sel);if(E&&!E.eq(n.state.selection)){let F=n.state.tr.setSelection(E);o&&F.setMeta("composition",o),n.dispatch(F)}}return}n.state.selection.from<n.state.selection.to&&p.start==p.endB&&n.state.selection instanceof A&&(p.start>n.state.selection.from&&p.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?p.start=n.state.selection.from:p.endA<n.state.selection.to&&p.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(p.endB+=n.state.selection.to-p.endA,p.endA=n.state.selection.to)),ge&&ct<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)==" \xA0"&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),y=u.resolve(p.start),b=m.sameParent(g)&&m.parent.inlineContent&&y.end()>=p.endA,S;if((en&&n.input.lastIOSEnter>Date.now()-225&&(!b||i.some(E=>E.nodeName=="DIV"||E.nodeName=="P"))||!b&&m.pos<c.doc.content.size&&(!m.sameParent(g)||!m.parent.inlineContent)&&!/\S/.test(c.doc.textBetween(m.pos,g.pos,"",""))&&(S=D.findFrom(c.doc.resolve(m.pos+1),1,!0))&&S.head>m.pos)&&n.someProp("handleKeyDown",E=>E(n,vt(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>p.start&&cp(u,p.start,p.endA,m,g)&&n.someProp("handleKeyDown",E=>E(n,vt(8,"Backspace")))){Xe&&le&&n.domObserver.suppressSelectionUpdates();return}le&&p.endB==p.start&&(n.input.lastChromeDelete=Date.now()),Xe&&!b&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(E){return E(n,vt(13,"Enter"))})},20));let C=p.start,M=p.endA,L=E=>{let F=E||n.state.tr.replace(C,M,c.doc.slice(p.start-c.from,p.endB-c.from));if(c.sel){let R=Ta(n,F.doc,c.sel);R&&!(le&&n.composing&&R.empty&&(p.start!=p.endB||n.input.lastChromeDelete<Date.now()-100)&&(R.head==C||R.head==F.mapping.map(M)-1)||ge&&R.empty&&R.head==C)&&F.setSelection(R)}return o&&F.setMeta("composition",o),F.scrollIntoView()},$;if(b){if(m.pos==g.pos){ge&&ct<=11&&m.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>Qe(n),20));let E=L(n.state.tr.delete(C,M)),F=u.resolve(p.start).marksAcross(u.resolve(p.endA));F&&E.ensureMarks(F),n.dispatch(E)}else if(p.endA==p.endB&&($=ap(m.parent.content.cut(m.parentOffset,g.parentOffset),y.parent.content.cut(y.parentOffset,p.endA-y.start())))){let E=L(n.state.tr);$.type=="add"?E.addMark(C,M,$.mark):E.removeMark(C,M,$.mark),n.dispatch(E)}else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let E=m.parent.textBetween(m.parentOffset,g.parentOffset),F=()=>L(n.state.tr.insertText(E,C,M));n.someProp("handleTextInput",R=>R(n,C,M,E,F))||n.dispatch(F())}}else n.dispatch(L())}function Ta(n,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:Do(n,e.resolve(t.anchor),e.resolve(t.head))}function ap(n,e){let t=n.firstChild.marks,r=e.firstChild.marks,i=t,o=r,s,l,a;for(let u=0;u<r.length;u++)i=r[u].removeFromSet(i);for(let u=0;u<t.length;u++)o=t[u].removeFromSet(o);if(i.length==1&&o.length==0)l=i[0],s="add",a=u=>u.mark(l.addToSet(u.marks));else if(i.length==0&&o.length==1)l=o[0],s="remove",a=u=>u.mark(l.removeFromSet(u.marks));else return null;let c=[];for(let u=0;u<e.childCount;u++)c.push(a(e.child(u)));if(x.from(c).eq(n))return{mark:l,type:s}}function cp(n,e,t,r,i){if(t-e<=i.pos-r.pos||co(r,!0,!1)<i.pos)return!1;let o=n.resolve(e);if(!r.parent.isTextblock){let l=o.nodeAfter;return l!=null&&t==e+l.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=n.resolve(co(o,!0,!0));return!s.parent.isTextblock||s.pos>t||co(s,!0,!1)<t?!1:r.parent.content.cut(r.parentOffset).eq(s.parent.content)}function co(n,e,t){let r=n.depth,i=e?n.end():n.pos;for(;r>0&&(e||n.indexAfter(r)==n.node(r).childCount);)r--,i++,e=!1;if(t){let o=n.node(r).maybeChild(n.indexAfter(r));for(;o&&!o.isLeaf;)o=o.firstChild,i++}return i}function up(n,e,t,r,i){let o=n.findDiffStart(e,t);if(o==null)return null;let{a:s,b:l}=n.findDiffEnd(e,t+n.size,t+e.size);if(i=="end"){let a=Math.max(0,o-Math.min(s,l));r-=s+a-o}if(s<o&&n.size<e.size){let a=r<=o&&r>=s?o-r:0;o-=a,o&&o<e.size&&Ea(e.textBetween(o-1,o+1))&&(o+=a?1:-1),l=o+(l-s),s=o}else if(l<o){let a=r<=o&&r>=l?o-r:0;o-=a,o&&o<n.size&&Ea(n.textBetween(o-1,o+1))&&(o+=a?1:-1),s=o+(s-l),l=o}return{start:o,endA:s,endB:l}}function Ea(n){if(n.length!=2)return!1;let e=n.charCodeAt(0),t=n.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}var An=class{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Mo,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(Ra),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Na(this),Oa(this),this.nodeViews=Da(this),this.docView=aa(this.state.doc,Aa(this),ao(this),this.dom,this),this.domObserver=new Ao(this,(r,i,o,s)=>lp(this,r,i,o,s)),this.domObserver.start(),Ph(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&Co(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(Ra),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let r in this._props)t[r]=this._props[r];t.state=this.state;for(let r in e)t[r]=e[r];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var r;let i=this.state,o=!1,s=!1;e.storedMarks&&this.composing&&(rc(this),s=!0),this.state=e;let l=i.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let h=Da(this);fp(h,this.nodeViews)&&(this.nodeViews=h,o=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&Co(this),this.editable=Na(this),Oa(this);let a=ao(this),c=Aa(this),u=i.plugins!=e.plugins&&!i.doc.eq(e.doc)?"reset":e.scrollToSelection>i.scrollToSelection?"to selection":"preserve",d=o||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(i.selection))&&(s=!0);let f=u=="preserve"&&s&&this.dom.style.overflowAnchor==null&&Xf(this);if(s){this.domObserver.stop();let h=d&&(ge||le)&&!this.composing&&!i.selection.empty&&!e.selection.empty&&dp(i.selection,e.selection);if(d){let p=le?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=_h(this)),(o||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=aa(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&bh(this))?Qe(this,h):(_a(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(i),!((r=this.dragging)===null||r===void 0)&&r.node&&!i.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,i),u=="reset"?this.dom.scrollTop=0:u=="to selection"?this.scrollToSelection():f&&Qf(f)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!(!e||!this.dom.contains(e.nodeType==1?e:e.parentNode))){if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof v){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&na(this,t.getBoundingClientRect(),e)}else na(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let r=this.directPlugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let r=this.state.plugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let r=this.pluginViews[t];r.update&&r.update(this,e)}}updateDraggedNode(e,t){let r=e.node,i=-1;if(this.state.doc.nodeAt(r.from)==r.node)i=r.from;else{let o=r.from+(this.state.doc.content.size-t.doc.content.size);(o>0&&this.state.doc.nodeAt(o))==r.node&&(i=o)}this.dragging=new Sr(e.slice,e.move,i<0?void 0:v.create(this.state.doc,i))}someProp(e,t){let r=this._props&&this._props[e],i;if(r!=null&&(i=t?t(r):r))return i;for(let s=0;s<this.directPlugins.length;s++){let l=this.directPlugins[s].props[e];if(l!=null&&(i=t?t(l):l))return i}let o=this.state.plugins;if(o)for(let s=0;s<o.length;s++){let l=o[s].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(ge){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&Zf(this.dom),Qe(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return ih(this,e)}coordsAtPos(e,t=1){return Ha(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,r=-1){let i=this.docView.posFromDOM(e,t,r);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return ch(this,t||this.state,e)}pasteHTML(e,t){return Tn(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Tn(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return Ro(this,e)}destroy(){this.docView&&(Ih(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],ao(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,$f())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return Bh(this,e)}domSelectionRange(){let e=this.domSelection();return e?fe&&this.root.nodeType===11&&_f(this.dom.ownerDocument)==this.dom&&np(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}};An.prototype.dispatch=function(n){let e=this._props.dispatchTransaction;e?e.call(this,n):this.updateState(this.state.apply(n))};function Aa(n){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(n.editable),n.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(n.state)),t)for(let r in t)r=="class"?e.class+=" "+t[r]:r=="style"?e.style=(e.style?e.style+";":"")+t[r]:!e[r]&&r!="contenteditable"&&r!="nodeName"&&(e[r]=String(t[r]))}),e.translate||(e.translate="no"),[ee.node(0,n.state.doc.content.size,e)]}function Oa(n){if(n.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),n.cursorWrapper={dom:e,deco:ee.widget(n.state.selection.from,e,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function Na(n){return!n.someProp("editable",e=>e(n.state)===!1)}function dp(n,e){let t=Math.min(n.$anchor.sharedDepth(n.head),e.$anchor.sharedDepth(e.head));return n.$anchor.start(t)!=e.$anchor.start(t)}function Da(n){let e=Object.create(null);function t(r){for(let i in r)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=r[i])}return n.someProp("nodeViews",t),n.someProp("markViews",t),e}function fp(n,e){let t=0,r=0;for(let i in n){if(n[i]!=e[i])return!0;t++}for(let i in e)r++;return t!=r}function Ra(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var Ze={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Ar={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},hp=typeof navigator<"u"&&/Mac/.test(navigator.platform),pp=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(G=0;G<10;G++)Ze[48+G]=Ze[96+G]=String(G);var G;for(G=1;G<=24;G++)Ze[G+111]="F"+G;var G;for(G=65;G<=90;G++)Ze[G]=String.fromCharCode(G+32),Ar[G]=String.fromCharCode(G);var G;for(Er in Ze)Ar.hasOwnProperty(Er)||(Ar[Er]=Ze[Er]);var Er;function cc(n){var e=hp&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||pp&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?Ar:Ze)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}var mp=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),gp=typeof navigator<"u"&&/Win/.test(navigator.platform);function yp(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let r,i,o,s;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))o=!0;else if(/^mod$/i.test(a))mp?s=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return r&&(t="Alt-"+t),i&&(t="Ctrl-"+t),s&&(t="Meta-"+t),o&&(t="Shift-"+t),t}function bp(n){let e=Object.create(null);for(let t in n)e[yp(t)]=n[t];return e}function Fo(n,e,t=!0){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t&&e.shiftKey&&(n="Shift-"+n),n}function uc(n){return new O({props:{handleKeyDown:zo(n)}})}function zo(n){let e=bp(n);return function(t,r){let i=cc(r),o,s=e[Fo(i,r)];if(s&&s(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(r.shiftKey){let l=e[Fo(i,r,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((r.altKey||r.metaKey||r.ctrlKey)&&!(gp&&r.ctrlKey&&r.altKey)&&(o=Ze[r.keyCode])&&o!=i){let l=e[Fo(o,r)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}var Or=(n,e)=>n.selection.empty?!1:(e&&e(n.tr.deleteSelection().scrollIntoView()),!0);function fc(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("backward",n):t.parentOffset>0)?null:t}var $o=(n,e,t)=>{let r=fc(n,t);if(!r)return!1;let i=jo(r);if(!i){let s=r.blockRange(),l=s&&qe(s);return l==null?!1:(e&&e(n.tr.lift(s,l).scrollIntoView()),!0)}let o=i.nodeBefore;if(Sc(n,i,e,-1))return!0;if(r.parent.content.size==0&&(nn(o,"end")||v.isSelectable(o)))for(let s=r.depth;;s--){let l=kn(n.doc,r.before(s),r.after(s),w.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=n.tr.step(l);a.setSelection(nn(o,"end")?D.findFrom(a.doc.resolve(a.mapping.map(i.pos,-1)),-1):v.create(a.doc,i.pos-o.nodeSize)),e(a.scrollIntoView())}return!0}if(s==1||r.node(s-1).childCount>1)break}return o.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos-o.nodeSize,i.pos).scrollIntoView()),!0):!1},hc=(n,e,t)=>{let r=fc(n,t);if(!r)return!1;let i=jo(r);return i?mc(n,i,e):!1},pc=(n,e,t)=>{let r=gc(n,t);if(!r)return!1;let i=_o(r);return i?mc(n,i,e):!1};function mc(n,e,t){let r=e.nodeBefore,i=r,o=e.pos-1;for(;!i.isTextblock;o--){if(i.type.spec.isolating)return!1;let u=i.lastChild;if(!u)return!1;i=u}let s=e.nodeAfter,l=s,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let u=l.firstChild;if(!u)return!1;l=u}let c=kn(n.doc,o,a,w.empty);if(!c||c.from!=o||c instanceof oe&&c.slice.size>=a-o)return!1;if(t){let u=n.tr.step(c);u.setSelection(A.create(u.doc,o)),t(u.scrollIntoView())}return!0}function nn(n,e,t=!1){for(let r=n;r;r=e=="start"?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(t&&r.childCount!=1)return!1}return!1}var Vo=(n,e,t)=>{let{$head:r,empty:i}=n.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("backward",n):r.parentOffset>0)return!1;o=jo(r)}let s=o&&o.nodeBefore;return!s||!v.isSelectable(s)?!1:(e&&e(n.tr.setSelection(v.create(n.doc,o.pos-s.nodeSize)).scrollIntoView()),!0)};function jo(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){if(n.index(e)>0)return n.doc.resolve(n.before(e+1));if(n.node(e).type.spec.isolating)break}return null}function gc(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("forward",n):t.parentOffset<t.parent.content.size)?null:t}var Wo=(n,e,t)=>{let r=gc(n,t);if(!r)return!1;let i=_o(r);if(!i)return!1;let o=i.nodeAfter;if(Sc(n,i,e,1))return!0;if(r.parent.content.size==0&&(nn(o,"start")||v.isSelectable(o))){let s=kn(n.doc,r.before(),r.after(),w.empty);if(s&&s.slice.size<s.to-s.from){if(e){let l=n.tr.step(s);l.setSelection(nn(o,"start")?D.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):v.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return o.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos,i.pos+o.nodeSize).scrollIntoView()),!0):!1},Ko=(n,e,t)=>{let{$head:r,empty:i}=n.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("forward",n):r.parentOffset<r.parent.content.size)return!1;o=_o(r)}let s=o&&o.nodeAfter;return!s||!v.isSelectable(s)?!1:(e&&e(n.tr.setSelection(v.create(n.doc,o.pos)).scrollIntoView()),!0)};function _o(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){let t=n.node(e);if(n.index(e)+1<t.childCount)return n.doc.resolve(n.after(e+1));if(t.type.spec.isolating)break}return null}var yc=(n,e)=>{let t=n.selection,r=t instanceof v,i;if(r){if(t.node.isTextblock||!Me(n.doc,t.from))return!1;i=t.from}else if(i=Mt(n.doc,t.from,-1),i==null)return!1;if(e){let o=n.tr.join(i);r&&o.setSelection(v.create(o.doc,i-n.doc.resolve(i).nodeBefore.nodeSize)),e(o.scrollIntoView())}return!0},bc=(n,e)=>{let t=n.selection,r;if(t instanceof v){if(t.node.isTextblock||!Me(n.doc,t.to))return!1;r=t.to}else if(r=Mt(n.doc,t.to,1),r==null)return!1;return e&&e(n.tr.join(r).scrollIntoView()),!0},xc=(n,e)=>{let{$from:t,$to:r}=n.selection,i=t.blockRange(r),o=i&&qe(i);return o==null?!1:(e&&e(n.tr.lift(i,o).scrollIntoView()),!0)},Uo=(n,e)=>{let{$head:t,$anchor:r}=n.selection;return!t.parent.type.spec.code||!t.sameParent(r)?!1:(e&&e(n.tr.insertText(`
`).scrollIntoView()),!0)};function Jo(n){for(let e=0;e<n.edgeCount;e++){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}var qo=(n,e)=>{let{$head:t,$anchor:r}=n.selection;if(!t.parent.type.spec.code||!t.sameParent(r))return!1;let i=t.node(-1),o=t.indexAfter(-1),s=Jo(i.contentMatchAt(o));if(!s||!i.canReplaceWith(o,o,s))return!1;if(e){let l=t.after(),a=n.tr.replaceWith(l,l,s.createAndFill());a.setSelection(D.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},Go=(n,e)=>{let t=n.selection,{$from:r,$to:i}=t;if(t instanceof de||r.parent.inlineContent||i.parent.inlineContent)return!1;let o=Jo(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(e){let s=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=n.tr.insert(s,o.createAndFill());l.setSelection(A.create(l.doc,s+1)),e(l.scrollIntoView())}return!0},Yo=(n,e)=>{let{$cursor:t}=n.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let o=t.before();if(ke(n.doc,o))return e&&e(n.tr.split(o).scrollIntoView()),!0}let r=t.blockRange(),i=r&&qe(r);return i==null?!1:(e&&e(n.tr.lift(r,i).scrollIntoView()),!0)};function xp(n){return(e,t)=>{let{$from:r,$to:i}=e.selection;if(e.selection instanceof v&&e.selection.node.isBlock)return!r.parentOffset||!ke(e.doc,r.pos)?!1:(t&&t(e.tr.split(r.pos).scrollIntoView()),!0);if(!r.depth)return!1;let o=[],s,l,a=!1,c=!1;for(let h=r.depth;;h--)if(r.node(h).isBlock){a=r.end(h)==r.pos+(r.depth-h),c=r.start(h)==r.pos-(r.depth-h),l=Jo(r.node(h-1).contentMatchAt(r.indexAfter(h-1)));let m=n&&n(i.parent,a,r);o.unshift(m||(a&&l?{type:l}:null)),s=h;break}else{if(h==1)return!1;o.unshift(null)}let u=e.tr;(e.selection instanceof A||e.selection instanceof de)&&u.deleteSelection();let d=u.mapping.map(r.pos),f=ke(u.doc,d,o.length,o);if(f||(o[0]=l?{type:l}:null,f=ke(u.doc,d,o.length,o)),!f)return!1;if(u.split(d,o.length,o),!a&&c&&r.node(s).type!=l){let h=u.mapping.map(r.before(s)),p=u.doc.resolve(h);l&&r.node(s-1).canReplaceWith(p.index(),p.index()+1,l)&&u.setNodeMarkup(u.mapping.map(r.before(s)),l)}return t&&t(u.scrollIntoView()),!0}}var kp=xp();var kc=(n,e)=>{let{$from:t,to:r}=n.selection,i,o=t.sharedDepth(r);return o==0?!1:(i=t.before(o),e&&e(n.tr.setSelection(v.create(n.doc,i))),!0)},Sp=(n,e)=>(e&&e(n.tr.setSelection(new de(n.doc))),!0);function wp(n,e,t){let r=e.nodeBefore,i=e.nodeAfter,o=e.index();return!r||!i||!r.type.compatibleContent(i.type)?!1:!r.content.size&&e.parent.canReplace(o-1,o)?(t&&t(n.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(o,o+1)||!(i.isTextblock||Me(n.doc,e.pos))?!1:(t&&t(n.tr.join(e.pos).scrollIntoView()),!0)}function Sc(n,e,t,r){let i=e.nodeBefore,o=e.nodeAfter,s,l,a=i.type.spec.isolating||o.type.spec.isolating;if(!a&&wp(n,e,t))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(s=(l=i.contentMatchAt(i.childCount)).findWrapping(o.type))&&l.matchType(s[0]||o.type).validEnd){if(t){let h=e.pos+o.nodeSize,p=x.empty;for(let y=s.length-1;y>=0;y--)p=x.from(s[y].create(null,p));p=x.from(i.copy(p));let m=n.tr.step(new q(e.pos-1,h,e.pos,h,new w(p,1,0),s.length,!0)),g=m.doc.resolve(h+2*s.length);g.nodeAfter&&g.nodeAfter.type==i.type&&Me(m.doc,g.pos)&&m.join(g.pos),t(m.scrollIntoView())}return!0}let u=o.type.spec.isolating||r>0&&a?null:D.findFrom(e,1),d=u&&u.$from.blockRange(u.$to),f=d&&qe(d);if(f!=null&&f>=e.depth)return t&&t(n.tr.lift(d,f).scrollIntoView()),!0;if(c&&nn(o,"start",!0)&&nn(i,"end")){let h=i,p=[];for(;p.push(h),!h.isTextblock;)h=h.lastChild;let m=o,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(h.canReplace(h.childCount,h.childCount,m.content)){if(t){let y=x.empty;for(let S=p.length-1;S>=0;S--)y=x.from(p[S].copy(y));let b=n.tr.step(new q(e.pos-p.length,e.pos+o.nodeSize,e.pos+g,e.pos+o.nodeSize-g,new w(y,p.length,0),0,!0));t(b.scrollIntoView())}return!0}}return!1}function wc(n){return function(e,t){let r=e.selection,i=n<0?r.$from:r.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return i.node(o).isTextblock?(t&&t(e.tr.setSelection(A.create(e.doc,n<0?i.start(o):i.end(o)))),!0):!1}}var Xo=wc(-1),Qo=wc(1);function Mc(n,e=null){return function(t,r){let{$from:i,$to:o}=t.selection,s=i.blockRange(o),l=s&&Jt(s,n,e);return l?(r&&r(t.tr.wrap(s,l).scrollIntoView()),!0):!1}}function Zo(n,e=null){return function(t,r){let i=!1;for(let o=0;o<t.selection.ranges.length&&!i;o++){let{$from:{pos:s},$to:{pos:l}}=t.selection.ranges[o];t.doc.nodesBetween(s,l,(a,c)=>{if(i)return!1;if(!(!a.isTextblock||a.hasMarkup(n,e)))if(a.type==n)i=!0;else{let u=t.doc.resolve(c),d=u.index();i=u.parent.canReplaceWith(d,d+1,n)}})}if(!i)return!1;if(r){let o=t.tr;for(let s=0;s<t.selection.ranges.length;s++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[s];o.setBlockType(l,a,n,e)}r(o.scrollIntoView())}return!0}}function es(...n){return function(e,t,r){for(let i=0;i<n.length;i++)if(n[i](e,t,r))return!0;return!1}}var Ho=es(Or,$o,Vo),dc=es(Or,Wo,Ko),ft={Enter:es(Uo,Go,Yo,kp),"Mod-Enter":qo,Backspace:Ho,"Mod-Backspace":Ho,"Shift-Backspace":Ho,Delete:dc,"Mod-Delete":dc,"Mod-a":Sp},Mp={"Ctrl-h":ft.Backspace,"Alt-Backspace":ft["Mod-Backspace"],"Ctrl-d":ft.Delete,"Ctrl-Alt-Backspace":ft["Mod-Delete"],"Alt-Delete":ft["Mod-Delete"],"Alt-d":ft["Mod-Delete"],"Ctrl-a":Xo,"Ctrl-e":Qo};for(let n in ft)Mp[n]=ft[n];var z0=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform?os.platform()=="darwin":!1;function Cc(n,e=null){return function(t,r){let{$from:i,$to:o}=t.selection,s=i.blockRange(o);if(!s)return!1;let l=r?t.tr:null;return Cp(l,s,n,e)?(r&&r(l.scrollIntoView()),!0):!1}}function Cp(n,e,t,r=null){let i=!1,o=e,s=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=s.resolve(e.start-2);o=new St(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new St(e.$from,s.resolve(e.$to.end(e.depth)),e.depth)),i=!0}let l=Jt(o,t,r,e);return l?(n&&vp(n,e,l,i,t),!0):!1}function vp(n,e,t,r,i){let o=x.empty;for(let u=t.length-1;u>=0;u--)o=x.from(t[u].type.create(t[u].attrs,o));n.step(new q(e.start-(r?2:0),e.end,e.start,e.end,new w(o,0,0),t.length,!0));let s=0;for(let u=0;u<t.length;u++)t[u].type==i&&(s=u+1);let l=t.length-s,a=e.start+t.length-(r?2:0),c=e.parent;for(let u=e.startIndex,d=e.endIndex,f=!0;u<d;u++,f=!1)!f&&ke(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(u).nodeSize;return n}function vc(n){return function(e,t){let{$from:r,$to:i}=e.selection,o=r.blockRange(i,s=>s.childCount>0&&s.firstChild.type==n);return o?t?r.node(o.depth-1).type==n?Tp(e,t,n,o):Ep(e,t,o):!0:!1}}function Tp(n,e,t,r){let i=n.tr,o=r.end,s=r.$to.end(r.depth);o<s&&(i.step(new q(o-1,s,o,s,new w(x.from(t.create(null,r.parent.copy())),1,0),1,!0)),r=new St(i.doc.resolve(r.$from.pos),i.doc.resolve(s),r.depth));let l=qe(r);if(l==null)return!1;i.lift(r,l);let a=i.doc.resolve(i.mapping.map(o,-1)-1);return Me(i.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&i.join(a.pos),e(i.scrollIntoView()),!0}function Ep(n,e,t){let r=n.tr,i=t.parent;for(let h=t.end,p=t.endIndex-1,m=t.startIndex;p>m;p--)h-=i.child(p).nodeSize,r.delete(h-1,h+1);let o=r.doc.resolve(t.start),s=o.nodeAfter;if(r.mapping.map(t.end)!=t.start+o.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=o.node(-1),u=o.index(-1);if(!c.canReplace(u+(l?0:1),u+1,s.content.append(a?x.empty:x.from(i))))return!1;let d=o.pos,f=d+s.nodeSize;return r.step(new q(d-(l?1:0),f+(a?1:0),d+1,f-1,new w((l?x.empty:x.from(i.copy(x.empty))).append(a?x.empty:x.from(i.copy(x.empty))),l?0:1,a?0:1),l?0:1)),e(r.scrollIntoView()),!0}function Tc(n){return function(e,t){let{$from:r,$to:i}=e.selection,o=r.blockRange(i,c=>c.childCount>0&&c.firstChild.type==n);if(!o)return!1;let s=o.startIndex;if(s==0)return!1;let l=o.parent,a=l.child(s-1);if(a.type!=n)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,u=x.from(c?n.create():null),d=new w(x.from(n.create(null,x.from(l.type.create(null,u)))),c?3:1,0),f=o.start,h=o.end;t(e.tr.step(new q(f-(c?3:1),h,f,h,d,1,!0)).scrollIntoView())}return!0}}var Ap=Object.defineProperty,Ic=(n,e)=>{for(var t in e)Ap(n,t,{get:e[t],enumerable:!0})};function Fr(n){let{state:e,transaction:t}=n,{selection:r}=t,{doc:i}=t,{storedMarks:o}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return o},get selection(){return r},get doc(){return i},get tr(){return r=t.selection,i=t.doc,o=t.storedMarks,t}}}var zr=class{constructor(n){this.editor=n.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=n.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:n,editor:e,state:t}=this,{view:r}=e,{tr:i}=t,o=this.buildProps(i);return Object.fromEntries(Object.entries(n).map(([s,l])=>[s,(...c)=>{let u=l(...c)(o);return!i.getMeta("preventDispatch")&&!this.hasCustomState&&r.dispatch(i),u}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(n,e=!0){let{rawCommands:t,editor:r,state:i}=this,{view:o}=r,s=[],l=!!n,a=n||i.tr,c=()=>(!l&&e&&!a.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(a),s.every(d=>d===!0)),u={...Object.fromEntries(Object.entries(t).map(([d,f])=>[d,(...p)=>{let m=this.buildProps(a,e),g=f(...p)(m);return s.push(g),u}])),run:c};return u}createCan(n){let{rawCommands:e,state:t}=this,r=!1,i=n||t.tr,o=this.buildProps(i,r);return{...Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...c)=>a(...c)({...o,dispatch:void 0})])),chain:()=>this.createChain(i,r)}}buildProps(n,e=!0){let{rawCommands:t,editor:r,state:i}=this,{view:o}=r,s={tr:n,editor:r,view:o,state:Fr({state:i,transaction:n}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(n,e),can:()=>this.createCan(n),get commands(){return Object.fromEntries(Object.entries(t).map(([l,a])=>[l,(...c)=>a(...c)(s)]))}};return s}},Op=class{constructor(){this.callbacks={}}on(n,e){return this.callbacks[n]||(this.callbacks[n]=[]),this.callbacks[n].push(e),this}emit(n,...e){let t=this.callbacks[n];return t&&t.forEach(r=>r.apply(this,e)),this}off(n,e){let t=this.callbacks[n];return t&&(e?this.callbacks[n]=t.filter(r=>r!==e):delete this.callbacks[n]),this}once(n,e){let t=(...r)=>{this.off(n,t),e.apply(this,r)};return this.on(n,t)}removeAllListeners(){this.callbacks={}}};function ls(n,e){let t=new Ut(n);return e.forEach(r=>{r.steps.forEach(i=>{t.step(i)})}),t}var Lc=n=>{let e=n.childNodes;for(let t=e.length-1;t>=0;t-=1){let r=e[t];r.nodeType===3&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?n.removeChild(r):r.nodeType===1&&Lc(r)}return n};function Nr(n){if(typeof window>"u")throw new Error("[tiptap error]: there is no window object available, so this function cannot be used");let e=`<body>${n}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return Lc(t)}function Dn(n,e,t){if(n instanceof Q||n instanceof x)return n;t={slice:!0,parseOptions:{},...t};let r=typeof n=="object"&&n!==null,i=typeof n=="string";if(r)try{if(Array.isArray(n)&&n.length>0)return x.fromArray(n.map(l=>e.nodeFromJSON(l)));let s=e.nodeFromJSON(n);return t.errorOnInvalidContent&&s.check(),s}catch(o){if(t.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:o});return console.warn("[tiptap warn]: Invalid content.","Passed value:",n,"Error:",o),Dn("",e,t)}if(i){if(t.errorOnInvalidContent){let s=!1,l="",a=new Wt({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(s=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(t.slice?Le.fromSchema(a).parseSlice(Nr(n),t.parseOptions):Le.fromSchema(a).parse(Nr(n),t.parseOptions),t.errorOnInvalidContent&&s)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}let o=Le.fromSchema(e);return t.slice?o.parseSlice(Nr(n),t.parseOptions).content:o.parse(Nr(n),t.parseOptions)}return Dn("",e,t)}function is(n,e,t={},r={}){return Dn(n,e,{slice:!1,parseOptions:t,errorOnInvalidContent:r.errorOnInvalidContent})}function Np(n){for(let e=0;e<n.edgeCount;e+=1){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function Bc(n,e,t){let r=[];return n.nodesBetween(e.from,e.to,(i,o)=>{t(i)&&r.push({node:i,pos:o})}),r}function Dp(n,e){for(let t=n.depth;t>0;t-=1){let r=n.node(t);if(e(r))return{pos:t>0?n.before(t):0,start:n.start(t),depth:t,node:r}}}function Hr(n){return e=>Dp(e.$from,n)}function T(n,e,t){return n.config[e]===void 0&&n.parent?T(n.parent,e,t):typeof n.config[e]=="function"?n.config[e].bind({...t,parent:n.parent?T(n.parent,e,t):null}):n.config[e]}function as(n){return n.map(e=>{let t={name:e.name,options:e.options,storage:e.storage},r=T(e,"addExtensions",t);return r?[e,...as(r())]:e}).flat(10)}function cs(n,e){let t=_e.fromSchema(e).serializeFragment(n),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}function Fc(n){return typeof n=="function"}function V(n,e=void 0,...t){return Fc(n)?e?n.bind(e)(...t):n(...t):n}function Rp(n={}){return Object.keys(n).length===0&&n.constructor===Object}function Rn(n){let e=n.filter(i=>i.type==="extension"),t=n.filter(i=>i.type==="node"),r=n.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:r}}function zc(n){let e=[],{nodeExtensions:t,markExtensions:r}=Rn(n),i=[...t,...r],o={default:null,validate:void 0,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(s=>{let l={name:s.name,options:s.options,storage:s.storage,extensions:i},a=T(s,"addGlobalAttributes",l);if(!a)return;a().forEach(u=>{u.types.forEach(d=>{Object.entries(u.attributes).forEach(([f,h])=>{e.push({type:d,name:f,attribute:{...o,...h}})})})})}),i.forEach(s=>{let l={name:s.name,options:s.options,storage:s.storage},a=T(s,"addAttributes",l);if(!a)return;let c=a();Object.entries(c).forEach(([u,d])=>{let f={...o,...d};typeof f?.default=="function"&&(f.default=f.default()),f?.isRequired&&f?.default===void 0&&delete f.default,e.push({type:s.name,name:u,attribute:f})})}),e}function N(...n){return n.filter(e=>!!e).reduce((e,t)=>{let r={...e};return Object.entries(t).forEach(([i,o])=>{if(!r[i]){r[i]=o;return}if(i==="class"){let l=o?String(o).split(" "):[],a=r[i]?r[i].split(" "):[],c=l.filter(u=>!a.includes(u));r[i]=[...a,...c].join(" ")}else if(i==="style"){let l=o?o.split(";").map(u=>u.trim()).filter(Boolean):[],a=r[i]?r[i].split(";").map(u=>u.trim()).filter(Boolean):[],c=new Map;a.forEach(u=>{let[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),l.forEach(u=>{let[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),r[i]=Array.from(c.entries()).map(([u,d])=>`${u}: ${d}`).join("; ")}else r[i]=o}),r},{})}function Lr(n,e){return e.filter(t=>t.type===n.type.name).filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(n.attrs)||{}:{[t.name]:n.attrs[t.name]}).reduce((t,r)=>N(t,r),{})}function Pp(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function Ec(n,e){return"style"in n?n:{...n,getAttrs:t=>{let r=n.getAttrs?n.getAttrs(t):n.attrs;if(r===!1)return!1;let i=e.reduce((o,s)=>{let l=s.attribute.parseHTML?s.attribute.parseHTML(t):Pp(t.getAttribute(s.name));return l==null?o:{...o,[s.name]:l}},{});return{...r,...i}}}}function Ac(n){return Object.fromEntries(Object.entries(n).filter(([e,t])=>e==="attrs"&&Rp(t)?!1:t!=null))}function Ip(n,e){var t;let r=zc(n),{nodeExtensions:i,markExtensions:o}=Rn(n),s=(t=i.find(c=>T(c,"topNode")))==null?void 0:t.name,l=Object.fromEntries(i.map(c=>{let u=r.filter(y=>y.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((y,b)=>{let S=T(b,"extendNodeSchema",d);return{...y,...S?S(c):{}}},{}),h=Ac({...f,content:V(T(c,"content",d)),marks:V(T(c,"marks",d)),group:V(T(c,"group",d)),inline:V(T(c,"inline",d)),atom:V(T(c,"atom",d)),selectable:V(T(c,"selectable",d)),draggable:V(T(c,"draggable",d)),code:V(T(c,"code",d)),whitespace:V(T(c,"whitespace",d)),linebreakReplacement:V(T(c,"linebreakReplacement",d)),defining:V(T(c,"defining",d)),isolating:V(T(c,"isolating",d)),attrs:Object.fromEntries(u.map(y=>{var b,S;return[y.name,{default:(b=y?.attribute)==null?void 0:b.default,validate:(S=y?.attribute)==null?void 0:S.validate}]}))}),p=V(T(c,"parseHTML",d));p&&(h.parseDOM=p.map(y=>Ec(y,u)));let m=T(c,"renderHTML",d);m&&(h.toDOM=y=>m({node:y,HTMLAttributes:Lr(y,u)}));let g=T(c,"renderText",d);return g&&(h.toText=g),[c.name,h]})),a=Object.fromEntries(o.map(c=>{let u=r.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((g,y)=>{let b=T(y,"extendMarkSchema",d);return{...g,...b?b(c):{}}},{}),h=Ac({...f,inclusive:V(T(c,"inclusive",d)),excludes:V(T(c,"excludes",d)),group:V(T(c,"group",d)),spanning:V(T(c,"spanning",d)),code:V(T(c,"code",d)),attrs:Object.fromEntries(u.map(g=>{var y,b;return[g.name,{default:(y=g?.attribute)==null?void 0:y.default,validate:(b=g?.attribute)==null?void 0:b.validate}]}))}),p=V(T(c,"parseHTML",d));p&&(h.parseDOM=p.map(g=>Ec(g,u)));let m=T(c,"renderHTML",d);return m&&(h.toDOM=g=>m({mark:g,HTMLAttributes:Lr(g,u)})),[c.name,h]}));return new Wt({topNode:s,nodes:l,marks:a})}function Lp(n){let e=n.filter((t,r)=>n.indexOf(t)!==r);return Array.from(new Set(e))}function us(n){return n.sort((t,r)=>{let i=T(t,"priority")||100,o=T(r,"priority")||100;return i>o?-1:i<o?1:0})}function Hc(n){let e=us(as(n)),t=Lp(e.map(r=>r.name));return t.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${t.map(r=>`'${r}'`).join(", ")}]. This can lead to issues.`),e}function $c(n,e,t){let{from:r,to:i}=e,{blockSeparator:o=`

`,textSerializers:s={}}=t||{},l="";return n.nodesBetween(r,i,(a,c,u,d)=>{var f;a.isBlock&&c>r&&(l+=o);let h=s?.[a.type.name];if(h)return u&&(l+=h({node:a,pos:c,parent:u,index:d,range:e})),!1;a.isText&&(l+=(f=a?.text)==null?void 0:f.slice(Math.max(r,c)-c,i-c))}),l}function Bp(n,e){let t={from:0,to:n.content.size};return $c(n,t,e)}function Vc(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}function et(n,e){if(typeof n=="string"){if(!e.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return e.marks[n]}return n}function jc(n,e){let t=et(e,n.schema),{from:r,to:i,empty:o}=n.selection,s=[];o?(n.storedMarks&&s.push(...n.storedMarks),s.push(...n.selection.$head.marks())):n.doc.nodesBetween(r,i,a=>{s.push(...a.marks)});let l=s.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function K(n,e){if(typeof n=="string"){if(!e.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return e.nodes[n]}return n}function Fp(n,e){let t=K(e,n.schema),{from:r,to:i}=n.selection,o=[];n.doc.nodesBetween(r,i,l=>{o.push(l)});let s=o.reverse().find(l=>l.type.name===t.name);return s?{...s.attrs}:{}}function $r(n,e){return e.nodes[n]?"node":e.marks[n]?"mark":null}function ds(n,e){let t=$r(typeof e=="string"?e:e.name,n.schema);return t==="node"?Fp(n,e):t==="mark"?jc(n,e):{}}function zp(n,e=JSON.stringify){let t={};return n.filter(r=>{let i=e(r);return Object.prototype.hasOwnProperty.call(t,i)?!1:t[i]=!0})}function Hp(n){let e=zp(n);return e.length===1?e:e.filter((t,r)=>!e.filter((o,s)=>s!==r).some(o=>t.oldRange.from>=o.oldRange.from&&t.oldRange.to<=o.oldRange.to&&t.newRange.from>=o.newRange.from&&t.newRange.to<=o.newRange.to))}function fs(n){let{mapping:e,steps:t}=n,r=[];return e.maps.forEach((i,o)=>{let s=[];if(i.ranges.length)i.forEach((l,a)=>{s.push({from:l,to:a})});else{let{from:l,to:a}=t[o];if(l===void 0||a===void 0)return;s.push({from:l,to:a})}s.forEach(({from:l,to:a})=>{let c=e.slice(o).map(l,-1),u=e.slice(o).map(a),d=e.invert().map(c,-1),f=e.invert().map(u);r.push({oldRange:{from:d,to:f},newRange:{from:c,to:u}})})}),Hp(r)}function hs(n){return Object.prototype.toString.call(n)==="[object RegExp]"}function Br(n,e,t={strict:!0}){let r=Object.keys(e);return r.length?r.every(i=>t.strict?e[i]===n[i]:hs(e[i])?e[i].test(n[i]):e[i]===n[i]):!0}function Wc(n,e,t={}){return n.find(r=>r.type===e&&Br(Object.fromEntries(Object.keys(t).map(i=>[i,r.attrs[i]])),t))}function Oc(n,e,t={}){return!!Wc(n,e,t)}function ps(n,e,t){var r;if(!n||!e)return;let i=n.parent.childAfter(n.parentOffset);if((!i.node||!i.node.marks.some(u=>u.type===e))&&(i=n.parent.childBefore(n.parentOffset)),!i.node||!i.node.marks.some(u=>u.type===e)||(t=t||((r=i.node.marks[0])==null?void 0:r.attrs),!Wc([...i.node.marks],e,t)))return;let s=i.index,l=n.start()+i.offset,a=s+1,c=l+i.node.nodeSize;for(;s>0&&Oc([...n.parent.child(s-1).marks],e,t);)s-=1,l-=n.parent.child(s).nodeSize;for(;a<n.parent.childCount&&Oc([...n.parent.child(a).marks],e,t);)c+=n.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function Vr(n,e,t){let r=[];return n===e?t.resolve(n).marks().forEach(i=>{let o=t.resolve(n),s=ps(o,i.type);s&&r.push({mark:i,...s})}):t.nodesBetween(n,e,(i,o)=>{!i||i?.nodeSize===void 0||r.push(...i.marks.map(s=>({from:o,to:o+i.nodeSize,mark:s})))}),r}var Kc=(n,e,t,r=20)=>{let i=n.doc.resolve(t),o=r,s=null;for(;o>0&&s===null;){let l=i.node(o);l?.type.name===e?s=l:o-=1}return[s,o]};function ts(n,e){return e.nodes[n]||e.marks[n]||null}function Ir(n,e,t){return Object.fromEntries(Object.entries(t).filter(([r])=>{let i=n.find(o=>o.type===e&&o.name===r);return i?i.attribute.keepOnSplit:!1}))}var $p=(n,e=500)=>{let t="",r=n.parentOffset;return n.parent.nodesBetween(Math.max(0,r-e),r,(i,o,s,l)=>{var a,c;let u=((c=(a=i.type.spec).toText)==null?void 0:c.call(a,{node:i,pos:o,parent:s,index:l}))||i.textContent||"%leaf%";t+=i.isAtom&&!i.isText?u:u.slice(0,Math.max(0,r-o))}),t};function ss(n,e,t={}){let{empty:r,ranges:i}=n.selection,o=e?et(e,n.schema):null;if(r)return!!(n.storedMarks||n.selection.$from.marks()).filter(d=>o?o.name===d.type.name:!0).find(d=>Br(d.attrs,t,{strict:!1}));let s=0,l=[];if(i.forEach(({$from:d,$to:f})=>{let h=d.pos,p=f.pos;n.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;let y=Math.max(h,g),b=Math.min(p,g+m.nodeSize),S=b-y;s+=S,l.push(...m.marks.map(C=>({mark:C,from:y,to:b})))})}),s===0)return!1;let a=l.filter(d=>o?o.name===d.mark.type.name:!0).filter(d=>Br(d.mark.attrs,t,{strict:!1})).reduce((d,f)=>d+f.to-f.from,0),c=l.filter(d=>o?d.mark.type!==o&&d.mark.type.excludes(o):!0).reduce((d,f)=>d+f.to-f.from,0);return(a>0?a+c:a)>=s}function ze(n,e,t={}){let{from:r,to:i,empty:o}=n.selection,s=e?K(e,n.schema):null,l=[];n.doc.nodesBetween(r,i,(d,f)=>{if(d.isText)return;let h=Math.max(r,f),p=Math.min(i,f+d.nodeSize);l.push({node:d,from:h,to:p})});let a=i-r,c=l.filter(d=>s?s.name===d.node.type.name:!0).filter(d=>Br(d.node.attrs,t,{strict:!1}));return o?!!c.length:c.reduce((d,f)=>d+f.to-f.from,0)>=a}function Vp(n,e,t={}){if(!e)return ze(n,null,t)||ss(n,null,t);let r=$r(e,n.schema);return r==="node"?ze(n,e,t):r==="mark"?ss(n,e,t):!1}var _c=(n,e)=>{let{$from:t,$to:r,$anchor:i}=n.selection;if(e){let o=Hr(l=>l.type.name===e)(n.selection);if(!o)return!1;let s=n.doc.resolve(o.pos+1);return i.pos+1===s.end()}return!(r.parentOffset<r.parent.nodeSize-2||t.pos!==r.pos)},Uc=n=>{let{$from:e,$to:t}=n.selection;return!(e.parentOffset>0||e.pos!==t.pos)};function Nc(n,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===n.name):e}function Dc(n,e){let{nodeExtensions:t}=Rn(e),r=t.find(s=>s.name===n);if(!r)return!1;let i={name:r.name,options:r.options,storage:r.storage},o=V(T(r,"group",i));return typeof o!="string"?!1:o.split(" ").includes("list")}function Pn(n,{checkChildren:e=!0,ignoreWhitespace:t=!1}={}){var r;if(t){if(n.type.name==="hardBreak")return!0;if(n.isText)return/^\s*$/m.test((r=n.text)!=null?r:"")}if(n.isText)return!n.text;if(n.isAtom||n.isLeaf)return!1;if(n.content.childCount===0)return!0;if(e){let i=!0;return n.content.forEach(o=>{i!==!1&&(Pn(o,{ignoreWhitespace:t,checkChildren:e})||(i=!1))}),i}return!1}function jr(n){return n instanceof v}function Jc(n){return n instanceof A}function Pt(n=0,e=0,t=0){return Math.min(Math.max(n,e),t)}function qc(n,e=null){if(!e)return null;let t=D.atStart(n),r=D.atEnd(n);if(e==="start"||e===!0)return t;if(e==="end")return r;let i=t.from,o=r.to;return e==="all"?A.create(n,Pt(0,i,o),Pt(n.content.size,i,o)):A.create(n,Pt(e,i,o),Pt(e,i,o))}function jp(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof oe||i instanceof q))return;let o=n.mapping.maps[r],s=0;o.forEach((l,a,c,u)=>{s===0&&(s=u)}),n.setSelection(D.near(n.doc.resolve(s),t))}var Wr=class{constructor(n){this.find=n.find,this.handler=n.handler}},Wp=(n,e)=>{if(hs(e))return e.exec(n);let t=e(n);if(!t)return null;let r=[t.text];return r.index=t.index,r.input=n,r.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(t.replaceWith)),r};function Dr(n){var e;let{editor:t,from:r,to:i,text:o,rules:s,plugin:l}=n,{view:a}=t;if(a.composing)return!1;let c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(e=c.nodeBefore||c.nodeAfter)!=null&&e.marks.find(f=>f.type.spec.code))return!1;let u=!1,d=$p(c)+o;return s.forEach(f=>{if(u)return;let h=Wp(d,f.find);if(!h)return;let p=a.state.tr,m=Fr({state:a.state,transaction:p}),g={from:r-(h[0].length-o.length),to:i},{commands:y,chain:b,can:S}=new zr({editor:t,state:m});f.handler({state:m,range:g,match:h,commands:y,chain:b,can:S})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:r,to:i,text:o}),a.dispatch(p),u=!0)}),u}function Kp(n){let{editor:e,rules:t}=n,r=new O({state:{init(){return null},apply(i,o,s){let l=i.getMeta(r);if(l)return l;let a=i.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:u}=a;typeof u=="string"?u=u:u=cs(x.from(u),s.schema);let{from:d}=a,f=d+u.length;Dr({editor:e,from:d,to:f,text:u,rules:t,plugin:r})}),i.selectionSet||i.docChanged?null:o}},props:{handleTextInput(i,o,s,l){return Dr({editor:e,from:o,to:s,text:l,rules:t,plugin:r})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{let{$cursor:o}=i.state.selection;o&&Dr({editor:e,from:o.pos,to:o.pos,text:"",rules:t,plugin:r})}),!1)},handleKeyDown(i,o){if(o.key!=="Enter")return!1;let{$cursor:s}=i.state.selection;return s?Dr({editor:e,from:s.pos,to:s.pos,text:`
`,rules:t,plugin:r}):!1}},isInputRules:!0});return r}function _p(n){return Object.prototype.toString.call(n).slice(8,-1)}function Rr(n){return _p(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function Gc(n,e){let t={...n};return Rr(n)&&Rr(e)&&Object.keys(e).forEach(r=>{Rr(e[r])&&Rr(n[r])?t[r]=Gc(n[r],e[r]):t[r]=e[r]}),t}var ms=class{constructor(n={}){this.type="extendable",this.parent=null,this.child=null,this.name="",this.config={name:this.name},this.config={...this.config,...n},this.name=this.config.name}get options(){return{...V(T(this,"addOptions",{name:this.name}))||{}}}get storage(){return{...V(T(this,"addStorage",{name:this.name,options:this.options}))||{}}}configure(n={}){let e=this.extend({...this.config,addOptions:()=>Gc(this.options,n)});return e.name=this.name,e.parent=this.parent,e}extend(n={}){let e=new this.constructor({...this.config,...n});return e.parent=this,this.child=e,e.name="name"in n?n.name:e.parent.name,e}},U=class Yc extends ms{constructor(){super(...arguments),this.type="mark"}static create(e={}){let t=typeof e=="function"?e():e;return new Yc(t)}static handleExit({editor:e,mark:t}){let{tr:r}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){let s=i.marks();if(!!!s.find(c=>c?.type.name===t.name))return!1;let a=s.find(c=>c?.type.name===t.name);return a&&r.removeStoredMark(a),r.insertText(" ",i.pos),e.view.dispatch(r),!0}return!1}configure(e){return super.configure(e)}extend(e){let t=typeof e=="function"?e():e;return super.extend(t)}};function Up(n){return typeof n=="number"}var Jp=class{constructor(n){this.find=n.find,this.handler=n.handler}},qp=(n,e,t)=>{if(hs(e))return[...n.matchAll(e)];let r=e(n,t);return r?r.map(i=>{let o=[i.text];return o.index=i.index,o.input=n,o.data=i.data,i.replaceWith&&(i.text.includes(i.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),o.push(i.replaceWith)),o}):[]};function Gp(n){let{editor:e,state:t,from:r,to:i,rule:o,pasteEvent:s,dropEvent:l}=n,{commands:a,chain:c,can:u}=new zr({editor:e,state:t}),d=[];return t.doc.nodesBetween(r,i,(h,p)=>{if(!h.isTextblock||h.type.spec.code)return;let m=Math.max(r,p),g=Math.min(i,p+h.content.size),y=h.textBetween(m-p,g-p,void 0,"\uFFFC");qp(y,o.find,s).forEach(S=>{if(S.index===void 0)return;let C=m+S.index+1,M=C+S[0].length,L={from:t.tr.mapping.map(C),to:t.tr.mapping.map(M)},$=o.handler({state:t,range:L,match:S,commands:a,chain:c,can:u,pasteEvent:s,dropEvent:l});d.push($)})}),d.every(h=>h!==null)}var Pr=null,Yp=n=>{var e;let t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)==null||e.setData("text/html",n),t};function Xp(n){let{editor:e,rules:t}=n,r=null,i=!1,o=!1,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}let a=({state:u,from:d,to:f,rule:h,pasteEvt:p})=>{let m=u.tr,g=Fr({state:u,transaction:m});if(!(!Gp({editor:e,state:g,from:Math.max(d-1,0),to:f.b-1,rule:h,pasteEvent:p,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m}};return t.map(u=>new O({view(d){let f=p=>{var m;r=(m=d.dom.parentElement)!=null&&m.contains(p.target)?d.dom.parentElement:null,r&&(Pr=e)},h=()=>{Pr&&(Pr=null)};return window.addEventListener("dragstart",f),window.addEventListener("dragend",h),{destroy(){window.removeEventListener("dragstart",f),window.removeEventListener("dragend",h)}}},props:{handleDOMEvents:{drop:(d,f)=>{if(o=r===d.dom.parentElement,l=f,!o){let h=Pr;h&&setTimeout(()=>{let p=h.state.selection;p&&h.commands.deleteRange({from:p.from,to:p.to})},10)}return!1},paste:(d,f)=>{var h;let p=(h=f.clipboardData)==null?void 0:h.getData("text/html");return s=f,i=!!p?.includes("data-pm-slice"),!1}}},appendTransaction:(d,f,h)=>{let p=d[0],m=p.getMeta("uiEvent")==="paste"&&!i,g=p.getMeta("uiEvent")==="drop"&&!o,y=p.getMeta("applyPasteRules"),b=!!y;if(!m&&!g&&!b)return;if(b){let{text:M}=y;typeof M=="string"?M=M:M=cs(x.from(M),h.schema);let{from:L}=y,$=L+M.length,E=Yp(M);return a({rule:u,state:h,from:L,to:{b:$},pasteEvt:E})}let S=f.doc.content.findDiffStart(h.doc.content),C=f.doc.content.findDiffEnd(h.doc.content);if(!(!Up(S)||!C||S===C.b))return a({rule:u,state:h,from:S,to:C,pasteEvt:s})}}))}var Kr=class{constructor(n,e){this.splittableMarks=[],this.editor=e,this.extensions=Hc(n),this.schema=Ip(this.extensions,e),this.setupExtensions()}get commands(){return this.extensions.reduce((n,e)=>{let t={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:ts(e.name,this.schema)},r=T(e,"addCommands",t);return r?{...n,...r()}:n},{})}get plugins(){let{editor:n}=this,e=us([...this.extensions].reverse()),t=[],r=[],i=e.map(o=>{let s={name:o.name,options:o.options,storage:this.editor.extensionStorage[o.name],editor:n,type:ts(o.name,this.schema)},l=[],a=T(o,"addKeyboardShortcuts",s),c={};if(o.type==="mark"&&T(o,"exitable",s)&&(c.ArrowRight=()=>U.handleExit({editor:n,mark:o})),a){let p=Object.fromEntries(Object.entries(a()).map(([m,g])=>[m,()=>g({editor:n})]));c={...c,...p}}let u=uc(c);l.push(u);let d=T(o,"addInputRules",s);Nc(o,n.options.enableInputRules)&&d&&t.push(...d());let f=T(o,"addPasteRules",s);Nc(o,n.options.enablePasteRules)&&f&&r.push(...f());let h=T(o,"addProseMirrorPlugins",s);if(h){let p=h();l.push(...p)}return l}).flat();return[Kp({editor:n,rules:t}),...Xp({editor:n,rules:r}),...i]}get attributes(){return zc(this.extensions)}get nodeViews(){let{editor:n}=this,{nodeExtensions:e}=Rn(this.extensions);return Object.fromEntries(e.filter(t=>!!T(t,"addNodeView")).map(t=>{let r=this.attributes.filter(l=>l.type===t.name),i={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:n,type:K(t.name,this.schema)},o=T(t,"addNodeView",i);if(!o)return[];let s=(l,a,c,u,d)=>{let f=Lr(l,r);return o()({node:l,view:a,getPos:c,decorations:u,innerDecorations:d,editor:n,extension:t,HTMLAttributes:f})};return[t.name,s]}))}get markViews(){let{editor:n}=this,{markExtensions:e}=Rn(this.extensions);return Object.fromEntries(e.filter(t=>!!T(t,"addMarkView")).map(t=>{let r=this.attributes.filter(l=>l.type===t.name),i={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:n,type:et(t.name,this.schema)},o=T(t,"addMarkView",i);if(!o)return[];let s=(l,a,c)=>{let u=Lr(l,r);return o()({mark:l,view:a,inline:c,editor:n,extension:t,HTMLAttributes:u})};return[t.name,s]}))}setupExtensions(){let n=this.extensions;this.editor.extensionStorage=Object.fromEntries(n.map(e=>[e.name,e.storage])),n.forEach(e=>{var t;let r={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:ts(e.name,this.schema)};e.type==="mark"&&((t=V(T(e,"keepOnSplit",r)))==null||t)&&this.splittableMarks.push(e.name);let i=T(e,"onBeforeCreate",r),o=T(e,"onCreate",r),s=T(e,"onUpdate",r),l=T(e,"onSelectionUpdate",r),a=T(e,"onTransaction",r),c=T(e,"onFocus",r),u=T(e,"onBlur",r),d=T(e,"onDestroy",r);i&&this.editor.on("beforeCreate",i),o&&this.editor.on("create",o),s&&this.editor.on("update",s),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),u&&this.editor.on("blur",u),d&&this.editor.on("destroy",d)})}};Kr.resolve=Hc;Kr.sort=us;Kr.flatten=as;var Qp={};Ic(Qp,{ClipboardTextSerializer:()=>Qc,Commands:()=>tu,Delete:()=>nu,Drop:()=>ru,Editable:()=>iu,FocusEvents:()=>su,Keymap:()=>lu,Paste:()=>au,Tabindex:()=>cu,focusEventsPluginKey:()=>ou});var z=class Xc extends ms{constructor(){super(...arguments),this.type="extension"}static create(e={}){let t=typeof e=="function"?e():e;return new Xc(t)}configure(e){return super.configure(e)}extend(e){let t=typeof e=="function"?e():e;return super.extend(t)}},Qc=z.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new O({key:new P("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:n}=this,{state:e,schema:t}=n,{doc:r,selection:i}=e,{ranges:o}=i,s=Math.min(...o.map(u=>u.$from.pos)),l=Math.max(...o.map(u=>u.$to.pos)),a=Vc(t);return $c(r,{from:s,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),Zc={};Ic(Zc,{blur:()=>Zp,clearContent:()=>em,clearNodes:()=>tm,command:()=>nm,createParagraphNear:()=>rm,cut:()=>im,deleteCurrentNode:()=>om,deleteNode:()=>sm,deleteRange:()=>lm,deleteSelection:()=>am,enter:()=>cm,exitCode:()=>um,extendMarkRange:()=>dm,first:()=>fm,focus:()=>pm,forEach:()=>mm,insertContent:()=>gm,insertContentAt:()=>bm,joinBackward:()=>Sm,joinDown:()=>km,joinForward:()=>wm,joinItemBackward:()=>Mm,joinItemForward:()=>Cm,joinTextblockBackward:()=>vm,joinTextblockForward:()=>Tm,joinUp:()=>xm,keyboardShortcut:()=>Am,lift:()=>Om,liftEmptyBlock:()=>Nm,liftListItem:()=>Dm,newlineInCode:()=>Rm,resetAttributes:()=>Pm,scrollIntoView:()=>Im,selectAll:()=>Lm,selectNodeBackward:()=>Bm,selectNodeForward:()=>Fm,selectParentNode:()=>zm,selectTextblockEnd:()=>Hm,selectTextblockStart:()=>$m,setContent:()=>Vm,setMark:()=>Wm,setMeta:()=>Km,setNode:()=>_m,setNodeSelection:()=>Um,setTextSelection:()=>Jm,sinkListItem:()=>qm,splitBlock:()=>Gm,splitListItem:()=>Ym,toggleList:()=>Xm,toggleMark:()=>Qm,toggleNode:()=>Zm,toggleWrap:()=>eg,undoInputRule:()=>tg,unsetAllMarks:()=>ng,unsetMark:()=>rg,updateAttributes:()=>ig,wrapIn:()=>og,wrapInList:()=>sg});var Zp=()=>({editor:n,view:e})=>(requestAnimationFrame(()=>{var t;n.isDestroyed||(e.dom.blur(),(t=window?.getSelection())==null||t.removeAllRanges())}),!0),em=(n=!0)=>({commands:e})=>e.setContent("",{emitUpdate:n}),tm=()=>({state:n,tr:e,dispatch:t})=>{let{selection:r}=e,{ranges:i}=r;return t&&i.forEach(({$from:o,$to:s})=>{n.doc.nodesBetween(o.pos,s.pos,(l,a)=>{if(l.type.isText)return;let{doc:c,mapping:u}=e,d=c.resolve(u.map(a)),f=c.resolve(u.map(a+l.nodeSize)),h=d.blockRange(f);if(!h)return;let p=qe(h);if(l.type.isTextblock){let{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},nm=n=>e=>n(e),rm=()=>({state:n,dispatch:e})=>Go(n,e),im=(n,e)=>({editor:t,tr:r})=>{let{state:i}=t,o=i.doc.slice(n.from,n.to);r.deleteRange(n.from,n.to);let s=r.mapping.map(e);return r.insert(s,o.content),r.setSelection(new A(r.doc.resolve(s-1))),!0},om=()=>({tr:n,dispatch:e})=>{let{selection:t}=n,r=t.$anchor.node();if(r.content.size>0)return!1;let i=n.selection.$anchor;for(let o=i.depth;o>0;o-=1)if(i.node(o).type===r.type){if(e){let l=i.before(o),a=i.after(o);n.delete(l,a).scrollIntoView()}return!0}return!1},sm=n=>({tr:e,state:t,dispatch:r})=>{let i=K(n,t.schema),o=e.selection.$anchor;for(let s=o.depth;s>0;s-=1)if(o.node(s).type===i){if(r){let a=o.before(s),c=o.after(s);e.delete(a,c).scrollIntoView()}return!0}return!1},lm=n=>({tr:e,dispatch:t})=>{let{from:r,to:i}=n;return t&&e.delete(r,i),!0},am=()=>({state:n,dispatch:e})=>Or(n,e),cm=()=>({commands:n})=>n.keyboardShortcut("Enter"),um=()=>({state:n,dispatch:e})=>qo(n,e),dm=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let o=et(n,r.schema),{doc:s,selection:l}=t,{$from:a,from:c,to:u}=l;if(i){let d=ps(a,o,e);if(d&&d.from<=c&&d.to>=u){let f=A.create(s,d.from,d.to);t.setSelection(f)}}return!0},fm=n=>e=>{let t=typeof n=="function"?n(e):n;for(let r=0;r<t.length;r+=1)if(t[r](e))return!0;return!1};function hm(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function gs(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}var pm=(n=null,e={})=>({editor:t,view:r,tr:i,dispatch:o})=>{e={scrollIntoView:!0,...e};let s=()=>{(gs()||hm())&&r.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(r.focus(),e?.scrollIntoView&&t.commands.scrollIntoView())})};if(r.hasFocus()&&n===null||n===!1)return!0;if(o&&n===null&&!Jc(t.state.selection))return s(),!0;let l=qc(i.doc,n)||t.state.selection,a=t.state.selection.eq(l);return o&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),s()),!0},mm=(n,e)=>t=>n.every((r,i)=>e(r,{...t,index:i})),gm=(n,e)=>({tr:t,commands:r})=>r.insertContentAt({from:t.selection.from,to:t.selection.to},n,e),ym=n=>!("type"in n),bm=(n,e,t)=>({tr:r,dispatch:i,editor:o})=>{var s;if(i){t={parseOptions:o.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};let l,{selection:a}=o.state,c=y=>{o.emit("contentError",{editor:o,error:y,disableCollaboration:()=>{"collaboration"in o.storage&&typeof o.storage.collaboration=="object"&&o.storage.collaboration&&(o.storage.collaboration.isDisabled=!0)}})},u={preserveWhitespace:"full",...t.parseOptions};if(!t.errorOnInvalidContent&&!o.options.enableContentCheck&&o.options.emitContentError)try{Dn(e,o.schema,{parseOptions:u,errorOnInvalidContent:!0})}catch(y){c(y)}try{l=Dn(e,o.schema,{parseOptions:u,errorOnInvalidContent:(s=t.errorOnInvalidContent)!=null?s:o.options.enableContentCheck})}catch(y){return c(y),!1}let{from:d,to:f}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},h=!0,p=!0;if((ym(l)?l:[l]).forEach(y=>{y.check(),h=h?y.isText&&y.marks.length===0:!1,p=p?y.isBlock:!1}),d===f&&p){let{parent:y}=r.doc.resolve(d);y.isTextblock&&!y.type.spec.code&&!y.childCount&&(d-=1,f+=1)}let g;if(h){if(Array.isArray(e))g=e.map(y=>y.text||"").join("");else if(e instanceof x){let y="";e.forEach(b=>{b.text&&(y+=b.text)}),g=y}else typeof e=="object"&&e&&e.text?g=e.text:g=e;r.insertText(g,d,f)}else{g=l;let y=a.$from.parentOffset===0,b=a.$from.node().isText||a.$from.node().isTextblock,S=a.$from.node().content.size>0;y&&b&&S&&(d=Math.max(0,d-1)),r.replaceWith(d,f,g)}t.updateSelection&&jp(r,r.steps.length-1,-1),t.applyInputRules&&r.setMeta("applyInputRules",{from:d,text:g}),t.applyPasteRules&&r.setMeta("applyPasteRules",{from:d,text:g})}return!0},xm=()=>({state:n,dispatch:e})=>yc(n,e),km=()=>({state:n,dispatch:e})=>bc(n,e),Sm=()=>({state:n,dispatch:e})=>$o(n,e),wm=()=>({state:n,dispatch:e})=>Wo(n,e),Mm=()=>({state:n,dispatch:e,tr:t})=>{try{let r=Mt(n.doc,n.selection.$from.pos,-1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},Cm=()=>({state:n,dispatch:e,tr:t})=>{try{let r=Mt(n.doc,n.selection.$from.pos,1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},vm=()=>({state:n,dispatch:e})=>hc(n,e),Tm=()=>({state:n,dispatch:e})=>pc(n,e);function eu(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function Em(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t==="Space"&&(t=" ");let r,i,o,s;for(let l=0;l<e.length-1;l+=1){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))o=!0;else if(/^mod$/i.test(a))gs()||eu()?s=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return r&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),s&&(t=`Meta-${t}`),o&&(t=`Shift-${t}`),t}var Am=n=>({editor:e,view:t,tr:r,dispatch:i})=>{let o=Em(n).split(/-(?!$)/),s=o.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:s==="Space"?" ":s,altKey:o.includes("Alt"),ctrlKey:o.includes("Ctrl"),metaKey:o.includes("Meta"),shiftKey:o.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a?.steps.forEach(c=>{let u=c.map(r.mapping);u&&i&&r.maybeStep(u)}),!0},Om=(n,e={})=>({state:t,dispatch:r})=>{let i=K(n,t.schema);return ze(t,i,e)?xc(t,r):!1},Nm=()=>({state:n,dispatch:e})=>Yo(n,e),Dm=n=>({state:e,dispatch:t})=>{let r=K(n,e.schema);return vc(r)(e,t)},Rm=()=>({state:n,dispatch:e})=>Uo(n,e);function Rc(n,e){let t=typeof e=="string"?[e]:e;return Object.keys(n).reduce((r,i)=>(t.includes(i)||(r[i]=n[i]),r),{})}var Pm=(n,e)=>({tr:t,state:r,dispatch:i})=>{let o=null,s=null,l=$r(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(o=K(n,r.schema)),l==="mark"&&(s=et(n,r.schema)),i&&t.selection.ranges.forEach(a=>{r.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,u)=>{o&&o===c.type&&t.setNodeMarkup(u,void 0,Rc(c.attrs,e)),s&&c.marks.length&&c.marks.forEach(d=>{s===d.type&&t.addMark(u,u+c.nodeSize,s.create(Rc(d.attrs,e)))})})}),!0):!1},Im=()=>({tr:n,dispatch:e})=>(e&&n.scrollIntoView(),!0),Lm=()=>({tr:n,dispatch:e})=>{if(e){let t=new de(n.doc);n.setSelection(t)}return!0},Bm=()=>({state:n,dispatch:e})=>Vo(n,e),Fm=()=>({state:n,dispatch:e})=>Ko(n,e),zm=()=>({state:n,dispatch:e})=>kc(n,e),Hm=()=>({state:n,dispatch:e})=>Qo(n,e),$m=()=>({state:n,dispatch:e})=>Xo(n,e),Vm=(n,{errorOnInvalidContent:e,emitUpdate:t=!0,parseOptions:r={}}={})=>({editor:i,tr:o,dispatch:s,commands:l})=>{let{doc:a}=o;if(r.preserveWhitespace!=="full"){let c=is(n,i.schema,r,{errorOnInvalidContent:e??i.options.enableContentCheck});return s&&o.replaceWith(0,a.content.size,c).setMeta("preventUpdate",!t),!0}return s&&o.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:a.content.size},n,{parseOptions:r,errorOnInvalidContent:e??i.options.enableContentCheck})};function jm(n,e,t){var r;let{selection:i}=e,o=null;if(Jc(i)&&(o=i.$cursor),o){let l=(r=n.storedMarks)!=null?r:o.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}let{ranges:s}=i;return s.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(t):!1;return n.doc.nodesBetween(l.pos,a.pos,(u,d,f)=>{if(c)return!1;if(u.isInline){let h=!f||f.type.allowsMarkType(t),p=!!t.isInSet(u.marks)||!u.marks.some(m=>m.type.excludes(t));c=h&&p}return!c}),c})}var Wm=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let{selection:o}=t,{empty:s,ranges:l}=o,a=et(n,r.schema);if(i)if(s){let c=jc(r,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{let u=c.$from.pos,d=c.$to.pos;r.doc.nodesBetween(u,d,(f,h)=>{let p=Math.max(h,u),m=Math.min(h+f.nodeSize,d);f.marks.find(y=>y.type===a)?f.marks.forEach(y=>{a===y.type&&t.addMark(p,m,a.create({...y.attrs,...e}))}):t.addMark(p,m,a.create(e))})});return jm(r,t,a)},Km=(n,e)=>({tr:t})=>(t.setMeta(n,e),!0),_m=(n,e={})=>({state:t,dispatch:r,chain:i})=>{let o=K(n,t.schema),s;return t.selection.$anchor.sameParent(t.selection.$head)&&(s=t.selection.$anchor.parent.attrs),o.isTextblock?i().command(({commands:l})=>Zo(o,{...s,...e})(t)?!0:l.clearNodes()).command(({state:l})=>Zo(o,{...s,...e})(l,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},Um=n=>({tr:e,dispatch:t})=>{if(t){let{doc:r}=e,i=Pt(n,0,r.content.size),o=v.create(r,i);e.setSelection(o)}return!0},Jm=n=>({tr:e,dispatch:t})=>{if(t){let{doc:r}=e,{from:i,to:o}=typeof n=="number"?{from:n,to:n}:n,s=A.atStart(r).from,l=A.atEnd(r).to,a=Pt(i,s,l),c=Pt(o,s,l),u=A.create(r,a,c);e.setSelection(u)}return!0},qm=n=>({state:e,dispatch:t})=>{let r=K(n,e.schema);return Tc(r)(e,t)};function Pc(n,e){let t=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(t){let r=t.filter(i=>e?.includes(i.type.name));n.tr.ensureMarks(r)}}var Gm=({keepMarks:n=!0}={})=>({tr:e,state:t,dispatch:r,editor:i})=>{let{selection:o,doc:s}=e,{$from:l,$to:a}=o,c=i.extensionManager.attributes,u=Ir(c,l.node().type.name,l.node().attrs);if(o instanceof v&&o.node.isBlock)return!l.parentOffset||!ke(s,l.pos)?!1:(r&&(n&&Pc(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;let d=a.parentOffset===a.parent.content.size,f=l.depth===0?void 0:Np(l.node(-1).contentMatchAt(l.indexAfter(-1))),h=d&&f?[{type:f,attrs:u}]:void 0,p=ke(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&ke(e.doc,e.mapping.map(l.pos),1,f?[{type:f}]:void 0)&&(p=!0,h=f?[{type:f,attrs:u}]:void 0),r){if(p&&(o instanceof A&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,h),f&&!d&&!l.parentOffset&&l.parent.type!==f)){let m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,f)&&e.setNodeMarkup(e.mapping.map(l.before()),f)}n&&Pc(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return p},Ym=(n,e={})=>({tr:t,state:r,dispatch:i,editor:o})=>{var s;let l=K(n,r.schema),{$from:a,$to:c}=r.selection,u=r.selection.node;if(u&&u.isBlock||a.depth<2||!a.sameParent(c))return!1;let d=a.node(-1);if(d.type!==l)return!1;let f=o.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let y=x.empty,b=a.index(-1)?1:a.index(-2)?2:3;for(let E=a.depth-b;E>=a.depth-3;E-=1)y=x.from(a.node(E).copy(y));let S=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,C={...Ir(f,a.node().type.name,a.node().attrs),...e},M=((s=l.contentMatch.defaultType)==null?void 0:s.createAndFill(C))||void 0;y=y.append(x.from(l.createAndFill(null,M)||void 0));let L=a.before(a.depth-(b-1));t.replace(L,a.after(-S),new w(y,4-b,0));let $=-1;t.doc.nodesBetween(L,t.doc.content.size,(E,F)=>{if($>-1)return!1;E.isTextblock&&E.content.size===0&&($=F+1)}),$>-1&&t.setSelection(A.near(t.doc.resolve($))),t.scrollIntoView()}return!0}let h=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p={...Ir(f,d.type.name,d.attrs),...e},m={...Ir(f,a.node().type.name,a.node().attrs),...e};t.delete(a.pos,c.pos);let g=h?[{type:l,attrs:p},{type:h,attrs:m}]:[{type:l,attrs:p}];if(!ke(t.doc,a.pos,2))return!1;if(i){let{selection:y,storedMarks:b}=r,{splittableMarks:S}=o.extensionManager,C=b||y.$to.parentOffset&&y.$from.marks();if(t.split(a.pos,2,g).scrollIntoView(),!C||!i)return!0;let M=C.filter(L=>S.includes(L.type.name));t.ensureMarks(M)}return!0},ns=(n,e)=>{let t=Hr(s=>s.type===e)(n.selection);if(!t)return!0;let r=n.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(r===void 0)return!0;let i=n.doc.nodeAt(r);return t.node.type===i?.type&&Me(n.doc,t.pos)&&n.join(t.pos),!0},rs=(n,e)=>{let t=Hr(s=>s.type===e)(n.selection);if(!t)return!0;let r=n.doc.resolve(t.start).after(t.depth);if(r===void 0)return!0;let i=n.doc.nodeAt(r);return t.node.type===i?.type&&Me(n.doc,r)&&n.join(r),!0},Xm=(n,e,t,r={})=>({editor:i,tr:o,state:s,dispatch:l,chain:a,commands:c,can:u})=>{let{extensions:d,splittableMarks:f}=i.extensionManager,h=K(n,s.schema),p=K(e,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:b}=m,S=y.blockRange(b),C=g||m.$to.parentOffset&&m.$from.marks();if(!S)return!1;let M=Hr(L=>Dc(L.type.name,d))(m);if(S.depth>=1&&M&&S.depth-M.depth<=1){if(M.node.type===h)return c.liftListItem(p);if(Dc(M.node.type.name,d)&&h.validContent(M.node.content)&&l)return a().command(()=>(o.setNodeMarkup(M.pos,h),!0)).command(()=>ns(o,h)).command(()=>rs(o,h)).run()}return!t||!C||!l?a().command(()=>u().wrapInList(h,r)?!0:c.clearNodes()).wrapInList(h,r).command(()=>ns(o,h)).command(()=>rs(o,h)).run():a().command(()=>{let L=u().wrapInList(h,r),$=C.filter(E=>f.includes(E.type.name));return o.ensureMarks($),L?!0:c.clearNodes()}).wrapInList(h,r).command(()=>ns(o,h)).command(()=>rs(o,h)).run()},Qm=(n,e={},t={})=>({state:r,commands:i})=>{let{extendEmptyMarkRange:o=!1}=t,s=et(n,r.schema);return ss(r,s,e)?i.unsetMark(s,{extendEmptyMarkRange:o}):i.setMark(s,e)},Zm=(n,e,t={})=>({state:r,commands:i})=>{let o=K(n,r.schema),s=K(e,r.schema),l=ze(r,o,t),a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?i.setNode(s,a):i.setNode(o,{...a,...t})},eg=(n,e={})=>({state:t,commands:r})=>{let i=K(n,t.schema);return ze(t,i,e)?r.lift(i):r.wrapIn(i,e)},tg=()=>({state:n,dispatch:e})=>{let t=n.plugins;for(let r=0;r<t.length;r+=1){let i=t[r],o;if(i.spec.isInputRules&&(o=i.getState(n))){if(e){let s=n.tr,l=o.transform;for(let a=l.steps.length-1;a>=0;a-=1)s.step(l.steps[a].invert(l.docs[a]));if(o.text){let a=s.doc.resolve(o.from).marks();s.replaceWith(o.from,o.to,n.schema.text(o.text,a))}else s.delete(o.from,o.to)}return!0}}return!1},ng=()=>({tr:n,dispatch:e})=>{let{selection:t}=n,{empty:r,ranges:i}=t;return r||e&&i.forEach(o=>{n.removeMark(o.$from.pos,o.$to.pos)}),!0},rg=(n,e={})=>({tr:t,state:r,dispatch:i})=>{var o;let{extendEmptyMarkRange:s=!1}=e,{selection:l}=t,a=et(n,r.schema),{$from:c,empty:u,ranges:d}=l;if(!i)return!0;if(u&&s){let{from:f,to:h}=l,p=(o=c.marks().find(g=>g.type===a))==null?void 0:o.attrs,m=ps(c,a,p);m&&(f=m.from,h=m.to),t.removeMark(f,h,a)}else d.forEach(f=>{t.removeMark(f.$from.pos,f.$to.pos,a)});return t.removeStoredMark(a),!0},ig=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let o=null,s=null,l=$r(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(o=K(n,r.schema)),l==="mark"&&(s=et(n,r.schema)),i&&t.selection.ranges.forEach(a=>{let c=a.$from.pos,u=a.$to.pos,d,f,h,p;t.selection.empty?r.doc.nodesBetween(c,u,(m,g)=>{o&&o===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m)}):r.doc.nodesBetween(c,u,(m,g)=>{g<c&&o&&o===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m),g>=c&&g<=u&&(o&&o===m.type&&t.setNodeMarkup(g,void 0,{...m.attrs,...e}),s&&m.marks.length&&m.marks.forEach(y=>{if(s===y.type){let b=Math.max(g,c),S=Math.min(g+m.nodeSize,u);t.addMark(b,S,s.create({...y.attrs,...e}))}}))}),f&&(d!==void 0&&t.setNodeMarkup(d,void 0,{...f.attrs,...e}),s&&f.marks.length&&f.marks.forEach(m=>{s===m.type&&t.addMark(h,p,s.create({...m.attrs,...e}))}))}),!0):!1},og=(n,e={})=>({state:t,dispatch:r})=>{let i=K(n,t.schema);return Mc(i,e)(t,r)},sg=(n,e={})=>({state:t,dispatch:r})=>{let i=K(n,t.schema);return Cc(i,e)(t,r)},tu=z.create({name:"commands",addCommands(){return{...Zc}}}),nu=z.create({name:"delete",onUpdate({transaction:n,appendedTransactions:e}){var t,r,i;let o=()=>{var s,l,a,c;if((c=(a=(l=(s=this.editor.options.coreExtensionOptions)==null?void 0:s.delete)==null?void 0:l.filterTransaction)==null?void 0:a.call(l,n))!=null?c:n.getMeta("y-sync$"))return;let u=ls(n.before,[n,...e]);fs(u).forEach(h=>{u.mapping.mapResult(h.oldRange.from).deletedAfter&&u.mapping.mapResult(h.oldRange.to).deletedBefore&&u.before.nodesBetween(h.oldRange.from,h.oldRange.to,(p,m)=>{let g=m+p.nodeSize-2,y=h.oldRange.from<=m&&g<=h.oldRange.to;this.editor.emit("delete",{type:"node",node:p,from:m,to:g,newFrom:u.mapping.map(m),newTo:u.mapping.map(g),deletedRange:h.oldRange,newRange:h.newRange,partial:!y,editor:this.editor,transaction:n,combinedTransform:u})})});let f=u.mapping;u.steps.forEach((h,p)=>{var m,g;if(h instanceof Je){let y=f.slice(p).map(h.from,-1),b=f.slice(p).map(h.to),S=f.invert().map(y,-1),C=f.invert().map(b),M=(m=u.doc.nodeAt(y-1))==null?void 0:m.marks.some($=>$.eq(h.mark)),L=(g=u.doc.nodeAt(b))==null?void 0:g.marks.some($=>$.eq(h.mark));this.editor.emit("delete",{type:"mark",mark:h.mark,from:h.from,to:h.to,deletedRange:{from:S,to:C},newRange:{from:y,to:b},partial:!!(L||M),editor:this.editor,transaction:n,combinedTransform:u})}})};(i=(r=(t=this.editor.options.coreExtensionOptions)==null?void 0:t.delete)==null?void 0:r.async)==null||i?setTimeout(o,0):o()}}),ru=z.create({name:"drop",addProseMirrorPlugins(){return[new O({key:new P("tiptapDrop"),props:{handleDrop:(n,e,t,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:t,moved:r})}}})]}}),iu=z.create({name:"editable",addProseMirrorPlugins(){return[new O({key:new P("editable"),props:{editable:()=>this.editor.options.editable}})]}}),ou=new P("focusEvents"),su=z.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:n}=this;return[new O({key:ou,props:{handleDOMEvents:{focus:(e,t)=>{n.isFocused=!0;let r=n.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,t)=>{n.isFocused=!1;let r=n.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),lu=z.create({name:"keymap",addKeyboardShortcuts(){let n=()=>this.editor.commands.first(({commands:s})=>[()=>s.undoInputRule(),()=>s.command(({tr:l})=>{let{selection:a,doc:c}=l,{empty:u,$anchor:d}=a,{pos:f,parent:h}=d,p=d.parent.isTextblock&&f>0?l.doc.resolve(f-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,y=m&&p.parent.childCount===1?g===d.pos:D.atStart(c).from===f;return!u||!h.type.isTextblock||h.textContent.length||!y||y&&d.parent.type.name==="paragraph"?!1:s.clearNodes()}),()=>s.deleteSelection(),()=>s.joinBackward(),()=>s.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:s})=>[()=>s.deleteSelection(),()=>s.deleteCurrentNode(),()=>s.joinForward(),()=>s.selectNodeForward()]),r={Enter:()=>this.editor.commands.first(({commands:s})=>[()=>s.newlineInCode(),()=>s.createParagraphNear(),()=>s.liftEmptyBlock(),()=>s.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i={...r},o={...r,"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return gs()||eu()?o:i},addProseMirrorPlugins(){return[new O({key:new P("clearDocument"),appendTransaction:(n,e,t)=>{if(n.some(m=>m.getMeta("composition")))return;let r=n.some(m=>m.docChanged)&&!e.doc.eq(t.doc),i=n.some(m=>m.getMeta("preventClearDocument"));if(!r||i)return;let{empty:o,from:s,to:l}=e.selection,a=D.atStart(e.doc).from,c=D.atEnd(e.doc).to;if(o||!(s===a&&l===c)||!Pn(t.doc))return;let f=t.tr,h=Fr({state:t,transaction:f}),{commands:p}=new zr({editor:this.editor,state:h});if(p.clearNodes(),!!f.steps.length)return f}})]}}),au=z.create({name:"paste",addProseMirrorPlugins(){return[new O({key:new P("tiptapPaste"),props:{handlePaste:(n,e,t)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:t})}}})]}}),cu=z.create({name:"tabindex",addProseMirrorPlugins(){return[new O({key:new P("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}}),lg=class rn{constructor(e,t,r=!1,i=null){this.currentNode=null,this.actualDepth=null,this.isBlock=r,this.resolvedPos=e,this.editor=t,this.currentNode=i}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!=null?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,r=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can\u2019t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,r=this.to-1}this.editor.commands.insertContentAt({from:t,to:r},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new rn(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new rn(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new rn(e,this.editor)}get children(){let e=[];return this.node.content.forEach((t,r)=>{let i=t.isBlock&&!t.isTextblock,o=t.isAtom&&!t.isText,s=this.pos+r+(o?0:1);if(s<0||s>this.resolvedPos.doc.nodeSize-2)return;let l=this.resolvedPos.doc.resolve(s);if(!i&&l.depth<=this.depth)return;let a=new rn(l,this.editor,i,i?t:null);i&&(a.actualDepth=this.depth+1),e.push(new rn(l,this.editor,i,i?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let r=null,i=this.parent;for(;i&&!r;){if(i.node.type.name===e)if(Object.keys(t).length>0){let o=i.node.attrs,s=Object.keys(t);for(let l=0;l<s.length;l+=1){let a=s[l];if(o[a]!==t[a])break}}else r=i;i=i.parent}return r}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},r=!1){let i=[];if(!this.children||this.children.length===0)return i;let o=Object.keys(t);return this.children.forEach(s=>{r&&i.length>0||(s.node.type.name===e&&o.every(a=>t[a]===s.node.attrs[a])&&i.push(s),!(r&&i.length>0)&&(i=i.concat(s.querySelectorAll(e,t,r))))}),i}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}},ag=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}`;function cg(n,e,t){let r=document.querySelector(`style[data-tiptap-style${t?`-${t}`:""}]`);if(r!==null)return r;let i=document.createElement("style");return e&&i.setAttribute("nonce",e),i.setAttribute(`data-tiptap-style${t?`-${t}`:""}`,""),i.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(i),i}var uu=class extends Op{constructor(n={}){super(),this.css=null,this.editorView=null,this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.instanceId=Math.random().toString(36).slice(2,9),this.options={element:typeof document<"u"?document.createElement("div"):null,content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:r})=>{throw r},onPaste:()=>null,onDrop:()=>null,onDelete:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(n),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:r,slice:i,moved:o})=>this.options.onDrop(r,i,o)),this.on("paste",({event:r,slice:i})=>this.options.onPaste(r,i)),this.on("delete",this.options.onDelete);let e=this.createDoc(),t=qc(e,this.options.autofocus);this.editorState=pr.create({doc:e,schema:this.schema,selection:t||void 0}),this.options.element&&this.mount(this.options.element)}mount(n){if(typeof document>"u")throw new Error("[tiptap error]: The editor cannot be mounted because there is no 'document' defined in this environment.");this.createView(n),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}unmount(){var n;if(this.editorView){let e=this.editorView.dom;e?.editor&&delete e.editor,this.editorView.destroy()}this.editorView=null,this.isInitialized=!1,(n=this.css)==null||n.remove(),this.css=null}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&typeof document<"u"&&(this.css=cg(ag,this.options.injectNonce))}setOptions(n={}){this.options={...this.options,...n},!(!this.editorView||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(n,e=!0){this.setOptions({editable:n}),e&&this.emit("update",{editor:this,transaction:this.state.tr,appendedTransactions:[]})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get view(){return this.editorView?this.editorView:new Proxy({state:this.editorState,updateState:n=>{this.editorState=n},dispatch:n=>{this.editorState=this.state.apply(n)},composing:!1,dragging:null,editable:!0,isDestroyed:!1},{get:(n,e)=>{if(e==="state")return this.editorState;if(e in n)return Reflect.get(n,e);throw new Error(`[tiptap error]: The editor view is not available. Cannot access view['${e}']. The editor may not be mounted yet.`)}})}get state(){return this.editorView&&(this.editorState=this.view.state),this.editorState}registerPlugin(n,e){let t=Fc(e)?e(n,[...this.state.plugins]):[...this.state.plugins,n],r=this.state.reconfigure({plugins:t});return this.view.updateState(r),r}unregisterPlugin(n){if(this.isDestroyed)return;let e=this.state.plugins,t=e;if([].concat(n).forEach(i=>{let o=typeof i=="string"?`${i}$`:i.key;t=t.filter(s=>!s.key.startsWith(o))}),e.length===t.length)return;let r=this.state.reconfigure({plugins:t});return this.view.updateState(r),r}createExtensionManager(){var n,e;let r=[...this.options.enableCoreExtensions?[iu,Qc.configure({blockSeparator:(e=(n=this.options.coreExtensionOptions)==null?void 0:n.clipboardTextSerializer)==null?void 0:e.blockSeparator}),tu,su,lu,cu,ru,au,nu].filter(i=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[i.name]!==!1:!0):[],...this.options.extensions].filter(i=>["extension","node","mark"].includes(i?.type));this.extensionManager=new Kr(r,this)}createCommandManager(){this.commandManager=new zr({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createDoc(){let n;try{n=is(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(e){if(!(e instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(e.message))throw e;this.emit("contentError",{editor:this,error:e,disableCollaboration:()=>{"collaboration"in this.storage&&typeof this.storage.collaboration=="object"&&this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(t=>t.name!=="collaboration"),this.createExtensionManager()}}),n=is(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}return n}createView(n){var e;this.editorView=new An(n,{...this.options.editorProps,attributes:{role:"textbox",...(e=this.options.editorProps)==null?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:this.editorState});let t=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(t),this.createNodeViews(),this.prependClass(),this.injectCSS();let r=this.view.dom;r.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({markViews:this.extensionManager.markViews,nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(n){this.isCapturingTransaction=!0,n(),this.isCapturingTransaction=!1;let e=this.capturedTransaction;return this.capturedTransaction=null,e}dispatchTransaction(n){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=n;return}n.steps.forEach(c=>{var u;return(u=this.capturedTransaction)==null?void 0:u.step(c)});return}let{state:e,transactions:t}=this.state.applyTransaction(n),r=!this.state.selection.eq(e.selection),i=t.includes(n),o=this.state;if(this.emit("beforeTransaction",{editor:this,transaction:n,nextState:e}),!i)return;this.view.updateState(e),this.emit("transaction",{editor:this,transaction:n,appendedTransactions:t.slice(1)}),r&&this.emit("selectionUpdate",{editor:this,transaction:n});let s=t.findLast(c=>c.getMeta("focus")||c.getMeta("blur")),l=s?.getMeta("focus"),a=s?.getMeta("blur");l&&this.emit("focus",{editor:this,event:l.event,transaction:s}),a&&this.emit("blur",{editor:this,event:a.event,transaction:s}),!(n.getMeta("preventUpdate")||!t.some(c=>c.docChanged)||o.doc.eq(e.doc))&&this.emit("update",{editor:this,transaction:n,appendedTransactions:t.slice(1)})}getAttributes(n){return ds(this.state,n)}isActive(n,e){let t=typeof n=="string"?n:null,r=typeof n=="string"?e:n;return Vp(this.state,t,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return cs(this.state.doc.content,this.schema)}getText(n){let{blockSeparator:e=`

`,textSerializers:t={}}=n||{};return Bp(this.state.doc,{blockSeparator:e,textSerializers:{...Vc(this.schema),...t}})}get isEmpty(){return Pn(this.state.doc)}destroy(){this.emit("destroy"),this.unmount(),this.removeAllListeners()}get isDestroyed(){var n,e;return(e=(n=this.editorView)==null?void 0:n.isDestroyed)!=null?e:!0}$node(n,e){var t;return((t=this.$doc)==null?void 0:t.querySelector(n,e))||null}$nodes(n,e){var t;return((t=this.$doc)==null?void 0:t.querySelectorAll(n,e))||null}$pos(n){let e=this.state.doc.resolve(n);return new lg(e,this)}get $doc(){return this.$pos(0)}};function Ee(n){return new Wr({find:n.find,handler:({state:e,range:t,match:r})=>{let i=V(n.getAttributes,void 0,r);if(i===!1||i===null)return null;let{tr:o}=e,s=r[r.length-1],l=r[0];if(s){let a=l.search(/\S/),c=t.from+l.indexOf(s),u=c+s.length;if(Vr(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===n.type&&m!==h.mark.type)).filter(h=>h.to>c).length)return null;u<t.to&&o.delete(u,t.to),c>t.from&&o.delete(t.from+a,c);let f=t.from+a+s.length;o.addMark(t.from+a,f,n.type.create(i||{})),o.removeStoredMark(n.type)}}})}function _r(n){return new Wr({find:n.find,handler:({state:e,range:t,match:r})=>{let i=V(n.getAttributes,void 0,r)||{},{tr:o}=e,s=t.from,l=t.to,a=n.type.create(i);if(r[1]){let c=r[0].lastIndexOf(r[1]),u=s+c;u>l?u=l:l=u+r[1].length;let d=r[0][r[0].length-1];o.insertText(d,s+r[0].length-1),o.replaceWith(u,l,a)}else if(r[0]){let c=n.type.isInline?s:s-1;o.insert(c,n.type.create(i)).delete(o.mapping.map(s),o.mapping.map(l))}o.scrollIntoView()}})}function In(n){return new Wr({find:n.find,handler:({state:e,range:t,match:r})=>{let i=e.doc.resolve(t.from),o=V(n.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),n.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,n.type,o)}})}function He(n){return new Wr({find:n.find,handler:({state:e,range:t,match:r,chain:i})=>{let o=V(n.getAttributes,void 0,r)||{},s=e.tr.delete(t.from,t.to),a=s.doc.resolve(t.from).blockRange(),c=a&&Jt(a,n.type,o);if(!c)return null;if(s.wrap(a,c),n.keepMarks&&n.editor){let{selection:d,storedMarks:f}=e,{splittableMarks:h}=n.editor.extensionManager,p=f||d.$to.parentOffset&&d.$from.marks();if(p){let m=p.filter(g=>h.includes(g.type.name));s.ensureMarks(m)}}if(n.keepAttributes){let d=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";i().updateAttributes(d,o).run()}let u=s.doc.resolve(t.from-1).nodeBefore;u&&u.type===n.type&&Me(s.doc,t.from-1)&&(!n.joinPredicate||n.joinPredicate(r,u))&&s.join(t.from-1)}})}function du(n,e){let{selection:t}=n,{$from:r}=t;if(t instanceof v){let o=r.index();return r.parent.canReplaceWith(o,o+1,e)}let i=r.depth;for(;i>=0;){let o=r.index(i);if(r.node(i).contentMatchAt(o).matchType(e))return!0;i-=1}return!1}function fu(n){return n.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")}var B=class hu extends ms{constructor(){super(...arguments),this.type="node"}static create(e={}){let t=typeof e=="function"?e():e;return new hu(t)}configure(e){return super.configure(e)}extend(e){let t=typeof e=="function"?e():e;return super.extend(t)}};function ye(n){return new Jp({find:n.find,handler:({state:e,range:t,match:r,pasteEvent:i})=>{let o=V(n.getAttributes,void 0,r,i);if(o===!1||o===null)return null;let{tr:s}=e,l=r[r.length-1],a=r[0],c=t.to;if(l){let u=a.search(/\S/),d=t.from+a.indexOf(l),f=d+l.length;if(Vr(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(g=>g===n.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;f<t.to&&s.delete(f,t.to),d>t.from&&s.delete(t.from+u,d),c=t.from+u+l.length,s.addMark(t.from+u,c,n.type.create(o||{})),s.removeStoredMark(n.type)}}})}function pu(n={}){return new O({view(e){return new ys(e,n)}})}var ys=class{constructor(e,t){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=t.width)!==null&&r!==void 0?r:1,this.color=t.color===!1?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(i=>{let o=s=>{this[i](s)};return e.dom.addEventListener(i,o),{name:i,handler:o}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t=!e.parent.inlineContent,r,i=this.editorView.dom,o=i.getBoundingClientRect(),s=o.width/i.offsetWidth,l=o.height/i.offsetHeight;if(t){let d=e.nodeBefore,f=e.nodeAfter;if(d||f){let h=this.editorView.nodeDOM(this.cursorPos-(d?d.nodeSize:0));if(h){let p=h.getBoundingClientRect(),m=d?p.bottom:p.top;d&&f&&(m=(m+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let g=this.width/2*l;r={left:p.left,right:p.right,top:m-g,bottom:m+g}}}}if(!r){let d=this.editorView.coordsAtPos(this.cursorPos),f=this.width/2*s;r={left:d.left-f,right:d.left+f,top:d.top,bottom:d.bottom}}let a=this.editorView.dom.offsetParent;this.element||(this.element=a.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",t),this.element.classList.toggle("prosemirror-dropcursor-inline",!t);let c,u;if(!a||a==document.body&&getComputedStyle(a).position=="static")c=-pageXOffset,u=-pageYOffset;else{let d=a.getBoundingClientRect(),f=d.width/a.offsetWidth,h=d.height/a.offsetHeight;c=d.left-a.scrollLeft*f,u=d.top-a.scrollTop*h}this.element.style.left=(r.left-c)/s+"px",this.element.style.top=(r.top-u)/l+"px",this.element.style.width=(r.right-r.left)/s+"px",this.element.style.height=(r.bottom-r.top)/l+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=r&&r.type.spec.disableDropCursor,o=typeof i=="function"?i(this.editorView,t,e):i;if(t&&!o){let s=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=ur(this.editorView.state.doc,s,this.editorView.dragging.slice);l!=null&&(s=l)}this.setCursor(s),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}};var ae=class n extends D{constructor(e){super(e,e)}map(e,t){let r=e.resolve(t.map(this.head));return n.valid(r)?new n(r):D.near(r)}content(){return w.empty}eq(e){return e instanceof n&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new n(e.resolve(t.pos))}getBookmark(){return new bs(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!ug(e)||!dg(e))return!1;let r=t.type.spec.allowGapCursor;if(r!=null)return r;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,r=!1){e:for(;;){if(!r&&n.valid(e))return e;let i=e.pos,o=null;for(let s=e.depth;;s--){let l=e.node(s);if(t>0?e.indexAfter(s)<l.childCount:e.index(s)>0){o=l.child(t>0?e.indexAfter(s):e.index(s)-1);break}else if(s==0)return null;i+=t;let a=e.doc.resolve(i);if(n.valid(a))return a}for(;;){let s=t>0?o.firstChild:o.lastChild;if(!s){if(o.isAtom&&!o.isText&&!v.isSelectable(o)){e=e.doc.resolve(i+o.nodeSize*t),r=!1;continue e}break}o=s,i+=t;let l=e.doc.resolve(i);if(n.valid(l))return l}return null}}};ae.prototype.visible=!1;ae.findFrom=ae.findGapCursorFrom;D.jsonID("gapcursor",ae);var bs=class n{constructor(e){this.pos=e}map(e){return new n(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return ae.valid(t)?new ae(t):D.near(t)}};function ug(n){for(let e=n.depth;e>=0;e--){let t=n.index(e),r=n.node(e);if(t==0){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t-1);;i=i.lastChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function dg(n){for(let e=n.depth;e>=0;e--){let t=n.indexAfter(e),r=n.node(e);if(t==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t);;i=i.firstChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function mu(){return new O({props:{decorations:mg,createSelectionBetween(n,e,t){return e.pos==t.pos&&ae.valid(t)?new ae(t):null},handleClick:hg,handleKeyDown:fg,handleDOMEvents:{beforeinput:pg}}})}var fg=zo({ArrowLeft:Ur("horiz",-1),ArrowRight:Ur("horiz",1),ArrowUp:Ur("vert",-1),ArrowDown:Ur("vert",1)});function Ur(n,e){let t=n=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,i,o){let s=r.selection,l=e>0?s.$to:s.$from,a=s.empty;if(s instanceof A){if(!o.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=r.doc.resolve(e>0?l.after():l.before())}let c=ae.findGapCursorFrom(l,e,a);return c?(i&&i(r.tr.setSelection(new ae(c))),!0):!1}}function hg(n,e,t){if(!n||!n.editable)return!1;let r=n.state.doc.resolve(e);if(!ae.valid(r))return!1;let i=n.posAtCoords({left:t.clientX,top:t.clientY});return i&&i.inside>-1&&v.isSelectable(n.state.doc.nodeAt(i.inside))?!1:(n.dispatch(n.state.tr.setSelection(new ae(r))),!0)}function pg(n,e){if(e.inputType!="insertCompositionText"||!(n.state.selection instanceof ae))return!1;let{$from:t}=n.state.selection,r=t.parent.contentMatchAt(t.index()).findWrapping(n.state.schema.nodes.text);if(!r)return!1;let i=x.empty;for(let s=r.length-1;s>=0;s--)i=x.from(r[s].createAndFill(null,i));let o=n.state.tr.replace(t.pos,t.pos,new w(i,0,0));return o.setSelection(A.near(o.doc.resolve(t.pos+1))),n.dispatch(o),!1}function mg(n){if(!(n.selection instanceof ae))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",W.create(n.doc,[ee.widget(n.selection.head,e,{key:"gapcursor"})])}var Jr=200,re=function(){};re.prototype.append=function(e){return e.length?(e=re.from(e),!this.length&&e||e.length<Jr&&this.leafAppend(e)||this.length<Jr&&e.leafPrepend(this)||this.appendInner(e)):this};re.prototype.prepend=function(e){return e.length?re.from(e).append(this):this};re.prototype.appendInner=function(e){return new gg(this,e)};re.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?re.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};re.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};re.prototype.forEach=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length),t<=r?this.forEachInner(e,t,r,0):this.forEachInvertedInner(e,t,r,0)};re.prototype.map=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length);var i=[];return this.forEach(function(o,s){return i.push(e(o,s))},t,r),i};re.from=function(e){return e instanceof re?e:e&&e.length?new gu(e):re.empty};var gu=function(n){function e(r){n.call(this),this.values=r}n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(i,o){return i==0&&o==this.length?this:new e(this.values.slice(i,o))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(i,o,s,l){for(var a=o;a<s;a++)if(i(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(i,o,s,l){for(var a=o-1;a>=s;a--)if(i(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(i){if(this.length+i.length<=Jr)return new e(this.values.concat(i.flatten()))},e.prototype.leafPrepend=function(i){if(this.length+i.length<=Jr)return new e(i.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(re);re.empty=new gu([]);var gg=function(n){function e(t,r){n.call(this),this.left=t,this.right=r,this.length=t.length+r.length,this.depth=Math.max(t.depth,r.depth)+1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,i,o,s){var l=this.left.length;if(i<l&&this.left.forEachInner(r,i,Math.min(o,l),s)===!1||o>l&&this.right.forEachInner(r,Math.max(i-l,0),Math.min(this.length,o)-l,s+l)===!1)return!1},e.prototype.forEachInvertedInner=function(r,i,o,s){var l=this.left.length;if(i>l&&this.right.forEachInvertedInner(r,i-l,Math.max(o,l)-l,s+l)===!1||o<l&&this.left.forEachInvertedInner(r,Math.min(i,l),o,s)===!1)return!1},e.prototype.sliceInner=function(r,i){if(r==0&&i==this.length)return this;var o=this.left.length;return i<=o?this.left.slice(r,i):r>=o?this.right.slice(r-o,i-o):this.left.slice(r,o).append(this.right.slice(0,i-o))},e.prototype.leafAppend=function(r){var i=this.right.leafAppend(r);if(i)return new e(this.left,i)},e.prototype.leafPrepend=function(r){var i=this.left.leafPrepend(r);if(i)return new e(i,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(re),xs=re;var yg=500,Lt=class n{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let i,o;t&&(i=this.remapping(r,this.items.length),o=i.maps.length);let s=e.tr,l,a,c=[],u=[];return this.items.forEach((d,f)=>{if(!d.step){i||(i=this.remapping(r,f+1),o=i.maps.length),o--,u.push(d);return}if(i){u.push(new $e(d.map));let h=d.step.map(i.slice(o)),p;h&&s.maybeStep(h).doc&&(p=s.mapping.maps[s.mapping.maps.length-1],c.push(new $e(p,void 0,void 0,c.length+u.length))),o--,p&&i.appendMap(p,o)}else s.maybeStep(d.step);if(d.selection)return l=i?d.selection.map(i.slice(o)):d.selection,a=new n(this.items.slice(0,r).append(u.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:s,selection:l}}addTransform(e,t,r,i){let o=[],s=this.eventCount,l=this.items,a=!i&&l.length?l.get(l.length-1):null;for(let u=0;u<e.steps.length;u++){let d=e.steps[u].invert(e.docs[u]),f=new $e(e.mapping.maps[u],d,t),h;(h=a&&a.merge(f))&&(f=h,u?o.pop():l=l.slice(0,l.length-1)),o.push(f),t&&(s++,t=void 0),i||(a=f)}let c=s-r.depth;return c>xg&&(l=bg(l,c),s-=c),new n(l.append(o),s)}remapping(e,t){let r=new yn;return this.items.forEach((i,o)=>{let s=i.mirrorOffset!=null&&o-i.mirrorOffset>=e?r.maps.length-i.mirrorOffset:void 0;r.appendMap(i.map,s)},e,t),r}addMaps(e){return this.eventCount==0?this:new n(this.items.append(e.map(t=>new $e(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let r=[],i=Math.max(0,this.items.length-t),o=e.mapping,s=e.steps.length,l=this.eventCount;this.items.forEach(f=>{f.selection&&l--},i);let a=t;this.items.forEach(f=>{let h=o.getMirror(--a);if(h==null)return;s=Math.min(s,h);let p=o.maps[h];if(f.step){let m=e.steps[h].invert(e.docs[h]),g=f.selection&&f.selection.map(o.slice(a+1,h));g&&l++,r.push(new $e(p,m,g))}else r.push(new $e(p))},i);let c=[];for(let f=t;f<s;f++)c.push(new $e(o.maps[f]));let u=this.items.slice(0,i).append(c).append(r),d=new n(u,l);return d.emptyItemCount()>yg&&(d=d.compress(this.items.length-r.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),r=t.maps.length,i=[],o=0;return this.items.forEach((s,l)=>{if(l>=e)i.push(s),s.selection&&o++;else if(s.step){let a=s.step.map(t.slice(r)),c=a&&a.getMap();if(r--,c&&t.appendMap(c,r),a){let u=s.selection&&s.selection.map(t.slice(r));u&&o++;let d=new $e(c.invert(),a,u),f,h=i.length-1;(f=i.length&&i[h].merge(d))?i[h]=f:i.push(d)}}else s.map&&r--},this.items.length,0),new n(xs.from(i.reverse()),o)}};Lt.empty=new Lt(xs.empty,0);function bg(n,e){let t;return n.forEach((r,i)=>{if(r.selection&&e--==0)return t=i,!1}),n.slice(t)}var $e=class n{constructor(e,t,r,i){this.map=e,this.step=t,this.selection=r,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new n(t.getMap().invert(),t,this.selection)}}},Ve=class{constructor(e,t,r,i,o){this.done=e,this.undone=t,this.prevRanges=r,this.prevTime=i,this.prevComposition=o}},xg=20;function kg(n,e,t,r){let i=t.getMeta(It),o;if(i)return i.historyState;t.getMeta(Mg)&&(n=new Ve(n.done,n.undone,null,0,-1));let s=t.getMeta("appendedTransaction");if(t.steps.length==0)return n;if(s&&s.getMeta(It))return s.getMeta(It).redo?new Ve(n.done.addTransform(t,void 0,r,qr(e)),n.undone,yu(t.mapping.maps),n.prevTime,n.prevComposition):new Ve(n.done,n.undone.addTransform(t,void 0,r,qr(e)),null,n.prevTime,n.prevComposition);if(t.getMeta("addToHistory")!==!1&&!(s&&s.getMeta("addToHistory")===!1)){let l=t.getMeta("composition"),a=n.prevTime==0||!s&&n.prevComposition!=l&&(n.prevTime<(t.time||0)-r.newGroupDelay||!Sg(t,n.prevRanges)),c=s?ks(n.prevRanges,t.mapping):yu(t.mapping.maps);return new Ve(n.done.addTransform(t,a?e.selection.getBookmark():void 0,r,qr(e)),Lt.empty,c,t.time,l??n.prevComposition)}else return(o=t.getMeta("rebased"))?new Ve(n.done.rebased(t,o),n.undone.rebased(t,o),ks(n.prevRanges,t.mapping),n.prevTime,n.prevComposition):new Ve(n.done.addMaps(t.mapping.maps),n.undone.addMaps(t.mapping.maps),ks(n.prevRanges,t.mapping),n.prevTime,n.prevComposition)}function Sg(n,e){if(!e)return!1;if(!n.docChanged)return!0;let t=!1;return n.mapping.maps[0].forEach((r,i)=>{for(let o=0;o<e.length;o+=2)r<=e[o+1]&&i>=e[o]&&(t=!0)}),t}function yu(n){let e=[];for(let t=n.length-1;t>=0&&e.length==0;t--)n[t].forEach((r,i,o,s)=>e.push(o,s));return e}function ks(n,e){if(!n)return null;let t=[];for(let r=0;r<n.length;r+=2){let i=e.map(n[r],1),o=e.map(n[r+1],-1);i<=o&&t.push(i,o)}return t}function wg(n,e,t){let r=qr(e),i=It.get(e).spec.config,o=(t?n.undone:n.done).popEvent(e,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(t?n.done:n.undone).addTransform(o.transform,e.selection.getBookmark(),i,r),a=new Ve(t?l:o.remaining,t?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(It,{redo:t,historyState:a})}var Ss=!1,bu=null;function qr(n){let e=n.plugins;if(bu!=e){Ss=!1,bu=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){Ss=!0;break}}return Ss}var It=new P("history"),Mg=new P("closeHistory");function xu(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new O({key:It,state:{init(){return new Ve(Lt.empty,Lt.empty,null,0,-1)},apply(e,t,r){return kg(t,r,e,n)}},config:n,props:{handleDOMEvents:{beforeinput(e,t){let r=t.inputType,i=r=="historyUndo"?ws:r=="historyRedo"?Ms:null;return i?(t.preventDefault(),i(e.state,e.dispatch)):!1}}}})}function Gr(n,e){return(t,r)=>{let i=It.getState(t);if(!i||(n?i.undone:i.done).eventCount==0)return!1;if(r){let o=wg(i,t,n);o&&r(e?o.scrollIntoView():o)}return!0}}var ws=Gr(!1,!0),Ms=Gr(!0,!0),Ex=Gr(!1,!1),Ax=Gr(!0,!1);var Ix=z.create({name:"characterCount",addOptions(){return{limit:null,mode:"textSize",textCounter:n=>n.length,wordCounter:n=>n.split(" ").filter(e=>e!=="").length}},addStorage(){return{characters:()=>0,words:()=>0}},onBeforeCreate(){this.storage.characters=n=>{let e=n?.node||this.editor.state.doc;if((n?.mode||this.options.mode)==="textSize"){let r=e.textBetween(0,e.content.size,void 0," ");return this.options.textCounter(r)}return e.nodeSize},this.storage.words=n=>{let e=n?.node||this.editor.state.doc,t=e.textBetween(0,e.content.size," "," ");return this.options.wordCounter(t)}},addProseMirrorPlugins(){let n=!1;return[new O({key:new P("characterCount"),appendTransaction:(e,t,r)=>{if(n)return;let i=this.options.limit;if(i==null||i===0){n=!0;return}let o=this.storage.characters({node:r.doc});if(o>i){let s=o-i,l=0,a=s;console.warn(`[CharacterCount] Initial content exceeded limit of ${i} characters. Content was automatically trimmed.`);let c=r.tr.deleteRange(l,a);return n=!0,c}n=!0},filterTransaction:(e,t)=>{let r=this.options.limit;if(!e.docChanged||r===0||r===null||r===void 0)return!0;let i=this.storage.characters({node:t.doc}),o=this.storage.characters({node:e.doc});if(o<=r||i>r&&o>r&&o<=i)return!0;if(i>r&&o>r&&o>i||!e.getMeta("paste"))return!1;let l=e.selection.$head.pos,a=o-r,c=l-a,u=l;return e.deleteRange(c,u),!(this.storage.characters({node:e.doc})>r)}})]}}),Su=z.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[pu(this.options)]}}),$x=z.create({name:"focus",addOptions(){return{className:"has-focus",mode:"all"}},addProseMirrorPlugins(){return[new O({key:new P("focus"),props:{decorations:({doc:n,selection:e})=>{let{isEditable:t,isFocused:r}=this.editor,{anchor:i}=e,o=[];if(!t||!r)return W.create(n,[]);let s=0;this.options.mode==="deepest"&&n.descendants((a,c)=>{if(a.isText)return;if(!(i>=c&&i<=c+a.nodeSize-1))return!1;s+=1});let l=0;return n.descendants((a,c)=>{if(a.isText||!(i>=c&&i<=c+a.nodeSize-1))return!1;if(l+=1,this.options.mode==="deepest"&&s-l>0||this.options.mode==="shallowest"&&l>1)return this.options.mode==="deepest";o.push(ee.node(c,c+a.nodeSize,{class:this.options.className}))}),W.create(n,o)}}})]}}),wu=z.create({name:"gapCursor",addProseMirrorPlugins(){return[mu()]},extendNodeSchema(n){var e;let t={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(e=V(T(n,"allowGapCursor",t)))!=null?e:null}}}),Cs=z.create({name:"placeholder",addOptions(){return{emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something \u2026",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}},addProseMirrorPlugins(){return[new O({key:new P("placeholder"),props:{decorations:({doc:n,selection:e})=>{let t=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=e,i=[];if(!t)return null;let o=this.editor.isEmpty;return n.descendants((s,l)=>{let a=r>=l&&r<=l+s.nodeSize,c=!s.isLeaf&&Pn(s);if((a||!this.options.showOnlyCurrent)&&c){let u=[this.options.emptyNodeClass];o&&u.push(this.options.emptyEditorClass);let d=ee.node(l,l+s.nodeSize,{class:u.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:s,pos:l,hasAnchor:a}):this.options.placeholder});i.push(d)}return this.options.includeChildren}),W.create(n,i)}}})]}}),Gx=z.create({name:"selection",addOptions(){return{className:"selection"}},addProseMirrorPlugins(){let{editor:n,options:e}=this;return[new O({key:new P("selection"),props:{decorations(t){return t.selection.empty||n.isFocused||!n.isEditable||jr(t.selection)?null:W.create(t.doc,[ee.inline(t.selection.from,t.selection.to,{class:e.className})])}}})]}});function ku({types:n,node:e}){return e&&Array.isArray(n)&&n.includes(e.type)||e?.type===n}var Qx=z.create({name:"trailingNode",addOptions(){return{node:"paragraph",notAfter:[]}},addProseMirrorPlugins(){let n=new P(this.name),e=Object.entries(this.editor.schema.nodes).map(([,t])=>t).filter(t=>(this.options.notAfter||[]).concat(this.options.node).includes(t.name));return[new O({key:n,appendTransaction:(t,r,i)=>{let{doc:o,tr:s,schema:l}=i,a=n.getState(i),c=o.content.size,u=l.nodes[this.options.node];if(a)return s.insert(c,u.create())},state:{init:(t,r)=>{let i=r.tr.doc.lastChild;return!ku({node:i,types:e})},apply:(t,r)=>{if(!t.docChanged)return r;let i=t.doc.lastChild;return!ku({node:i,types:e})}}})]}}),Mu=z.create({name:"undoRedo",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:e})=>ws(n,e),redo:()=>({state:n,dispatch:e})=>Ms(n,e)}},addProseMirrorPlugins(){return[xu(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-\u044F":()=>this.editor.commands.undo(),"Shift-Mod-\u044F":()=>this.editor.commands.redo()}}});var on=(n,e)=>{if(n==="slot")return 0;if(n instanceof Function)return n(e);let{children:t,...r}=e??{};if(n==="svg")throw new Error("SVG elements are not supported in the JSX syntax, use the array syntax instead");return[n,r,t]};var Cg=/^\s*>\s$/,vg=B.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return on("blockquote",{...N(this.options.HTMLAttributes,n),children:on("slot",{})})},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[He({find:Cg,type:this.type})]}}),Cu=vg;var Tg=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,Eg=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,Ag=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,Og=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,Ng=U.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:n=>n.type.name===this.name},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return on("strong",{...N(this.options.HTMLAttributes,n),children:on("slot",{})})},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Ee({find:Tg,type:this.type}),Ee({find:Ag,type:this.type})]},addPasteRules(){return[ye({find:Eg,type:this.type}),ye({find:Og,type:this.type})]}}),vu=Ng;var Dg=/(^|[^`])`([^`]+)`(?!`)/,Rg=/(^|[^`])`([^`]+)`(?!`)/g,Pg=U.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[Ee({find:Dg,type:this.type})]},addPasteRules(){return[ye({find:Rg,type:this.type})]}}),Tu=Pg;var Ig=/^```([a-z]+)?[\s\n]$/,Lg=/^~~~([a-z]+)?[\s\n]$/,Bg=B.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:n=>{var e;let{languageClassPrefix:t}=this.options,o=[...((e=n.firstElementChild)==null?void 0:e.classList)||[]].filter(s=>s.startsWith(t)).map(s=>s.replace(t,""))[0];return o||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:e}){return["pre",N(this.options.HTMLAttributes,e),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:e})=>e.setNode(this.name,n),toggleCodeBlock:n=>({commands:e})=>e.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:n,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!n||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=n,{selection:t}=e,{$from:r,empty:i}=t;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith(`

`);return!o||!s?!1:n.chain().command(({tr:l})=>(l.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=n,{selection:t,doc:r}=e,{$from:i,empty:o}=t;if(!o||i.parent.type!==this.type||!(i.parentOffset===i.parent.nodeSize-2))return!1;let l=i.after();return l===void 0?!1:r.nodeAt(l)?n.commands.command(({tr:c})=>(c.setSelection(D.near(r.resolve(l))),!0)):n.commands.exitCode()}}},addInputRules(){return[In({find:Ig,type:this.type,getAttributes:n=>({language:n[1]})}),In({find:Lg,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new O({key:new P("codeBlockVSCodeHandler"),props:{handlePaste:(n,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let t=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=i?.mode;if(!t||!o)return!1;let{tr:s,schema:l}=n.state,a=l.text(t.replace(/\r\n?/g,`
`));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(A.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),n.dispatch(s),!0}}})]}}),Eu=Bg;var Au=B.create({name:"customBlock",group:"block",atom:!0,defining:!0,draggable:!0,selectable:!0,isolating:!0,allowGapCursor:!0,inline:!1,addNodeView(){return({editor:n,node:e,getPos:t,HTMLAttributes:r,decorations:i,extension:o})=>{let s=document.createElement("div");s.setAttribute("data-config",e.attrs.config),s.setAttribute("data-id",e.attrs.id),s.setAttribute("data-type","customBlock");let l=document.createElement("div");if(l.className="fi-fo-rich-editor-custom-block-header fi-not-prose",s.appendChild(l),n.isEditable&&typeof e.attrs.config=="object"&&e.attrs.config!==null&&Object.keys(e.attrs.config).length>0){let c=document.createElement("div");c.className="fi-fo-rich-editor-custom-block-edit-btn-ctn",l.appendChild(c);let u=document.createElement("button");u.className="fi-icon-btn",u.type="button",u.innerHTML=o.options.editCustomBlockButtonIconHtml,u.addEventListener("click",()=>o.options.editCustomBlockUsing(e.attrs.id,e.attrs.config)),c.appendChild(u)}let a=document.createElement("p");if(a.className="fi-fo-rich-editor-custom-block-heading",a.textContent=e.attrs.label,l.appendChild(a),n.isEditable){let c=document.createElement("div");c.className="fi-fo-rich-editor-custom-block-delete-btn-ctn",l.appendChild(c);let u=document.createElement("button");u.className="fi-icon-btn",u.type="button",u.innerHTML=o.options.deleteCustomBlockButtonIconHtml,u.addEventListener("click",()=>n.chain().setNodeSelection(t()).deleteSelection().run()),c.appendChild(u)}if(e.attrs.preview){let c=document.createElement("div");c.className="fi-fo-rich-editor-custom-block-preview fi-not-prose",c.innerHTML=new TextDecoder().decode(Uint8Array.from(atob(e.attrs.preview),u=>u.charCodeAt(0))),s.appendChild(c)}return{dom:s}}},addOptions(){return{deleteCustomBlockButtonIconHtml:null,editCustomBlockButtonIconHtml:null,editCustomBlockUsing:()=>{},insertCustomBlockUsing:()=>{}}},addAttributes(){return{config:{default:null,parseHTML:n=>JSON.parse(n.getAttribute("data-config"))},id:{default:null,parseHTML:n=>n.getAttribute("data-id"),renderHTML:n=>n.id?{"data-id":n.id}:{}},label:{default:null,parseHTML:n=>n.getAttribute("data-label"),rendered:!1},preview:{default:null,parseHTML:n=>n.getAttribute("data-preview"),rendered:!1}}},parseHTML(){return[{tag:`div[data-type="${this.name}"]`}]},renderHTML({HTMLAttributes:n}){return["div",N(n)]},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:n,state:e})=>{let t=!1,{selection:r}=e,{empty:i,anchor:o}=r;if(!i)return!1;let s=new Q,l=0;return e.doc.nodesBetween(o-1,o,(a,c)=>{if(a.type.name===this.name)return t=!0,s=a,l=c,!1}),t})}},addProseMirrorPlugins(){let{insertCustomBlockUsing:n}=this.options;return[new O({props:{handleDrop(e,t){if(!t||(t.preventDefault(),!t.dataTransfer.getData("customBlock")))return!1;let r=t.dataTransfer.getData("customBlock");return n(r,e.posAtCoords({left:t.clientX,top:t.clientY}).pos),!1}}})]}});var Fg=B.create({name:"doc",topNode:!0,content:"block+"}),Ou=Fg;var zg=B.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",N(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:e,state:t,editor:r})=>n.first([()=>n.exitCode(),()=>n.command(()=>{let{selection:i,storedMarks:o}=t;if(i.$from.parent.type.spec.isolating)return!1;let{keepMarks:s}=this.options,{splittableMarks:l}=r.extensionManager,a=o||i.$to.parentOffset&&i.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:u})=>{if(u&&a&&s){let d=a.filter(f=>l.includes(f.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Nu=zg;var Hg=B.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:e}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,N(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.setNode(this.name,n):!1,toggleHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,e)=>({...n,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(n=>In({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}}),Du=Hg;var $g=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,Vg=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,jg=U.create({name:"highlight",addOptions(){return{multicolor:!1,HTMLAttributes:{}}},addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:n=>n.getAttribute("data-color")||n.style.backgroundColor,renderHTML:n=>n.color?{"data-color":n.color,style:`background-color: ${n.color}; color: inherit`}:{}}}:{}},parseHTML(){return[{tag:"mark"}]},renderHTML({HTMLAttributes:n}){return["mark",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setHighlight:n=>({commands:e})=>e.setMark(this.name,n),toggleHighlight:n=>({commands:e})=>e.toggleMark(this.name,n),unsetHighlight:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[Ee({find:$g,type:this.type})]},addPasteRules(){return[ye({find:Vg,type:this.type})]}}),Ru=jg;var Wg=B.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",N(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:e})=>{if(!du(e,e.schema.nodes[this.name]))return!1;let{selection:t}=e,{$to:r}=t,i=n();return jr(t)?i.insertContentAt(r.pos,{type:this.name}):i.insertContent({type:this.name}),i.command(({tr:o,dispatch:s})=>{var l;if(s){let{$to:a}=o.selection,c=a.end();if(a.nodeAfter)a.nodeAfter.isTextblock?o.setSelection(A.create(o.doc,a.pos+1)):a.nodeAfter.isBlock?o.setSelection(v.create(o.doc,a.pos)):o.setSelection(A.create(o.doc,a.pos));else{let u=(l=a.parent.type.contentMatch.defaultType)==null?void 0:l.create();u&&(o.insert(c,u),o.setSelection(A.create(o.doc,c+1)))}o.scrollIntoView()}return!0}).run()}}},addInputRules(){return[_r({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Pu=Wg;var Kg=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,_g=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,Ug=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,Jg=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,qg=U.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:n=>n.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Ee({find:Kg,type:this.type}),Ee({find:Ug,type:this.type})]},addPasteRules(){return[ye({find:_g,type:this.type}),ye({find:Jg,type:this.type})]}}),Iu=qg;var Gg=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,Yg=B.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null},width:{default:null},height:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:n}){return["img",N(this.options.HTMLAttributes,n)]},addCommands(){return{setImage:n=>({commands:e})=>e.insertContent({type:this.name,attrs:n})}},addInputRules(){return[_r({find:Gg,type:this.type,getAttributes:n=>{let[,,e,t,r]=n;return{src:t,alt:e,title:r}}})]}}),Lu=Yg;var Bu=Lu.extend({addAttributes(){return{...this.parent?.(),id:{default:null,parseHTML:n=>n.getAttribute("data-id"),renderHTML:n=>n.id?{"data-id":n.id}:{}}}}});var Fu=B.create({name:"lead",group:"block",content:"block+",addOptions(){return{HTMLAttributes:{class:"lead"}}},parseHTML(){return[{tag:"div",getAttrs:n=>n.classList.contains("lead")}]},renderHTML({HTMLAttributes:n}){return["div",N(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleLead:()=>({commands:n})=>n.toggleWrap(this.name)}}});var Xg="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xF6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",Qg="\u03B5\u03BB1\u03C52\u0431\u04331\u0435\u043B3\u0434\u0435\u0442\u04384\u0435\u044E2\u043A\u0430\u0442\u043E\u043B\u0438\u043A6\u043E\u043C3\u043C\u043A\u04342\u043E\u043D1\u0441\u043A\u0432\u04306\u043E\u043D\u043B\u0430\u0439\u043D5\u0440\u04333\u0440\u0443\u04412\u04442\u0441\u0430\u0439\u04423\u0440\u04313\u0443\u043A\u04403\u049B\u0430\u04373\u0570\u0561\u05753\u05D9\u05E9\u05E8\u05D0\u05DC5\u05E7\u05D5\u05DD3\u0627\u0628\u0648\u0638\u0628\u064A5\u0631\u0627\u0645\u0643\u06485\u0644\u0627\u0631\u062F\u06464\u0628\u062D\u0631\u064A\u06465\u062C\u0632\u0627\u0626\u06315\u0633\u0639\u0648\u062F\u064A\u06296\u0639\u0644\u064A\u0627\u06465\u0645\u063A\u0631\u06285\u0645\u0627\u0631\u0627\u062A5\u06CC\u0631\u0627\u06465\u0628\u0627\u0631\u062A2\u0632\u0627\u06314\u064A\u062A\u06433\u06BE\u0627\u0631\u062A5\u062A\u0648\u0646\u06334\u0633\u0648\u062F\u0627\u06463\u0631\u064A\u06295\u0634\u0628\u0643\u06294\u0639\u0631\u0627\u06422\u06282\u0645\u0627\u06464\u0641\u0644\u0633\u0637\u064A\u06466\u0642\u0637\u06313\u0643\u0627\u062B\u0648\u0644\u064A\u06436\u0648\u06453\u0645\u0635\u06312\u0644\u064A\u0633\u064A\u06275\u0648\u0631\u064A\u062A\u0627\u0646\u064A\u06277\u0642\u06394\u0647\u0645\u0631\u0627\u06475\u067E\u0627\u06A9\u0633\u062A\u0627\u06467\u0680\u0627\u0631\u062A4\u0915\u0949\u092E3\u0928\u0947\u091F3\u092D\u093E\u0930\u09240\u092E\u094D3\u094B\u09245\u0938\u0902\u0917\u0920\u09285\u09AC\u09BE\u0982\u09B2\u09BE5\u09AD\u09BE\u09B0\u09A42\u09F0\u09A44\u0A2D\u0A3E\u0A30\u0A244\u0AAD\u0ABE\u0AB0\u0AA44\u0B2D\u0B3E\u0B30\u0B244\u0B87\u0BA8\u0BCD\u0BA4\u0BBF\u0BAF\u0BBE6\u0BB2\u0B99\u0BCD\u0B95\u0BC86\u0B9A\u0BBF\u0B99\u0BCD\u0B95\u0BAA\u0BCD\u0BAA\u0BC2\u0BB0\u0BCD11\u0C2D\u0C3E\u0C30\u0C24\u0C4D5\u0CAD\u0CBE\u0CB0\u0CA44\u0D2D\u0D3E\u0D30\u0D24\u0D025\u0DBD\u0D82\u0D9A\u0DCF4\u0E04\u0E2D\u0E213\u0E44\u0E17\u0E223\u0EA5\u0EB2\u0EA73\u10D2\u10D42\u307F\u3093\u306A3\u30A2\u30DE\u30BE\u30F34\u30AF\u30E9\u30A6\u30C94\u30B0\u30FC\u30B0\u30EB4\u30B3\u30E02\u30B9\u30C8\u30A23\u30BB\u30FC\u30EB3\u30D5\u30A1\u30C3\u30B7\u30E7\u30F36\u30DD\u30A4\u30F3\u30C84\u4E16\u754C2\u4E2D\u4FE11\u56FD1\u570B1\u6587\u7F513\u4E9A\u9A6C\u900A3\u4F01\u4E1A2\u4F5B\u5C712\u4FE1\u606F2\u5065\u5EB72\u516B\u53662\u516C\u53F81\u76CA2\u53F0\u6E7E1\u70632\u5546\u57CE1\u5E971\u68072\u5609\u91CC0\u5927\u9152\u5E975\u5728\u7EBF2\u5927\u62FF2\u5929\u4E3B\u65593\u5A31\u4E502\u5BB6\u96FB2\u5E7F\u4E1C2\u5FAE\u535A2\u6148\u55842\u6211\u7231\u4F603\u624B\u673A2\u62DB\u80582\u653F\u52A11\u5E9C2\u65B0\u52A0\u57612\u95FB2\u65F6\u5C1A2\u66F8\u7C4D2\u673A\u67842\u6DE1\u9A6C\u95213\u6E38\u620F2\u6FB3\u95802\u70B9\u770B2\u79FB\u52A82\u7EC4\u7EC7\u673A\u67844\u7F51\u57401\u5E971\u7AD91\u7EDC2\u8054\u901A2\u8C37\u6B4C2\u8D2D\u72692\u901A\u8CA92\u96C6\u56E22\u96FB\u8A0A\u76C8\u79D14\u98DE\u5229\u6D663\u98DF\u54C12\u9910\u53852\u9999\u683C\u91CC\u62C93\u6E2F2\uB2F7\uB1371\uCEF42\uC0BC\uC1312\uD55C\uAD6D2",ln=(n,e)=>{for(let t in e)n[t]=e[t];return n},Ds="numeric",Rs="ascii",Ps="alpha",Fn="asciinumeric",Bn="alphanumeric",Is="domain",Ku="emoji",Zg="scheme",ey="slashscheme",vs="whitespace";function ty(n,e){return n in e||(e[n]=[]),e[n]}function Bt(n,e,t){e[Ds]&&(e[Fn]=!0,e[Bn]=!0),e[Rs]&&(e[Fn]=!0,e[Ps]=!0),e[Fn]&&(e[Bn]=!0),e[Ps]&&(e[Bn]=!0),e[Bn]&&(e[Is]=!0),e[Ku]&&(e[Is]=!0);for(let r in e){let i=ty(r,t);i.indexOf(n)<0&&i.push(n)}}function ny(n,e){let t={};for(let r in e)e[r].indexOf(n)>=0&&(t[r]=!0);return t}function be(n=null){this.j={},this.jr=[],this.jd=null,this.t=n}be.groups={};be.prototype={accepts(){return!!this.t},go(n){let e=this,t=e.j[n];if(t)return t;for(let r=0;r<e.jr.length;r++){let i=e.jr[r][0],o=e.jr[r][1];if(o&&i.test(n))return o}return e.jd},has(n,e=!1){return e?n in this.j:!!this.go(n)},ta(n,e,t,r){for(let i=0;i<n.length;i++)this.tt(n[i],e,t,r)},tr(n,e,t,r){r=r||be.groups;let i;return e&&e.j?i=e:(i=new be(e),t&&r&&Bt(e,t,r)),this.jr.push([n,i]),i},ts(n,e,t,r){let i=this,o=n.length;if(!o)return i;for(let s=0;s<o-1;s++)i=i.tt(n[s]);return i.tt(n[o-1],e,t,r)},tt(n,e,t,r){r=r||be.groups;let i=this;if(e&&e.j)return i.j[n]=e,e;let o=e,s,l=i.go(n);if(l?(s=new be,ln(s.j,l.j),s.jr.push.apply(s.jr,l.jr),s.jd=l.jd,s.t=l.t):s=new be,o){if(r)if(s.t&&typeof s.t=="string"){let a=ln(ny(s.t,r),t);Bt(o,a,r)}else t&&Bt(o,t,r);s.t=o}return i.j[n]=s,s}};var I=(n,e,t,r,i)=>n.ta(e,t,r,i),_=(n,e,t,r,i)=>n.tr(e,t,r,i),zu=(n,e,t,r,i)=>n.ts(e,t,r,i),k=(n,e,t,r,i)=>n.tt(e,t,r,i),rt="WORD",Ls="UWORD",_u="ASCIINUMERICAL",Uu="ALPHANUMERICAL",Wn="LOCALHOST",Bs="TLD",Fs="UTLD",Zr="SCHEME",sn="SLASH_SCHEME",Hs="NUM",zs="WS",$s="NL",zn="OPENBRACE",Hn="CLOSEBRACE",ei="OPENBRACKET",ti="CLOSEBRACKET",ni="OPENPAREN",ri="CLOSEPAREN",ii="OPENANGLEBRACKET",oi="CLOSEANGLEBRACKET",si="FULLWIDTHLEFTPAREN",li="FULLWIDTHRIGHTPAREN",ai="LEFTCORNERBRACKET",ci="RIGHTCORNERBRACKET",ui="LEFTWHITECORNERBRACKET",di="RIGHTWHITECORNERBRACKET",fi="FULLWIDTHLESSTHAN",hi="FULLWIDTHGREATERTHAN",pi="AMPERSAND",mi="APOSTROPHE",gi="ASTERISK",pt="AT",yi="BACKSLASH",bi="BACKTICK",xi="CARET",mt="COLON",Vs="COMMA",ki="DOLLAR",je="DOT",Si="EQUALS",js="EXCLAMATION",Oe="HYPHEN",$n="PERCENT",wi="PIPE",Mi="PLUS",Ci="POUND",Vn="QUERY",Ws="QUOTE",Ju="FULLWIDTHMIDDLEDOT",Ks="SEMI",We="SLASH",jn="TILDE",vi="UNDERSCORE",qu="EMOJI",Ti="SYM",Gu=Object.freeze({__proto__:null,ALPHANUMERICAL:Uu,AMPERSAND:pi,APOSTROPHE:mi,ASCIINUMERICAL:_u,ASTERISK:gi,AT:pt,BACKSLASH:yi,BACKTICK:bi,CARET:xi,CLOSEANGLEBRACKET:oi,CLOSEBRACE:Hn,CLOSEBRACKET:ti,CLOSEPAREN:ri,COLON:mt,COMMA:Vs,DOLLAR:ki,DOT:je,EMOJI:qu,EQUALS:Si,EXCLAMATION:js,FULLWIDTHGREATERTHAN:hi,FULLWIDTHLEFTPAREN:si,FULLWIDTHLESSTHAN:fi,FULLWIDTHMIDDLEDOT:Ju,FULLWIDTHRIGHTPAREN:li,HYPHEN:Oe,LEFTCORNERBRACKET:ai,LEFTWHITECORNERBRACKET:ui,LOCALHOST:Wn,NL:$s,NUM:Hs,OPENANGLEBRACKET:ii,OPENBRACE:zn,OPENBRACKET:ei,OPENPAREN:ni,PERCENT:$n,PIPE:wi,PLUS:Mi,POUND:Ci,QUERY:Vn,QUOTE:Ws,RIGHTCORNERBRACKET:ci,RIGHTWHITECORNERBRACKET:di,SCHEME:Zr,SEMI:Ks,SLASH:We,SLASH_SCHEME:sn,SYM:Ti,TILDE:jn,TLD:Bs,UNDERSCORE:vi,UTLD:Fs,UWORD:Ls,WORD:rt,WS:zs}),tt=/[a-z]/,Ln=/\p{L}/u,Ts=/\p{Emoji}/u;var nt=/\d/,Es=/\s/;var Hu="\r",As=`
`,ry="\uFE0F",iy="\u200D",Os="\uFFFC",Yr=null,Xr=null;function oy(n=[]){let e={};be.groups=e;let t=new be;Yr==null&&(Yr=$u(Xg)),Xr==null&&(Xr=$u(Qg)),k(t,"'",mi),k(t,"{",zn),k(t,"}",Hn),k(t,"[",ei),k(t,"]",ti),k(t,"(",ni),k(t,")",ri),k(t,"<",ii),k(t,">",oi),k(t,"\uFF08",si),k(t,"\uFF09",li),k(t,"\u300C",ai),k(t,"\u300D",ci),k(t,"\u300E",ui),k(t,"\u300F",di),k(t,"\uFF1C",fi),k(t,"\uFF1E",hi),k(t,"&",pi),k(t,"*",gi),k(t,"@",pt),k(t,"`",bi),k(t,"^",xi),k(t,":",mt),k(t,",",Vs),k(t,"$",ki),k(t,".",je),k(t,"=",Si),k(t,"!",js),k(t,"-",Oe),k(t,"%",$n),k(t,"|",wi),k(t,"+",Mi),k(t,"#",Ci),k(t,"?",Vn),k(t,'"',Ws),k(t,"/",We),k(t,";",Ks),k(t,"~",jn),k(t,"_",vi),k(t,"\\",yi),k(t,"\u30FB",Ju);let r=_(t,nt,Hs,{[Ds]:!0});_(r,nt,r);let i=_(r,tt,_u,{[Fn]:!0}),o=_(r,Ln,Uu,{[Bn]:!0}),s=_(t,tt,rt,{[Rs]:!0});_(s,nt,i),_(s,tt,s),_(i,nt,i),_(i,tt,i);let l=_(t,Ln,Ls,{[Ps]:!0});_(l,tt),_(l,nt,o),_(l,Ln,l),_(o,nt,o),_(o,tt),_(o,Ln,o);let a=k(t,As,$s,{[vs]:!0}),c=k(t,Hu,zs,{[vs]:!0}),u=_(t,Es,zs,{[vs]:!0});k(t,Os,u),k(c,As,a),k(c,Os,u),_(c,Es,u),k(u,Hu),k(u,As),_(u,Es,u),k(u,Os,u);let d=_(t,Ts,qu,{[Ku]:!0});k(d,"#"),_(d,Ts,d),k(d,ry,d);let f=k(d,iy);k(f,"#"),_(f,Ts,d);let h=[[tt,s],[nt,i]],p=[[tt,null],[Ln,l],[nt,o]];for(let m=0;m<Yr.length;m++)ht(t,Yr[m],Bs,rt,h);for(let m=0;m<Xr.length;m++)ht(t,Xr[m],Fs,Ls,p);Bt(Bs,{tld:!0,ascii:!0},e),Bt(Fs,{utld:!0,alpha:!0},e),ht(t,"file",Zr,rt,h),ht(t,"mailto",Zr,rt,h),ht(t,"http",sn,rt,h),ht(t,"https",sn,rt,h),ht(t,"ftp",sn,rt,h),ht(t,"ftps",sn,rt,h),Bt(Zr,{scheme:!0,ascii:!0},e),Bt(sn,{slashscheme:!0,ascii:!0},e),n=n.sort((m,g)=>m[0]>g[0]?1:-1);for(let m=0;m<n.length;m++){let g=n[m][0],b=n[m][1]?{[Zg]:!0}:{[ey]:!0};g.indexOf("-")>=0?b[Is]=!0:tt.test(g)?nt.test(g)?b[Fn]=!0:b[Rs]=!0:b[Ds]=!0,zu(t,g,g,b)}return zu(t,"localhost",Wn,{ascii:!0}),t.jd=new be(Ti),{start:t,tokens:ln({groups:e},Gu)}}function Yu(n,e){let t=sy(e.replace(/[A-Z]/g,l=>l.toLowerCase())),r=t.length,i=[],o=0,s=0;for(;s<r;){let l=n,a=null,c=0,u=null,d=-1,f=-1;for(;s<r&&(a=l.go(t[s]));)l=a,l.accepts()?(d=0,f=0,u=l):d>=0&&(d+=t[s].length,f++),c+=t[s].length,o+=t[s].length,s++;o-=d,s-=f,c-=d,i.push({t:u.t,v:e.slice(o-c,o),s:o-c,e:o})}return i}function sy(n){let e=[],t=n.length,r=0;for(;r<t;){let i=n.charCodeAt(r),o,s=i<55296||i>56319||r+1===t||(o=n.charCodeAt(r+1))<56320||o>57343?n[r]:n.slice(r,r+2);e.push(s),r+=s.length}return e}function ht(n,e,t,r,i){let o,s=e.length;for(let l=0;l<s-1;l++){let a=e[l];n.j[a]?o=n.j[a]:(o=new be(r),o.jr=i.slice(),n.j[a]=o),n=o}return o=new be(t),o.jr=i.slice(),n.j[e[s-1]]=o,o}function $u(n){let e=[],t=[],r=0,i="0123456789";for(;r<n.length;){let o=0;for(;i.indexOf(n[r+o])>=0;)o++;if(o>0){e.push(t.join(""));for(let s=parseInt(n.substring(r,r+o),10);s>0;s--)t.pop();r+=o}else t.push(n[r]),r++}return e}var Kn={defaultProtocol:"http",events:null,format:Vu,formatHref:Vu,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function _s(n,e=null){let t=ln({},Kn);n&&(t=ln(t,n instanceof _s?n.o:n));let r=t.ignoreTags,i=[];for(let o=0;o<r.length;o++)i.push(r[o].toUpperCase());this.o=t,e&&(this.defaultRender=e),this.ignoreTags=i}_s.prototype={o:Kn,ignoreTags:[],defaultRender(n){return n},check(n){return this.get("validate",n.toString(),n)},get(n,e,t){let r=e!=null,i=this.o[n];return i&&(typeof i=="object"?(i=t.t in i?i[t.t]:Kn[n],typeof i=="function"&&r&&(i=i(e,t))):typeof i=="function"&&r&&(i=i(e,t.t,t)),i)},getObj(n,e,t){let r=this.o[n];return typeof r=="function"&&e!=null&&(r=r(e,t.t,t)),r},render(n){let e=n.render(this);return(this.get("render",null,n)||this.defaultRender)(e,n.t,n)}};function Vu(n){return n}function Xu(n,e){this.t="token",this.v=n,this.tk=e}Xu.prototype={isLink:!1,toString(){return this.v},toHref(n){return this.toString()},toFormattedString(n){let e=this.toString(),t=n.get("truncate",e,this),r=n.get("format",e,this);return t&&r.length>t?r.substring(0,t)+"\u2026":r},toFormattedHref(n){return n.get("formatHref",this.toHref(n.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(n=Kn.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(n),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(n){return{type:this.t,value:this.toFormattedString(n),isLink:this.isLink,href:this.toFormattedHref(n),start:this.startIndex(),end:this.endIndex()}},validate(n){return n.get("validate",this.toString(),this)},render(n){let e=this,t=this.toHref(n.get("defaultProtocol")),r=n.get("formatHref",t,this),i=n.get("tagName",t,e),o=this.toFormattedString(n),s={},l=n.get("className",t,e),a=n.get("target",t,e),c=n.get("rel",t,e),u=n.getObj("attributes",t,e),d=n.getObj("events",t,e);return s.href=r,l&&(s.class=l),a&&(s.target=a),c&&(s.rel=c),u&&ln(s,u),{tagName:i,attributes:s,content:o,eventListeners:d}}};function Ei(n,e){class t extends Xu{constructor(i,o){super(i,o),this.t=n}}for(let r in e)t.prototype[r]=e[r];return t.t=n,t}var ju=Ei("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Wu=Ei("text"),ly=Ei("nl"),Qr=Ei("url",{isLink:!0,toHref(n=Kn.defaultProtocol){return this.hasProtocol()?this.v:`${n}://${this.v}`},hasProtocol(){let n=this.tk;return n.length>=2&&n[0].t!==Wn&&n[1].t===mt}});var Ae=n=>new be(n);function ay({groups:n}){let e=n.domain.concat([pi,gi,pt,yi,bi,xi,ki,Si,Oe,Hs,$n,wi,Mi,Ci,We,Ti,jn,vi]),t=[mi,mt,Vs,je,js,$n,Vn,Ws,Ks,ii,oi,zn,Hn,ti,ei,ni,ri,si,li,ai,ci,ui,di,fi,hi],r=[pi,mi,gi,yi,bi,xi,ki,Si,Oe,zn,Hn,$n,wi,Mi,Ci,Vn,We,Ti,jn,vi],i=Ae(),o=k(i,jn);I(o,r,o),I(o,n.domain,o);let s=Ae(),l=Ae(),a=Ae();I(i,n.domain,s),I(i,n.scheme,l),I(i,n.slashscheme,a),I(s,r,o),I(s,n.domain,s);let c=k(s,pt);k(o,pt,c),k(l,pt,c),k(a,pt,c);let u=k(o,je);I(u,r,o),I(u,n.domain,o);let d=Ae();I(c,n.domain,d),I(d,n.domain,d);let f=k(d,je);I(f,n.domain,d);let h=Ae(ju);I(f,n.tld,h),I(f,n.utld,h),k(c,Wn,h);let p=k(d,Oe);k(p,Oe,p),I(p,n.domain,d),I(h,n.domain,d),k(h,je,f),k(h,Oe,p);let m=k(h,mt);I(m,n.numeric,ju);let g=k(s,Oe),y=k(s,je);k(g,Oe,g),I(g,n.domain,s),I(y,r,o),I(y,n.domain,s);let b=Ae(Qr);I(y,n.tld,b),I(y,n.utld,b),I(b,n.domain,s),I(b,r,o),k(b,je,y),k(b,Oe,g),k(b,pt,c);let S=k(b,mt),C=Ae(Qr);I(S,n.numeric,C);let M=Ae(Qr),L=Ae();I(M,e,M),I(M,t,L),I(L,e,M),I(L,t,L),k(b,We,M),k(C,We,M);let $=k(l,mt),E=k(a,mt),F=k(E,We),R=k(F,We);I(l,n.domain,s),k(l,je,y),k(l,Oe,g),I(a,n.domain,s),k(a,je,y),k(a,Oe,g),I($,n.domain,M),k($,We,M),k($,Vn,M),I(R,n.domain,M),I(R,e,M),k(R,We,M);let J=[[zn,Hn],[ei,ti],[ni,ri],[ii,oi],[si,li],[ai,ci],[ui,di],[fi,hi]];for(let Se=0;Se<J.length;Se++){let[ot,me]=J[Se],we=k(M,ot);k(L,ot,we),k(we,me,M);let Y=Ae(Qr);I(we,e,Y);let X=Ae();I(we,t),I(Y,e,Y),I(Y,t,X),I(X,e,Y),I(X,t,X),k(Y,me,M),k(X,me,M)}return k(i,Wn,b),k(i,$s,ly),{start:i,tokens:Gu}}function cy(n,e,t){let r=t.length,i=0,o=[],s=[];for(;i<r;){let l=n,a=null,c=null,u=0,d=null,f=-1;for(;i<r&&!(a=l.go(t[i].t));)s.push(t[i++]);for(;i<r&&(c=a||l.go(t[i].t));)a=null,l=c,l.accepts()?(f=0,d=l):f>=0&&f++,i++,u++;if(f<0)i-=u,i<r&&(s.push(t[i]),i++);else{s.length>0&&(o.push(Ns(Wu,e,s)),s=[]),i-=f,u-=f;let h=d.t,p=t.slice(i-u,i);o.push(Ns(h,e,p))}}return s.length>0&&o.push(Ns(Wu,e,s)),o}function Ns(n,e,t){let r=t[0].s,i=t[t.length-1].e,o=e.slice(r,i);return new n(o,t)}var uy=typeof console<"u"&&console&&console.warn||(()=>{}),dy="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",j={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function Qu(){return be.groups={},j.scanner=null,j.parser=null,j.tokenQueue=[],j.pluginQueue=[],j.customSchemes=[],j.initialized=!1,j}function Us(n,e=!1){if(j.initialized&&uy(`linkifyjs: already initialized - will not register custom scheme "${n}" ${dy}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(n))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);j.customSchemes.push([n,e])}function fy(){j.scanner=oy(j.customSchemes);for(let n=0;n<j.tokenQueue.length;n++)j.tokenQueue[n][1]({scanner:j.scanner});j.parser=ay(j.scanner.tokens);for(let n=0;n<j.pluginQueue.length;n++)j.pluginQueue[n][1]({scanner:j.scanner,parser:j.parser});return j.initialized=!0,j}function Ai(n){return j.initialized||fy(),cy(j.parser.start,n,Yu(j.scanner.start,n))}Ai.scan=Yu;function Oi(n,e=null,t=null){if(e&&typeof e=="object"){if(t)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);t=e,e=null}let r=new _s(t),i=Ai(n),o=[];for(let s=0;s<i.length;s++){let l=i[s];l.isLink&&(!e||l.t===e)&&r.check(l)&&o.push(l.toFormattedObject(r))}return o}function hy(n){return n.length===1?n[0].isLink:n.length===3&&n[1].isLink?["()","[]"].includes(n[0].value+n[2].value):!1}function py(n){return new O({key:new P("autolink"),appendTransaction:(e,t,r)=>{let i=e.some(c=>c.docChanged)&&!t.doc.eq(r.doc),o=e.some(c=>c.getMeta("preventAutolink"));if(!i||o)return;let{tr:s}=r,l=ls(t.doc,[...e]);if(fs(l).forEach(({newRange:c})=>{let u=Bc(r.doc,c,h=>h.isTextblock),d,f;if(u.length>1?(d=u[0],f=r.doc.textBetween(d.pos,d.pos+d.node.nodeSize,void 0," ")):u.length&&r.doc.textBetween(c.from,c.to," "," ").endsWith(" ")&&(d=u[0],f=r.doc.textBetween(d.pos,c.to,void 0," ")),d&&f){let h=f.split(" ").filter(y=>y!=="");if(h.length<=0)return!1;let p=h[h.length-1],m=d.pos+f.lastIndexOf(p);if(!p)return!1;let g=Ai(p).map(y=>y.toObject(n.defaultProtocol));if(!hy(g))return!1;g.filter(y=>y.isLink).map(y=>({...y,from:m+y.start+1,to:m+y.end+1})).filter(y=>r.schema.marks.code?!r.doc.rangeHasMark(y.from,y.to,r.schema.marks.code):!0).filter(y=>n.validate(y.value)).filter(y=>n.shouldAutoLink(y.value)).forEach(y=>{Vr(y.from,y.to,r.doc).some(b=>b.mark.type===n.type)||s.addMark(y.from,y.to,n.type.create({href:y.href}))})}}),!!s.steps.length)return s}})}function my(n){return new O({key:new P("handleClickLink"),props:{handleClick:(e,t,r)=>{var i,o;if(r.button!==0||!e.editable)return!1;let s=null;if(r.target instanceof HTMLAnchorElement)s=r.target;else{let u=r.target,d=[];for(;u.nodeName!=="DIV";)d.push(u),u=u.parentNode;s=d.find(f=>f.nodeName==="A")}if(!s)return!1;let l=ds(e.state,n.type.name),a=(i=s?.href)!=null?i:l.href,c=(o=s?.target)!=null?o:l.target;return s&&a?(window.open(a,c),!0):!1}}})}function gy(n){return new O({key:new P("handlePasteLink"),props:{handlePaste:(e,t,r)=>{let{state:i}=e,{selection:o}=i,{empty:s}=o;if(s)return!1;let l="";r.content.forEach(c=>{l+=c.textContent});let a=Oi(l,{defaultProtocol:n.defaultProtocol}).find(c=>c.isLink&&c.value===l);return!l||!a?!1:n.editor.commands.setMark(n.type,{href:a.href})}}})}var yy=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function Ft(n,e){let t=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach(r=>{let i=typeof r=="string"?r:r.scheme;i&&t.push(i)}),!n||n.replace(yy,"").match(new RegExp(`^(?:(?:${t.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}var by=U.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(n=>{if(typeof n=="string"){Us(n);return}Us(n.scheme,n.optionalSlashes)})},onDestroy(){Qu()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(n,e)=>!!Ft(n,e.protocols),validate:n=>!!n,shouldAutoLink:n=>!!n}},addAttributes(){return{href:{default:null,parseHTML(n){return n.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:n=>{let e=n.getAttribute("href");return!e||!this.options.isAllowedUri(e,{defaultValidate:t=>!!Ft(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:n}){return this.options.isAllowedUri(n.href,{defaultValidate:e=>!!Ft(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",N(this.options.HTMLAttributes,n),0]:["a",N(this.options.HTMLAttributes,{...n,href:""}),0]},addCommands(){return{setLink:n=>({chain:e})=>{let{href:t}=n;return this.options.isAllowedUri(t,{defaultValidate:r=>!!Ft(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?e().setMark(this.name,n).setMeta("preventAutolink",!0).run():!1},toggleLink:n=>({chain:e})=>{let{href:t}=n;return this.options.isAllowedUri(t,{defaultValidate:r=>!!Ft(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?e().toggleMark(this.name,n,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run():!1},unsetLink:()=>({chain:n})=>n().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ye({find:n=>{let e=[];if(n){let{protocols:t,defaultProtocol:r}=this.options,i=Oi(n).filter(o=>o.isLink&&this.options.isAllowedUri(o.value,{defaultValidate:s=>!!Ft(s,t),protocols:t,defaultProtocol:r}));i.length&&i.forEach(o=>e.push({text:o.value,data:{href:o.href},index:o.start}))}return e},type:this.type,getAttributes:n=>{var e;return{href:(e=n.data)==null?void 0:e.href}}})]},addProseMirrorPlugins(){let n=[],{protocols:e,defaultProtocol:t}=this.options;return this.options.autolink&&n.push(py({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:r=>this.options.isAllowedUri(r,{defaultValidate:i=>!!Ft(i,e),protocols:e,defaultProtocol:t}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&n.push(my({type:this.type})),this.options.linkOnPaste&&n.push(gy({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),n}}),Zu=by;var xy=Object.defineProperty,ky=(n,e)=>{for(var t in e)xy(n,t,{get:e[t],enumerable:!0})},Sy="listItem",ed="textStyle",td=/^\s*([-+*])\s$/,Gs=B.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",N(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Sy,this.editor.getAttributes(ed)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=He({find:td,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=He({find:td,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(ed),editor:this.editor})),[n]}}),Ys=B.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",N(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),wy={};ky(wy,{findListItemPos:()=>_n,getNextListDepth:()=>Xs,handleBackspace:()=>Js,handleDelete:()=>qs,hasListBefore:()=>id,hasListItemAfter:()=>My,hasListItemBefore:()=>od,listItemHasSubList:()=>sd,nextListIsDeeper:()=>ld,nextListIsHigher:()=>ad});var _n=(n,e)=>{let{$from:t}=e.selection,r=K(n,e.schema),i=null,o=t.depth,s=t.pos,l=null;for(;o>0&&l===null;)i=t.node(o),i.type===r?l=o:(o-=1,s-=1);return l===null?null:{$pos:e.doc.resolve(s),depth:l}},Xs=(n,e)=>{let t=_n(n,e);if(!t)return!1;let[,r]=Kc(e,n,t.$pos.pos+4);return r},id=(n,e,t)=>{let{$anchor:r}=n.selection,i=Math.max(0,r.pos-2),o=n.doc.resolve(i).node();return!(!o||!t.includes(o.type.name))},od=(n,e)=>{var t;let{$anchor:r}=e.selection,i=e.doc.resolve(r.pos-2);return!(i.index()===0||((t=i.nodeBefore)==null?void 0:t.type.name)!==n)},sd=(n,e,t)=>{if(!t)return!1;let r=K(n,e.schema),i=!1;return t.descendants(o=>{o.type===r&&(i=!0)}),i},Js=(n,e,t)=>{if(n.commands.undoInputRule())return!0;if(n.state.selection.from!==n.state.selection.to)return!1;if(!ze(n.state,e)&&id(n.state,e,t)){let{$anchor:l}=n.state.selection,a=n.state.doc.resolve(l.before()-1),c=[];a.node().descendants((f,h)=>{f.type.name===e&&c.push({node:f,pos:h})});let u=c.at(-1);if(!u)return!1;let d=n.state.doc.resolve(a.start()+u.pos+1);return n.chain().cut({from:l.start()-1,to:l.end()+1},d.end()).joinForward().run()}if(!ze(n.state,e)||!Uc(n.state))return!1;let r=_n(e,n.state);if(!r)return!1;let o=n.state.doc.resolve(r.$pos.pos-2).node(r.depth),s=sd(e,n.state,o);return od(e,n.state)&&!s?n.commands.joinItemBackward():n.chain().liftListItem(e).run()},ld=(n,e)=>{let t=Xs(n,e),r=_n(n,e);return!r||!t?!1:t>r.depth},ad=(n,e)=>{let t=Xs(n,e),r=_n(n,e);return!r||!t?!1:t<r.depth},qs=(n,e)=>{if(!ze(n.state,e)||!_c(n.state,e))return!1;let{selection:t}=n.state,{$from:r,$to:i}=t;return!t.empty&&r.sameParent(i)?!1:ld(e,n.state)?n.chain().focus(n.state.selection.from+4).lift(e).joinBackward().run():ad(e,n.state)?n.chain().joinForward().joinBackward().run():n.commands.joinItemForward()},My=(n,e)=>{var t;let{$anchor:r}=e.selection,i=e.doc.resolve(r.pos-r.parentOffset-2);return!(i.index()===i.parent.childCount-1||((t=i.nodeAfter)==null?void 0:t.type.name)!==n)},Cy=z.create({name:"listKeymap",addOptions(){return{listTypes:[{itemName:"listItem",wrapperNames:["bulletList","orderedList"]},{itemName:"taskItem",wrapperNames:["taskList"]}]}},addKeyboardShortcuts(){return{Delete:({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t})=>{n.state.schema.nodes[t]!==void 0&&qs(n,t)&&(e=!0)}),e},"Mod-Delete":({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t})=>{n.state.schema.nodes[t]!==void 0&&qs(n,t)&&(e=!0)}),e},Backspace:({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t,wrapperNames:r})=>{n.state.schema.nodes[t]!==void 0&&Js(n,t,r)&&(e=!0)}),e},"Mod-Backspace":({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t,wrapperNames:r})=>{n.state.schema.nodes[t]!==void 0&&Js(n,t,r)&&(e=!0)}),e}}}}),vy="listItem",nd="textStyle",rd=/^(\d+)\.\s$/,Qs=B.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1},type:{default:null,parseHTML:n=>n.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){let{start:e,...t}=n;return e===1?["ol",N(this.options.HTMLAttributes,t),0]:["ol",N(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(vy,this.editor.getAttributes(nd)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=He({find:rd,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=He({find:rd,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(nd)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[n]}}),Ty=/^\s*(\[([( |x])?\])\s$/,Ey=B.create({name:"taskItem",addOptions(){return{nested:!1,HTMLAttributes:{},taskListTypeName:"taskList"}},content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes(){return{checked:{default:!1,keepOnSplit:!1,parseHTML:n=>{let e=n.getAttribute("data-checked");return e===""||e==="true"},renderHTML:n=>({"data-checked":n.checked})}}},parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:n,HTMLAttributes:e}){return["li",N(this.options.HTMLAttributes,e,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:n.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){let n={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...n,Tab:()=>this.editor.commands.sinkListItem(this.name)}:n},addNodeView(){return({node:n,HTMLAttributes:e,getPos:t,editor:r})=>{let i=document.createElement("li"),o=document.createElement("label"),s=document.createElement("span"),l=document.createElement("input"),a=document.createElement("div");return o.contentEditable="false",l.type="checkbox",l.addEventListener("mousedown",c=>c.preventDefault()),l.addEventListener("change",c=>{if(!r.isEditable&&!this.options.onReadOnlyChecked){l.checked=!l.checked;return}let{checked:u}=c.target;r.isEditable&&typeof t=="function"&&r.chain().focus(void 0,{scrollIntoView:!1}).command(({tr:d})=>{let f=t();if(typeof f!="number")return!1;let h=d.doc.nodeAt(f);return d.setNodeMarkup(f,void 0,{...h?.attrs,checked:u}),!0}).run(),!r.isEditable&&this.options.onReadOnlyChecked&&(this.options.onReadOnlyChecked(n,u)||(l.checked=!l.checked))}),Object.entries(this.options.HTMLAttributes).forEach(([c,u])=>{i.setAttribute(c,u)}),i.dataset.checked=n.attrs.checked,l.checked=n.attrs.checked,o.append(l,s),i.append(o,a),Object.entries(e).forEach(([c,u])=>{i.setAttribute(c,u)}),{dom:i,contentDOM:a,update:c=>c.type!==this.type?!1:(i.dataset.checked=c.attrs.checked,l.checked=c.attrs.checked,!0)}}},addInputRules(){return[He({find:Ty,type:this.type,getAttributes:n=>({checked:n[n.length-1]==="x"})})]}}),Ay=B.create({name:"taskList",addOptions(){return{itemTypeName:"taskItem",HTMLAttributes:{}}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:n}){return["ul",N(this.options.HTMLAttributes,n,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:n})=>n.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}}),aS=z.create({name:"listKit",addExtensions(){let n=[];return this.options.bulletList!==!1&&n.push(Gs.configure(this.options.bulletList)),this.options.listItem!==!1&&n.push(Ys.configure(this.options.listItem)),this.options.listKeymap!==!1&&n.push(Cy.configure(this.options.listKeymap)),this.options.orderedList!==!1&&n.push(Qs.configure(this.options.orderedList)),this.options.taskItem!==!1&&n.push(Ey.configure(this.options.taskItem)),this.options.taskList!==!1&&n.push(Ay.configure(this.options.taskList)),n}});var cd=["image/png","image/jpeg","image/gif","image/webp"],Ni=(n,e,t={})=>{n.dom.closest("form")?.dispatchEvent(new CustomEvent(e,{composed:!0,cancelable:!0,detail:t}))},Oy=({editor:n,get$WireUsing:e,key:t,statePath:r,uploadingMessage:i})=>{let o=s=>e().callSchemaComponentMethod(t,"getUploadedFileAttachmentTemporaryUrl",{attachment:s});return new O({key:new P("localFiles"),props:{handleDrop(s,l){if(!l.dataTransfer?.files.length)return!1;let a=Array.from(l.dataTransfer.files).filter(u=>cd.includes(u.type));if(!a.length)return!1;Ni(s,"form-processing-started",{message:i}),l.preventDefault(),l.stopPropagation();let c=s.posAtCoords({left:l.clientX,top:l.clientY});return a.forEach((u,d)=>{n.setEditable(!1),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploading-file",{bubbles:!0,detail:{key:t,livewireId:e().id}}));let f=new FileReader;f.readAsDataURL(u),f.onload=()=>{n.chain().insertContentAt(c?.pos??0,{type:"image",attrs:{class:"fi-loading",src:f.result}}).run()};let h=("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,p=>(p^crypto.getRandomValues(new Uint8Array(1))[0]&15>>p/4).toString(16));e().upload(`componentFileAttachments.${r}.${h}`,u,()=>{o(h).then(p=>{p&&(n.chain().updateAttributes("image",{class:null,id:h,src:p}).run(),n.setEditable(!0),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploaded-file",{bubbles:!0,detail:{key:t,livewireId:e().id}})),d===a.length-1&&Ni(s,"form-processing-finished"))})})}),!0},handlePaste(s,l){if(!l.clipboardData?.files.length)return!1;let a=Array.from(l.clipboardData.files).filter(c=>cd.includes(c.type));return a.length?(l.preventDefault(),l.stopPropagation(),Ni(s,"form-processing-started",{message:i}),a.forEach((c,u)=>{n.setEditable(!1),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploading-file",{bubbles:!0,detail:{key:t,livewireId:e().id}}));let d=new FileReader;d.readAsDataURL(c),d.onload=()=>{n.chain().insertContentAt(n.state.selection.anchor,{type:"image",attrs:{class:"fi-loading",src:d.result}}).run()};let f=("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,h=>(h^crypto.getRandomValues(new Uint8Array(1))[0]&15>>h/4).toString(16));e().upload(`componentFileAttachments.${r}.${f}`,c,()=>{o(f).then(h=>{h&&(n.chain().updateAttributes("image",{class:null,id:f,src:h}).run(),n.setEditable(!0),s.dom.dispatchEvent(new CustomEvent("rich-editor-uploaded-file",{bubbles:!0,detail:{key:t,livewireId:e().id}})),u===a.length-1&&Ni(s,"form-processing-finished"))})})}),!0):!1}}})},ud=z.create({name:"localFiles",addOptions(){return{key:null,statePath:null,uploadingMessage:null,get$WireUsing:null}},addProseMirrorPlugins(){return[Oy({editor:this.editor,...this.options})]}});function Ny(n){var e;let{char:t,allowSpaces:r,allowToIncludeChar:i,allowedPrefixes:o,startOfLine:s,$position:l}=n,a=r&&!i,c=fu(t),u=new RegExp(`\\s${c}$`),d=s?"^":"",f=i?"":c,h=a?new RegExp(`${d}${c}.*?(?=\\s${f}|$)`,"gm"):new RegExp(`${d}(?:^)?${c}[^\\s${f}]*`,"gm"),p=((e=l.nodeBefore)==null?void 0:e.isText)&&l.nodeBefore.text;if(!p)return null;let m=l.pos-p.length,g=Array.from(p.matchAll(h)).pop();if(!g||g.input===void 0||g.index===void 0)return null;let y=g.input.slice(Math.max(0,g.index-1),g.index),b=new RegExp(`^[${o?.join("")}\0]?$`).test(y);if(o!==null&&!b)return null;let S=m+g.index,C=S+g[0].length;return a&&u.test(p.slice(C-1,C+1))&&(g[0]+=" ",C+=1),S<l.pos&&C>=l.pos?{range:{from:S,to:C},query:g[0].slice(t.length),text:g[0]}:null}var Dy=new P("suggestion");function Ry({pluginKey:n=Dy,editor:e,char:t="@",allowSpaces:r=!1,allowToIncludeChar:i=!1,allowedPrefixes:o=[" "],startOfLine:s=!1,decorationTag:l="span",decorationClass:a="suggestion",command:c=()=>null,items:u=()=>[],render:d=()=>({}),allow:f=()=>!0,findSuggestionMatch:h=Ny}){let p,m=d?.(),g=new O({key:n,view(){return{update:async(y,b)=>{var S,C,M,L,$,E,F;let R=(S=this.key)==null?void 0:S.getState(b),J=(C=this.key)==null?void 0:C.getState(y.state),Se=R.active&&J.active&&R.range.from!==J.range.from,ot=!R.active&&J.active,me=R.active&&!J.active,we=!ot&&!me&&R.query!==J.query,Y=ot||Se&&we,X=we||Se,ce=me||Se&&we;if(!Y&&!X&&!ce)return;let ue=ce&&!Y?R:J,Qn=y.dom.querySelector(`[data-decoration-id="${ue.decorationId}"]`);p={editor:e,range:ue.range,query:ue.query,text:ue.text,items:[],command:Zn=>c({editor:e,range:ue.range,props:Zn}),decorationNode:Qn,clientRect:Qn?()=>{var Zn;let{decorationId:Gd}=(Zn=this.key)==null?void 0:Zn.getState(e.state),sl=y.dom.querySelector(`[data-decoration-id="${Gd}"]`);return sl?.getBoundingClientRect()||null}:null},Y&&((M=m?.onBeforeStart)==null||M.call(m,p)),X&&((L=m?.onBeforeUpdate)==null||L.call(m,p)),(X||Y)&&(p.items=await u({editor:e,query:ue.query})),ce&&(($=m?.onExit)==null||$.call(m,p)),X&&((E=m?.onUpdate)==null||E.call(m,p)),Y&&((F=m?.onStart)==null||F.call(m,p))},destroy:()=>{var y;p&&((y=m?.onExit)==null||y.call(m,p))}}},state:{init(){return{active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}},apply(y,b,S,C){let{isEditable:M}=e,{composing:L}=e.view,{selection:$}=y,{empty:E,from:F}=$,R={...b};if(R.composing=L,M&&(E||e.view.composing)){(F<b.range.from||F>b.range.to)&&!L&&!b.composing&&(R.active=!1);let J=h({char:t,allowSpaces:r,allowToIncludeChar:i,allowedPrefixes:o,startOfLine:s,$position:$.$from}),Se=`id_${Math.floor(Math.random()*4294967295)}`;J&&f({editor:e,state:C,range:J.range,isActive:b.active})?(R.active=!0,R.decorationId=b.decorationId?b.decorationId:Se,R.range=J.range,R.query=J.query,R.text=J.text):R.active=!1}else R.active=!1;return R.active||(R.decorationId=null,R.range={from:0,to:0},R.query=null,R.text=null),R}},props:{handleKeyDown(y,b){var S;let{active:C,range:M}=g.getState(y.state);return C&&((S=m?.onKeyDown)==null?void 0:S.call(m,{view:y,event:b,range:M}))||!1},decorations(y){let{active:b,range:S,decorationId:C}=g.getState(y);return b?W.create(y.doc,[ee.inline(S.from,S.to,{nodeName:l,class:a,"data-decoration-id":C})]):null}}});return g}var dd=Ry;var Py=function({editor:n,overrideSuggestionOptions:e,extensionName:t}){let r=new P;return{editor:n,char:"{{",pluginKey:r,command:({editor:i,range:o,props:s})=>{i.view.state.selection.$to.nodeAfter?.text?.startsWith(" ")&&(o.to+=1),i.chain().focus().insertContentAt(o,[{type:t,attrs:{...s}},{type:"text",text:" "}]).run(),i.view.dom.ownerDocument.defaultView?.getSelection()?.collapseToEnd()},allow:({state:i,range:o})=>{let s=i.doc.resolve(o.from),l=i.schema.nodes[t];return!!s.parent.type.contentMatch.matchType(l)},...e}},fd=B.create({name:"mergeTag",priority:101,addStorage(){return{suggestions:[],getSuggestionFromChar:()=>null}},addOptions(){return{HTMLAttributes:{},renderText({node:n}){return`{{ ${n.attrs.label??n.attrs.id} }}`},deleteTriggerWithBackspace:!1,renderHTML({options:n,node:e}){return["span",N(this.HTMLAttributes,n.HTMLAttributes),`${e.attrs.label??e.attrs.id}`]},suggestions:[],suggestion:{}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes(){return{id:{default:null,parseHTML:n=>n.getAttribute("data-id"),renderHTML:n=>n.id?{"data-id":n.id}:{}},label:{default:null,parseHTML:n=>n.getAttribute("data-label"),renderHTML:n=>n.label?{"data-label":n.label}:{}}}},parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:n,HTMLAttributes:e}){let t=this.editor?.extensionStorage?.[this.name]?.getSuggestionFromChar("{{"),r={...this.options};r.HTMLAttributes=N({"data-type":this.name},this.options.HTMLAttributes,e);let i=this.options.renderHTML({options:r,node:n,suggestion:t});return typeof i=="string"?["span",N({"data-type":this.name},this.options.HTMLAttributes,e),i]:i},renderText({node:n}){let e={options:this.options,node:n,suggestion:this.editor?.extensionStorage?.[this.name]?.getSuggestionFromChar("{{")};return this.options.renderText(e)},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:n,state:e})=>{let t=!1,{selection:r}=e,{empty:i,anchor:o}=r;if(!i)return!1;let s=new Q,l=0;return e.doc.nodesBetween(o-1,o,(a,c)=>{if(a.type.name===this.name)return t=!0,s=a,l=c,!1}),t&&n.insertText(this.options.deleteTriggerWithBackspace?"":"{{",l,l+s.nodeSize),t})}},addProseMirrorPlugins(){return[...this.storage.suggestions.map(dd),new O({props:{handleDrop(n,e){if(!e||(e.preventDefault(),!e.dataTransfer.getData("mergeTag")))return!1;let t=e.dataTransfer.getData("mergeTag");return n.dispatch(n.state.tr.insert(n.posAtCoords({left:e.clientX,top:e.clientY}).pos,n.state.schema.nodes.mergeTag.create({id:t}))),!1}}})]},onBeforeCreate(){this.storage.suggestions=(this.options.suggestions.length?this.options.suggestions:[this.options.suggestion]).map(n=>Py({editor:this.editor,overrideSuggestionOptions:n,extensionName:this.name})),this.storage.getSuggestionFromChar=n=>{let e=this.storage.suggestions.find(t=>t.char===n);return e||(this.storage.suggestions.length?this.storage.suggestions[0]:null)}}});var Iy=B.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),hd=Iy;var pd=Cs;var md=U.create({name:"small",parseHTML(){return[{tag:"small"}]},renderHTML({HTMLAttributes:n}){return["small",n,0]},addCommands(){return{setSmall:()=>({commands:n})=>n.setMark(this.name),toggleSmall:()=>({commands:n})=>n.toggleMark(this.name),unsetSmall:()=>({commands:n})=>n.unsetMark(this.name)}}});var Ly=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,By=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,Fy=U.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[Ee({find:Ly,type:this.type})]},addPasteRules(){return[ye({find:By,type:this.type})]}}),gd=Fy;var zy=U.create({name:"subscript",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"sub"},{style:"vertical-align",getAttrs(n){return n!=="sub"?!1:null}}]},renderHTML({HTMLAttributes:n}){return["sub",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setSubscript:()=>({commands:n})=>n.setMark(this.name),toggleSubscript:()=>({commands:n})=>n.toggleMark(this.name),unsetSubscript:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-,":()=>this.editor.commands.toggleSubscript()}}}),yd=zy;var Hy=U.create({name:"superscript",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"sup"},{style:"vertical-align",getAttrs(n){return n!=="super"?!1:null}}]},renderHTML({HTMLAttributes:n}){return["sup",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setSuperscript:()=>({commands:n})=>n.setMark(this.name),toggleSuperscript:()=>({commands:n})=>n.toggleMark(this.name),unsetSuperscript:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-.":()=>this.editor.commands.toggleSuperscript()}}}),bd=Hy;var $y=B.create({name:"text",group:"inline"}),xd=$y;var Vy=z.create({name:"textAlign",addOptions(){return{types:[],alignments:["left","center","right","justify"],defaultAlignment:null}},addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:n=>{let e=n.style.textAlign;return this.options.alignments.includes(e)?e:this.options.defaultAlignment},renderHTML:n=>n.textAlign?{style:`text-align: ${n.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:n=>({commands:e})=>this.options.alignments.includes(n)?this.options.types.map(t=>e.updateAttributes(t,{textAlign:n})).every(t=>t):!1,unsetTextAlign:()=>({commands:n})=>this.options.types.map(e=>n.resetAttributes(e,"textAlign")).every(e=>e),toggleTextAlign:n=>({editor:e,commands:t})=>this.options.alignments.includes(n)?e.isActive({textAlign:n})?t.unsetTextAlign():t.setTextAlign(n):!1}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}}),kd=Vy;var jy=U.create({name:"underline",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("underline")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["u",N(this.options.HTMLAttributes,n),0]},addCommands(){return{setUnderline:()=>({commands:n})=>n.setMark(this.name),toggleUnderline:()=>({commands:n})=>n.toggleMark(this.name),unsetUnderline:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),Sd=jy;var Jn=Math.min,gt=Math.max,qn=Math.round;var Pe=n=>({x:n,y:n}),Wy={left:"right",right:"left",bottom:"top",top:"bottom"},Ky={start:"end",end:"start"};function Zs(n,e,t){return gt(n,Jn(e,t))}function Ri(n,e){return typeof n=="function"?n(e):n}function zt(n){return n.split("-")[0]}function Pi(n){return n.split("-")[1]}function el(n){return n==="x"?"y":"x"}function tl(n){return n==="y"?"height":"width"}function yt(n){return["top","bottom"].includes(zt(n))?"y":"x"}function nl(n){return el(yt(n))}function wd(n,e,t){t===void 0&&(t=!1);let r=Pi(n),i=nl(n),o=tl(i),s=i==="x"?r===(t?"end":"start")?"right":"left":r==="start"?"bottom":"top";return e.reference[o]>e.floating[o]&&(s=Un(s)),[s,Un(s)]}function Md(n){let e=Un(n);return[Di(n),e,Di(e)]}function Di(n){return n.replace(/start|end/g,e=>Ky[e])}function _y(n,e,t){let r=["left","right"],i=["right","left"],o=["top","bottom"],s=["bottom","top"];switch(n){case"top":case"bottom":return t?e?i:r:e?r:i;case"left":case"right":return e?o:s;default:return[]}}function Cd(n,e,t,r){let i=Pi(n),o=_y(zt(n),t==="start",r);return i&&(o=o.map(s=>s+"-"+i),e&&(o=o.concat(o.map(Di)))),o}function Un(n){return n.replace(/left|right|bottom|top/g,e=>Wy[e])}function Uy(n){return{top:0,right:0,bottom:0,left:0,...n}}function vd(n){return typeof n!="number"?Uy(n):{top:n,right:n,bottom:n,left:n}}function Ht(n){let{x:e,y:t,width:r,height:i}=n;return{width:r,height:i,top:t,left:e,right:e+r,bottom:t+i,x:e,y:t}}function Td(n,e,t){let{reference:r,floating:i}=n,o=yt(e),s=nl(e),l=tl(s),a=zt(e),c=o==="y",u=r.x+r.width/2-i.width/2,d=r.y+r.height/2-i.height/2,f=r[l]/2-i[l]/2,h;switch(a){case"top":h={x:u,y:r.y-i.height};break;case"bottom":h={x:u,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-i.width,y:d};break;default:h={x:r.x,y:r.y}}switch(Pi(e)){case"start":h[s]-=f*(t&&c?-1:1);break;case"end":h[s]+=f*(t&&c?-1:1);break}return h}var Ed=async(n,e,t)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=t,l=o.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(e)),c=await s.getElementRects({reference:n,floating:e,strategy:i}),{x:u,y:d}=Td(c,r,a),f=r,h={},p=0;for(let m=0;m<l.length;m++){let{name:g,fn:y}=l[m],{x:b,y:S,data:C,reset:M}=await y({x:u,y:d,initialPlacement:r,placement:f,strategy:i,middlewareData:h,rects:c,platform:s,elements:{reference:n,floating:e}});u=b??u,d=S??d,h={...h,[g]:{...h[g],...C}},M&&p<=50&&(p++,typeof M=="object"&&(M.placement&&(f=M.placement),M.rects&&(c=M.rects===!0?await s.getElementRects({reference:n,floating:e,strategy:i}):M.rects),{x:u,y:d}=Td(c,f,a)),m=-1)}return{x:u,y:d,placement:f,strategy:i,middlewareData:h}};async function rl(n,e){var t;e===void 0&&(e={});let{x:r,y:i,platform:o,rects:s,elements:l,strategy:a}=n,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=Ri(e,n),p=vd(h),g=l[f?d==="floating"?"reference":"floating":d],y=Ht(await o.getClippingRect({element:(t=await(o.isElement==null?void 0:o.isElement(g)))==null||t?g:g.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:a})),b=d==="floating"?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,S=await(o.getOffsetParent==null?void 0:o.getOffsetParent(l.floating)),C=await(o.isElement==null?void 0:o.isElement(S))?await(o.getScale==null?void 0:o.getScale(S))||{x:1,y:1}:{x:1,y:1},M=Ht(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:b,offsetParent:S,strategy:a}):b);return{top:(y.top-M.top+p.top)/C.y,bottom:(M.bottom-y.bottom+p.bottom)/C.y,left:(y.left-M.left+p.left)/C.x,right:(M.right-y.right+p.right)/C.x}}var Ad=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(e){var t,r;let{placement:i,middlewareData:o,rects:s,initialPlacement:l,platform:a,elements:c}=e,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:m=!0,...g}=Ri(n,e);if((t=o.arrow)!=null&&t.alignmentOffset)return{};let y=zt(i),b=yt(l),S=zt(l)===l,C=await(a.isRTL==null?void 0:a.isRTL(c.floating)),M=f||(S||!m?[Un(l)]:Md(l)),L=p!=="none";!f&&L&&M.push(...Cd(l,m,p,C));let $=[l,...M],E=await rl(e,g),F=[],R=((r=o.flip)==null?void 0:r.overflows)||[];if(u&&F.push(E[y]),d){let me=wd(i,s,C);F.push(E[me[0]],E[me[1]])}if(R=[...R,{placement:i,overflows:F}],!F.every(me=>me<=0)){var J,Se;let me=(((J=o.flip)==null?void 0:J.index)||0)+1,we=$[me];if(we&&(!(d==="alignment"?b!==yt(we):!1)||R.every(ce=>ce.overflows[0]>0&&yt(ce.placement)===b)))return{data:{index:me,overflows:R},reset:{placement:we}};let Y=(Se=R.filter(X=>X.overflows[0]<=0).sort((X,ce)=>X.overflows[1]-ce.overflows[1])[0])==null?void 0:Se.placement;if(!Y)switch(h){case"bestFit":{var ot;let X=(ot=R.filter(ce=>{if(L){let ue=yt(ce.placement);return ue===b||ue==="y"}return!0}).map(ce=>[ce.placement,ce.overflows.filter(ue=>ue>0).reduce((ue,Qn)=>ue+Qn,0)]).sort((ce,ue)=>ce[1]-ue[1])[0])==null?void 0:ot[0];X&&(Y=X);break}case"initialPlacement":Y=l;break}if(i!==Y)return{reset:{placement:Y}}}return{}}}};var Od=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:o=!0,crossAxis:s=!1,limiter:l={fn:g=>{let{x:y,y:b}=g;return{x:y,y:b}}},...a}=Ri(n,e),c={x:t,y:r},u=await rl(e,a),d=yt(zt(i)),f=el(d),h=c[f],p=c[d];if(o){let g=f==="y"?"top":"left",y=f==="y"?"bottom":"right",b=h+u[g],S=h-u[y];h=Zs(b,h,S)}if(s){let g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",b=p+u[g],S=p-u[y];p=Zs(b,p,S)}let m=l.fn({...e,[f]:h,[d]:p});return{...m,data:{x:m.x-t,y:m.y-r,enabled:{[f]:o,[d]:s}}}}}};function Li(){return typeof window<"u"}function $t(n){return Dd(n)?(n.nodeName||"").toLowerCase():"#document"}function xe(n){var e;return(n==null||(e=n.ownerDocument)==null?void 0:e.defaultView)||window}function Ke(n){var e;return(e=(Dd(n)?n.ownerDocument:n.document)||window.document)==null?void 0:e.documentElement}function Dd(n){return Li()?n instanceof Node||n instanceof xe(n).Node:!1}function Ne(n){return Li()?n instanceof Element||n instanceof xe(n).Element:!1}function Ie(n){return Li()?n instanceof HTMLElement||n instanceof xe(n).HTMLElement:!1}function Nd(n){return!Li()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof xe(n).ShadowRoot}function an(n){let{overflow:e,overflowX:t,overflowY:r,display:i}=De(n);return/auto|scroll|overlay|hidden|clip/.test(e+r+t)&&!["inline","contents"].includes(i)}function Rd(n){return["table","td","th"].includes($t(n))}function Gn(n){return[":popover-open",":modal"].some(e=>{try{return n.matches(e)}catch{return!1}})}function Bi(n){let e=Fi(),t=Ne(n)?De(n):n;return["transform","translate","scale","rotate","perspective"].some(r=>t[r]?t[r]!=="none":!1)||(t.containerType?t.containerType!=="normal":!1)||!e&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!e&&(t.filter?t.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(t.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(t.contain||"").includes(r))}function Pd(n){let e=it(n);for(;Ie(e)&&!Vt(e);){if(Bi(e))return e;if(Gn(e))return null;e=it(e)}return null}function Fi(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Vt(n){return["html","body","#document"].includes($t(n))}function De(n){return xe(n).getComputedStyle(n)}function Yn(n){return Ne(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function it(n){if($t(n)==="html")return n;let e=n.assignedSlot||n.parentNode||Nd(n)&&n.host||Ke(n);return Nd(e)?e.host:e}function Id(n){let e=it(n);return Vt(e)?n.ownerDocument?n.ownerDocument.body:n.body:Ie(e)&&an(e)?e:Id(e)}function Ii(n,e,t){var r;e===void 0&&(e=[]),t===void 0&&(t=!0);let i=Id(n),o=i===((r=n.ownerDocument)==null?void 0:r.body),s=xe(i);if(o){let l=zi(s);return e.concat(s,s.visualViewport||[],an(i)?i:[],l&&t?Ii(l):[])}return e.concat(i,Ii(i,[],t))}function zi(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function Fd(n){let e=De(n),t=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=Ie(n),o=i?n.offsetWidth:t,s=i?n.offsetHeight:r,l=qn(t)!==o||qn(r)!==s;return l&&(t=o,r=s),{width:t,height:r,$:l}}function zd(n){return Ne(n)?n:n.contextElement}function cn(n){let e=zd(n);if(!Ie(e))return Pe(1);let t=e.getBoundingClientRect(),{width:r,height:i,$:o}=Fd(e),s=(o?qn(t.width):t.width)/r,l=(o?qn(t.height):t.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}var Jy=Pe(0);function Hd(n){let e=xe(n);return!Fi()||!e.visualViewport?Jy:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function qy(n,e,t){return e===void 0&&(e=!1),!t||e&&t!==xe(n)?!1:e}function Xn(n,e,t,r){e===void 0&&(e=!1),t===void 0&&(t=!1);let i=n.getBoundingClientRect(),o=zd(n),s=Pe(1);e&&(r?Ne(r)&&(s=cn(r)):s=cn(n));let l=qy(o,t,r)?Hd(o):Pe(0),a=(i.left+l.x)/s.x,c=(i.top+l.y)/s.y,u=i.width/s.x,d=i.height/s.y;if(o){let f=xe(o),h=r&&Ne(r)?xe(r):r,p=f,m=zi(p);for(;m&&r&&h!==p;){let g=cn(m),y=m.getBoundingClientRect(),b=De(m),S=y.left+(m.clientLeft+parseFloat(b.paddingLeft))*g.x,C=y.top+(m.clientTop+parseFloat(b.paddingTop))*g.y;a*=g.x,c*=g.y,u*=g.x,d*=g.y,a+=S,c+=C,p=xe(m),m=zi(p)}}return Ht({width:u,height:d,x:a,y:c})}function ol(n,e){let t=Yn(n).scrollLeft;return e?e.left+t:Xn(Ke(n)).left+t}function $d(n,e,t){t===void 0&&(t=!1);let r=n.getBoundingClientRect(),i=r.left+e.scrollLeft-(t?0:ol(n,r)),o=r.top+e.scrollTop;return{x:i,y:o}}function Gy(n){let{elements:e,rect:t,offsetParent:r,strategy:i}=n,o=i==="fixed",s=Ke(r),l=e?Gn(e.floating):!1;if(r===s||l&&o)return t;let a={scrollLeft:0,scrollTop:0},c=Pe(1),u=Pe(0),d=Ie(r);if((d||!d&&!o)&&(($t(r)!=="body"||an(s))&&(a=Yn(r)),Ie(r))){let h=Xn(r);c=cn(r),u.x=h.x+r.clientLeft,u.y=h.y+r.clientTop}let f=s&&!d&&!o?$d(s,a,!0):Pe(0);return{width:t.width*c.x,height:t.height*c.y,x:t.x*c.x-a.scrollLeft*c.x+u.x+f.x,y:t.y*c.y-a.scrollTop*c.y+u.y+f.y}}function Yy(n){return Array.from(n.getClientRects())}function Xy(n){let e=Ke(n),t=Yn(n),r=n.ownerDocument.body,i=gt(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=gt(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),s=-t.scrollLeft+ol(n),l=-t.scrollTop;return De(r).direction==="rtl"&&(s+=gt(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:s,y:l}}function Qy(n,e){let t=xe(n),r=Ke(n),i=t.visualViewport,o=r.clientWidth,s=r.clientHeight,l=0,a=0;if(i){o=i.width,s=i.height;let c=Fi();(!c||c&&e==="fixed")&&(l=i.offsetLeft,a=i.offsetTop)}return{width:o,height:s,x:l,y:a}}function Zy(n,e){let t=Xn(n,!0,e==="fixed"),r=t.top+n.clientTop,i=t.left+n.clientLeft,o=Ie(n)?cn(n):Pe(1),s=n.clientWidth*o.x,l=n.clientHeight*o.y,a=i*o.x,c=r*o.y;return{width:s,height:l,x:a,y:c}}function Ld(n,e,t){let r;if(e==="viewport")r=Qy(n,t);else if(e==="document")r=Xy(Ke(n));else if(Ne(e))r=Zy(e,t);else{let i=Hd(n);r={x:e.x-i.x,y:e.y-i.y,width:e.width,height:e.height}}return Ht(r)}function Vd(n,e){let t=it(n);return t===e||!Ne(t)||Vt(t)?!1:De(t).position==="fixed"||Vd(t,e)}function e0(n,e){let t=e.get(n);if(t)return t;let r=Ii(n,[],!1).filter(l=>Ne(l)&&$t(l)!=="body"),i=null,o=De(n).position==="fixed",s=o?it(n):n;for(;Ne(s)&&!Vt(s);){let l=De(s),a=Bi(s);!a&&l.position==="fixed"&&(i=null),(o?!a&&!i:!a&&l.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||an(s)&&!a&&Vd(n,s))?r=r.filter(u=>u!==s):i=l,s=it(s)}return e.set(n,r),r}function t0(n){let{element:e,boundary:t,rootBoundary:r,strategy:i}=n,s=[...t==="clippingAncestors"?Gn(e)?[]:e0(e,this._c):[].concat(t),r],l=s[0],a=s.reduce((c,u)=>{let d=Ld(e,u,i);return c.top=gt(d.top,c.top),c.right=Jn(d.right,c.right),c.bottom=Jn(d.bottom,c.bottom),c.left=gt(d.left,c.left),c},Ld(e,l,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function n0(n){let{width:e,height:t}=Fd(n);return{width:e,height:t}}function r0(n,e,t){let r=Ie(e),i=Ke(e),o=t==="fixed",s=Xn(n,!0,o,e),l={scrollLeft:0,scrollTop:0},a=Pe(0);function c(){a.x=ol(i)}if(r||!r&&!o)if(($t(e)!=="body"||an(i))&&(l=Yn(e)),r){let h=Xn(e,!0,o,e);a.x=h.x+e.clientLeft,a.y=h.y+e.clientTop}else i&&c();o&&!r&&i&&c();let u=i&&!r&&!o?$d(i,l):Pe(0),d=s.left+l.scrollLeft-a.x-u.x,f=s.top+l.scrollTop-a.y-u.y;return{x:d,y:f,width:s.width,height:s.height}}function il(n){return De(n).position==="static"}function Bd(n,e){if(!Ie(n)||De(n).position==="fixed")return null;if(e)return e(n);let t=n.offsetParent;return Ke(n)===t&&(t=t.ownerDocument.body),t}function jd(n,e){let t=xe(n);if(Gn(n))return t;if(!Ie(n)){let i=it(n);for(;i&&!Vt(i);){if(Ne(i)&&!il(i))return i;i=it(i)}return t}let r=Bd(n,e);for(;r&&Rd(r)&&il(r);)r=Bd(r,e);return r&&Vt(r)&&il(r)&&!Bi(r)?t:r||Pd(n)||t}var i0=async function(n){let e=this.getOffsetParent||jd,t=this.getDimensions,r=await t(n.floating);return{reference:r0(n.reference,await e(n.floating),n.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function o0(n){return De(n).direction==="rtl"}var s0={convertOffsetParentRelativeRectToViewportRelativeRect:Gy,getDocumentElement:Ke,getClippingRect:t0,getOffsetParent:jd,getElementRects:i0,getClientRects:Yy,getDimensions:n0,getScale:cn,isElement:Ne,isRTL:o0};var Wd=Od,Kd=Ad;var _d=(n,e,t)=>{let r=new Map,i={platform:s0,...t},o={...i.platform,_c:r};return Ed(n,e,{...i,platform:o})};var Ud=(n,e)=>{_d({getBoundingClientRect:()=>{let{from:r,to:i}=n.state.selection,o=n.view.coordsAtPos(r),s=n.view.coordsAtPos(i);return{top:Math.min(o.top,s.top),bottom:Math.max(o.bottom,s.bottom),left:Math.min(o.left,s.left),right:Math.max(o.right,s.right),width:Math.abs(s.right-o.left),height:Math.abs(s.bottom-o.top),x:Math.min(o.left,s.left),y:Math.min(o.top,s.top)}}},e,{placement:"bottom-start",strategy:"absolute",middleware:[Wd(),Kd()]}).then(({x:r,y:i,strategy:o})=>{e.style.width="max-content",e.style.position=o,e.style.left=`${r}px`,e.style.top=`${i}px`})},Jd=({mergeTags:n,noMergeTagSearchResultsMessage:e})=>({items:({query:t})=>n.filter(r=>r.toLowerCase().replace(/\s/g,"").includes(t.toLowerCase())),render:()=>{let t,r=0,i=null,o=()=>{let d=document.createElement("div");return d.className="fi-dropdown-panel fi-dropdown-list",d},s=()=>{if(!t||!i)return;let d=i.items||[];if(t.innerHTML="",d.length)d.forEach((f,h)=>{let p=document.createElement("button");p.className=`fi-dropdown-list-item fi-dropdown-list-item-label ${h===r?"fi-selected":""}`,p.textContent=f,p.type="button",p.addEventListener("click",()=>l(h)),t.appendChild(p)});else{let f=document.createElement("div");f.className="fi-dropdown-header",f.textContent=e,t.appendChild(f)}},l=d=>{if(!i)return;let h=(i.items||[])[d];h&&i.command({id:h})},a=()=>{if(!i)return;let d=i.items||[];d.length!==0&&(r=(r+d.length-1)%d.length,s())},c=()=>{if(!i)return;let d=i.items||[];d.length!==0&&(r=(r+1)%d.length,s())},u=()=>{l(r)};return{onStart:d=>{i=d,r=0,t=o(),t.style.position="absolute",s(),document.body.appendChild(t),d.clientRect&&Ud(d.editor,t)},onUpdate:d=>{i=d,r=0,s(),d.clientRect&&Ud(d.editor,t)},onKeyDown:d=>d.event.key==="Escape"?(t&&t.parentNode&&t.parentNode.removeChild(t),!0):d.event.key==="ArrowUp"?(a(),!0):d.event.key==="ArrowDown"?(c(),!0):d.event.key==="Enter"?(u(),!0):!1,onExit:()=>{t&&t.parentNode&&t.parentNode.removeChild(t)}}}});var qd=async({customExtensionUrls:n,deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:t,editCustomBlockUsing:r,insertCustomBlockUsing:i,key:o,mergeTags:s,noMergeTagSearchResultsMessage:l,placeholder:a,statePath:c,uploadingFileMessage:u,$wire:d})=>[Cu,vu,Gs,Tu,Eu,Au.configure({deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:t,editCustomBlockUsing:r,insertCustomBlockUsing:i}),Ou,Su,wu,Nu,Du,Ru,Pu,Iu,Bu,Fu,Zu.configure({autolink:!0,openOnClick:!1}),Ys,ud.configure({get$WireUsing:()=>d,key:o,statePath:c,uploadingMessage:u}),...s.length?[fd.configure({deleteTriggerWithBackspace:!0,suggestion:Jd({mergeTags:s,noMergeTagSearchResultsMessage:l})})]:[],Qs,hd,pd.configure({placeholder:a}),md,gd,yd,bd,xd,kd.configure({types:["heading","paragraph"],alignments:["start","center","end","justify"],defaultAlignment:"start"}),Sd,Mu,...(await Promise.all(n.map(async f=>(new RegExp("^(?:[a-z+]+:)?//","i").test(f)||(f=new URL(f,document.baseURI).href),(await import(f)).default)))).flat()];function l0({activePanel:n,deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:t,extensions:r,key:i,isDisabled:o,isLiveDebounced:s,isLiveOnBlur:l,liveDebounce:a,livewireId:c,mergeTags:u,noMergeTagSearchResultsMessage:d,placeholder:f,state:h,statePath:p,uploadingFileMessage:m}){let g;return{state:h,activePanel:n,editorSelection:{type:"text",anchor:1,head:1},isUploadingFile:!1,shouldUpdateState:!0,editorUpdatedAt:Date.now(),async init(){g=new uu({editable:!o,element:this.$refs.editor,extensions:await qd({customExtensionUrls:r,deleteCustomBlockButtonIconHtml:e,editCustomBlockButtonIconHtml:t,editCustomBlockUsing:(y,b)=>this.$wire.mountAction("customBlock",{editorSelection:this.editorSelection,id:y,config:b,mode:"edit"},{schemaComponent:i}),insertCustomBlockUsing:(y,b=null)=>this.$wire.mountAction("customBlock",{id:y,dragPosition:b,mode:"insert"},{schemaComponent:i}),key:i,mergeTags:u,noMergeTagSearchResultsMessage:d,placeholder:f,statePath:p,uploadingFileMessage:m,$wire:this.$wire}),content:this.state}),g.on("create",()=>{this.editorUpdatedAt=Date.now()}),g.on("update",Alpine.debounce(({editor:y})=>{this.editorUpdatedAt=Date.now(),this.state=y.getJSON(),this.shouldUpdateState=!1,s&&this.$wire.commit()},a??300)),g.on("selectionUpdate",({transaction:y})=>{this.editorUpdatedAt=Date.now(),this.editorSelection=y.selection.toJSON()}),l&&g.on("blur",()=>this.$wire.commit()),this.$watch("state",()=>{if(!this.shouldUpdateState){this.shouldUpdateState=!0;return}g.commands.setContent(this.state)}),window.addEventListener("run-rich-editor-commands",y=>{y.detail.livewireId===c&&y.detail.key===i&&this.runEditorCommands(y.detail)}),window.addEventListener("rich-editor-uploading-file",y=>{y.detail.livewireId===c&&y.detail.key===i&&(this.isUploadingFile=!0,y.stopPropagation())}),window.addEventListener("rich-editor-uploaded-file",y=>{y.detail.livewireId===c&&y.detail.key===i&&(this.isUploadingFile=!1,y.stopPropagation())}),window.dispatchEvent(new CustomEvent(`schema-component-${c}-${i}-loaded`))},getEditor(){return g},$getEditor(){return this.getEditor()},setEditorSelection(y){y&&(this.editorSelection=y,g.chain().command(({tr:b})=>(b.setSelection(D.fromJSON(g.state.doc,this.editorSelection)),!0)).run())},runEditorCommands({commands:y,editorSelection:b}){this.setEditorSelection(b);let S=g.chain();y.forEach(C=>S=S[C.name](...C.arguments??[])),S.run()},togglePanel(y=null){if(this.isPanelActive(y)){this.activePanel=null;return}this.activePanel=y},isPanelActive(y=null){return y===null?this.activePanel!==null:this.activePanel===y},insertMergeTag(y){g.chain().focus().insertContent([{type:"mergeTag",attrs:{id:y}},{type:"text",text:" "}]).run()}}}export{l0 as default};
