<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {

        Schema::dropIfExists('running_balances');
        Schema::dropIfExists('transactions');
        Schema::dropIfExists('initial_balances');
        Schema::dropIfExists('transaction_categories');


        Schema::create('accounts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->string('name')->comment('Nama akun: Kas Desa, Bank, dll');
            $table->string('account_number')->unique()->nullable()->comment('Nomor akun');
            $table->string('type')->comment('Tipe: cash, bank, savings');
            $table->decimal('initial_balance', 15, 2)->default(0)->comment('Saldo awal');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('transaction_categories', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->string('name')->comment('Nama kategori: Pendapatan, Pengeluaran, dll');
            $table->string('type')->comment('Tipe: income, expense, transfer');
            $table->text('description')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('account_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('transaction_category_id')->nullable()->constrained()->nullOnDelete();
            $table->string('type')->comment('Tipe: income, expense, transfer');
            $table->decimal('amount', 15, 2)->comment('Jumlah transaksi');
            $table->dateTime('transaction_date')->comment('Tanggal transaksi');
            $table->text('description')->nullable();
            $table->foreignUuid('destination_account_id')->nullable()->constrained('accounts')->nullOnDelete()->comment('Akun tujuan untuk transfer');
            $table->string('reference_number')->unique()->nullable()->comment('Nomor referensi transaksi');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('balances', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('account_id')->nullable()->constrained()->nullOnDelete();
            $table->decimal('balance', 15, 2)->comment('Saldo akun pada waktu tertentu');
            $table->dateTime('balance_date')->comment('Tanggal pencatatan saldo');
            $table->foreignUuid('transaction_id')->nullable()->constrained()->nullOnDelete()->comment('Transaksi terkait');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('budgets', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('transaction_category_id')->nullable()->constrained()->nullOnDelete();
            $table->decimal('amount', 15, 2)->comment('Anggaran yang dialokasikan');
            $table->dateTime('start_date')->comment('Tanggal mulai anggaran');
            $table->dateTime('end_date')->comment('Tanggal akhir anggaran');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('audit_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('user_id')->nullable()->constrained()->nullOnDelete();
            $table->string('action')->comment('Aksi: create, update, delete');
            $table->string('model_type')->comment('Tipe model: App\Models\Transaction, dll');
            $table->uuid('model_id')->comment('ID model yang terkait');
            $table->json('changes')->nullable()->comment('Perubahan data dalam JSON');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
        Schema::dropIfExists('budgets');
        Schema::dropIfExists('balances');
        Schema::dropIfExists('transactions');
        Schema::dropIfExists('transaction_categories');
        Schema::dropIfExists('accounts');
    }
};
