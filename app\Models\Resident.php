<?php

namespace App\Models;

use App\Models\Concerns\TenantScoped;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Resident extends Model
{
    use SoftDele<PERSON>, HasUuids, TenantScoped;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $guarded = [];

    public function subscriber(): BelongsTo
    {
        return $this->belongsTo(Subscriber::class);
    }

    public function family(): BelongsTo
    {
        return $this->belongsTo(Family::class)->withDefault();
    }

}
