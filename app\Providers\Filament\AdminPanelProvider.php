<?php

namespace App\Providers\Filament;

use App\Filament\Pages\CashFlowReport;
use App\Filament\Resources\Families\FamilyResource;
use App\Filament\Resources\Residents\ResidentResource;
use App\Http\Middleware\SetTenantFromSubscriptionUser;
use App\Models\Subscriber;
use Filament\Actions\Action;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationBuilder;
use Filament\Navigation\NavigationGroup;
use Filament\Pages\Dashboard;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\Width;
use Filament\Widgets\AccountWidget;
use Filament\Widgets\FilamentInfoWidget;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use pxlrbt\FilamentEnvironmentIndicator\EnvironmentIndicatorPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
//            ->spa()
            ->path('admin')
            ->brandName('Ruang Warga')
            ->login()
            ->colors([
                'primary' => Color::Blue,
            ])
            ->navigationGroups([
                'Data Warga',
                'Arus Kas',
                'Region',
                'Subscriptions',
                'Users Management',
            ])
            //            ->topbar()
//                ->tenant(Subscriber::class)
            ->sidebarCollapsibleOnDesktop()
            ->maxContentWidth(Width::Full)
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\Filament\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\Filament\Pages')
            ->pages([
                Dashboard::class,
                CashFlowReport::class
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\Filament\Widgets')
            ->widgets([
                AccountWidget::class,
            ])
            ->middleware([
                SetTenantFromSubscriptionUser::class,
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                EnvironmentIndicatorPlugin::make()
                ->visible(fn()=>auth()->user()?->isSuperadmin()),
            ])
//            ->navigation(function (NavigationBuilder $builder): NavigationBuilder {
//                $items = [];
//
//                if (auth()->check()) {
//                    if (auth()->user()->isSuperAdmin()) {
//                        $items = [
//                            ...FamilyResource::getNavigationItems(),
//                        ];
//                    } else {
//                        $items = [
//                            ...ResidentResource::getNavigationItems(),
//                        ];
//                    }
//                }
//
//                return $builder->items($items);
//            })
            ->userMenuItems([
                'profile' => fn (Action $action) => $action->label('Edit profile'),
            ]);
    }
}
