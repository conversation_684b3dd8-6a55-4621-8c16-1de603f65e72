<?php

namespace App\Filament\Resources\Balances\RelationManagers;

use App\Enums\AccountTypeEnum;
use App\Enums\TransactionTypeEnum;
use Carbon\Carbon;
use Filament\Actions\AssociateAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\DissociateAction;
use Filament\Actions\DissociateBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use function PHPUnit\Framework\isNull;

class BalanceDetailsRelationManager extends RelationManager
{
    protected static string $relationship = 'balanceDetails';

    protected static ?string $title = 'Detail Saldo';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('account_id.name')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('account_id')
            ->columns([
//                TextColumn::make('account.name')
//                    ->searchable(),
                TextColumn::make('transaction.description')
                    ->placeholder('-')
                    ->limit(25)
                    ->searchable()
                    ->tooltip(fn($record) => $record->transaction?->id ?: 'No transaction'),
                TextColumn::make('amount')
                    ->label('Jumlah')
                    ->money('IDR'),
                TextColumn::make('type')
                    ->label('Tipe')
                    ->formatStateUsing(fn(?TransactionTypeEnum $state) => $state?->label() ?? '-')
                    ->icon(fn(?TransactionTypeEnum $state) => $state?->icon() ?? '-')
                    ->color(fn(?TransactionTypeEnum $state) => $state?->color() ?? '-')
                    ->iconColor(fn(?TransactionTypeEnum $state) => $state?->color() ?? '-'),
                TextColumn::make('balance_before')
                    ->label('Saldo Sebelum')
                    ->money('IDR'),
                TextColumn::make('balance_after')
                    ->label('Saldo Setelah')
                    ->money('IDR'),
                TextColumn::make('balance_date')
                    ->label('Tanggal')
                    ->formatStateUsing(function ($value) {
                        return Carbon::parse($value)->locale('id')->translatedFormat('D, d M Y');
                    }),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->headerActions([
//                CreateAction::make(),
//                AssociateAction::make(),
            ])
            ->recordActions([
                EditAction::make(),
                DissociateAction::make(),
                DeleteAction::make(),
                ForceDeleteAction::make(),
                RestoreAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DissociateBulkAction::make(),
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn(Builder $query) => $query
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]));
    }
}
