<?php

namespace App\Enums;

enum ReligionEnum: string
{
    case ISLAM = 'islam';
    case KRISTEN = 'kristen';
    case KATHOLIK = 'katholik';
    case HINDU = 'hindu';
    case BUDDHA = 'buddha';
    case KONGHUCU = 'konghucu';
    case OTHER = 'other';

    public function label(): string
    {
        return match ($this) {
            self::ISLAM => 'Islam',
            self::KRISTEN => '<PERSON>',
            self::KATHOLIK => 'Kristen <PERSON>holik',
            self::HINDU => 'Hindu',
            self::BUDDHA => 'Buddha',
            self::KONGHUCU => '<PERSON><PERSON><PERSON>',
            self::OTHER => 'Lainnya',
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::ISLAM => 'heroicon-o-moon',
            self::KRISTEN => 'heroicon-o-cross',
            self::KATHOLIK => 'heroicon-o-church',
            self::HINDU => 'heroicon-o-fire',
            self::BUDDHA => 'heroicon-o-light-bulb',
            self::KONGHUCU => 'heroicon-o-book-open',
            self::OTHER => 'heroicon-o-question-mark-circle',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::ISLAM => 'green',
            self::KRISTEN => 'blue',
            self::KATHOLIK => 'indigo',
            self::HINDU => 'orange',
            self::BUDDHA => 'yellow',
            self::KONGHUCU => 'red',
            self::OTHER => 'gray',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }
        return $options;
    }

    public static function select(): array
    {
        return [
            self::ISLAM->value => self::ISLAM->label(),
            self::KRISTEN->value => self::KRISTEN->label(),
            self::KATHOLIK->value => self::KATHOLIK->label(),
            self::HINDU->value => self::HINDU->label(),
            self::BUDDHA->value => self::BUDDHA->label(),
            self::KONGHUCU->value => self::KONGHUCU->label(),
            self::OTHER->value => self::OTHER->label(),
        ];
    }
}
