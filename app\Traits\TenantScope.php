<?php

namespace App\Traits;

trait TenantScope
{
    protected static function bootTenantScope(): void
    {
        if (auth()->check() && auth()->user()->isSuperAdmin()) {
            return;
        }

        if (auth()->check() && !app()->runningInConsole()) {
            static::addGlobalScope('subscriber', function ($builder) {
                $builder->where('subscriber_id', auth()->user()->subscriber_id);
            });
        }
    }
}
