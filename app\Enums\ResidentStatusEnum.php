<?php

namespace App\Enums;

enum ResidentStatusEnum: string
{
    case WNI = 'wni';
    case WNA = 'wna';
    case DUAL_CITIZEN = 'dual_citizen';
    case STATELESS = 'stateless';
    case PENDATANG = 'pendatang';
    case PENDUDUK_TETAP = 'penduduk_tetap';
    case PENDUDUK_SEMENTARA = 'penduduk_sementara';

    public function label(): string
    {
        return match ($this) {
            self::WNI => 'Warga Negara Indonesia',
            self::WNA => 'Warga Negara Asing',
            self::DUAL_CITIZEN => 'Kewarganegaraan Ganda',
            self::STATELESS => 'Tanpa Kewarganegaraan',
            self::PENDATANG => 'Pendatang',
            self::PENDUDUK_TETAP => 'Penduduk Tetap',
            self::PENDUDUK_SEMENTARA => 'Penduduk Sementara',
        };
    }

    public function legalStatus(): string
    {
        return match ($this) {
            self::WNI, self::DUAL_CITIZEN => 'Diakui <PERSON>',
            self::WNA => 'Tunduk Pada Peraturan Keimigrasian',
            self::STATELESS => 'Status Hukum Terbatas',
            default => 'Status Administratif'
        };
    }

    public function documentation(): string
    {
        return match ($this) {
            self::WNI => 'KTP Indonesia',
            self::WNA => 'KITAS/KITAP',
            self::DUAL_CITIZEN => 'Dokumen Kewarganegaraan Ganda',
            self::STATELESS => 'Dokumen Khusus',
            self::PENDUDUK_TETAP => 'KK/KTP',
            self::PENDUDUK_SEMENTARA => 'KTP Sementara',
            self::PENDATANG => 'Surat Keterangan Pendatang'
        };
    }

    public function icon(): string
    {
        return match ($this) {
            self::WNI => 'heroicon-o-flag',
            self::WNA => 'heroicon-o-globe-alt',
            self::DUAL_CITIZEN => 'heroicon-o-arrows-right-left',
            self::STATELESS => 'heroicon-o-question-mark-circle',
            self::PENDUDUK_TETAP => 'heroicon-o-home',
            self::PENDUDUK_SEMENTARA => 'heroicon-o-clock',
            self::PENDATANG => 'heroicon-o-arrow-right'
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::WNI => 'red',
            self::WNA => 'blue',
            self::DUAL_CITIZEN => 'purple',
            self::STATELESS => 'gray',
            self::PENDUDUK_TETAP => 'green',
            self::PENDUDUK_SEMENTARA => 'yellow',
            self::PENDATANG => 'orange'
        };
    }

    public static function citizenshipOptions(): array
    {
        return [
            self::WNI->value => self::WNI->label(),
            self::WNA->value => self::WNA->label(),
            self::DUAL_CITIZEN->value => self::DUAL_CITIZEN->label(),
            self::STATELESS->value => self::STATELESS->label()
        ];
    }

    public static function residenceOptions(): array
    {
        return [
            self::PENDUDUK_TETAP->value => self::PENDUDUK_TETAP->label(),
            self::PENDUDUK_SEMENTARA->value => self::PENDUDUK_SEMENTARA->label(),
            self::PENDATANG->value => self::PENDATANG->label()
        ];
    }

    public static function options(): array
    {
        return array_merge(self::citizenshipOptions(), self::residenceOptions());
    }
}
