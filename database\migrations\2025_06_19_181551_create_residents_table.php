<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('residents', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('family_id')->nullable()->constrained()->nullOnDelete();
            $table->string('nik')->unique()->index();
            $table->string('fullname')->nullable()->index();
            $table->string('gender')->nullable();
            $table->string('birthplace')->nullable();
            $table->date('birthdate')->nullable();
            $table->string('religion')->nullable();
            $table->string('marital_status')->nullable();
            $table->string('education')->nullable();
            $table->string('job')->nullable();
            $table->string('blood_type')->nullable();
            $table->string('resident_status')->nullable();
            $table->date('moving_date')->nullable();
            $table->date('death_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('residents');
    }
};
