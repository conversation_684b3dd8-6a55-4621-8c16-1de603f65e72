<?php

namespace App\Filament\Resources\Residents\Tables;

use App\Enums\BloodTypeEnum;
use App\Enums\EducationLevelEnum;
use App\Enums\FamilyRelationshipEnum;
use App\Enums\GenderEnum;
use App\Enums\MaritalStatusEnum;
use App\Enums\OccupationEnum;
use App\Enums\ReligionEnum;
use App\Enums\ResidentStatusEnum;
use App\Models\Resident;
use Carbon\Carbon;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Notifications\Notification;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class ResidentsTable
{

    public static function formatFamilyId($number): string
    {
        $str = (string)$number;
        $formatted = '';
        for ($i = 0; $i < strlen($str); $i += 4) {
            if ($i > 0) {
                $formatted .= '-';
            }
            $formatted .= substr($str, $i, 4);
        }
        return $formatted;
    }

    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('family.family_identifier')
                    ->label('No KK')
                    ->formatStateUsing(function ($state) {
                        return static::formatFamilyId($state);
                    })
                    ->searchable(),
                TextColumn::make('nik')
                    ->label('NIK')
                    ->formatStateUsing(function ($state) {
                        return static::formatFamilyId($state);
                    })
                    ->searchable(),
                TextColumn::make('fullname')
                    ->label('Nama Lengkap')
                    ->searchable(),
                TextColumn::make('age')
                    ->label('Usia')
                    ->getStateUsing(function ($record) {
                        if (!$record->birthdate) {
                            return '-';
                        }

                        $birthdate = Carbon::parse($record->birthdate);
                        $now = Carbon::now();

                        $years = (int)abs($now->diffInYears($birthdate));
                        $months = (int)abs($now->diffInMonths($birthdate) % 12);

                        return "{$years} tahun, {$months} bulan";
                    })
                    ->sortable(query: function ($query, $direction) {
                        return $query->orderBy('birthdate', $direction == 'asc' ? 'desc' : 'asc');
                    }),
                TextColumn::make('family_status')
                    ->label('Status Keluarga')
                    ->formatStateUsing(fn(?string $state): string => $state ? FamilyRelationshipEnum::from($state)->label() : '-')
                    ->searchable(),

                CheckboxColumn::make('is_head_of_family')
                    ->label('Kepala'),
                TextColumn::make('gender')
                    ->label('Jenis Kelamin')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(?string $state): string => $state ? GenderEnum::from($state)->label() : '-')
                    ->searchable(),
                TextColumn::make('birthplace')
                    ->label('Tempat Lahir')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('birthdate')
                    ->label('Tanggal Lahir')
                    ->date()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                TextColumn::make('religion')
                    ->label('Agama')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(?string $state): string => $state ? ReligionEnum::from($state)->label() : '-')
                    ->searchable(),
                TextColumn::make('marital_status')
                    ->label('Status Pernikahan')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(?string $state): string => $state ? MaritalStatusEnum::from($state)->label() : '-')
                    ->searchable(),
                TextColumn::make('education')
                    ->label('Pendidikan Terakhir')
                    ->formatStateUsing(fn(?string $state): string => $state ? EducationLevelEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('job')
                    ->label('Pekerjaan')
                    ->formatStateUsing(fn(?string $state): string => $state ? OccupationEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('blood_type')
                    ->label('Jenis Kelamin')
                    ->formatStateUsing(fn(?string $state): string => $state ? BloodTypeEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('resident_status')
                    ->label('Status Pernikahan')
                    ->formatStateUsing(fn(?string $state): string => $state ? ResidentStatusEnum::from($state)->label() : '-')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->recordActions([
                EditAction::make()
                    ->label(false)
                    ->tooltip('Edit data')
                    ->icon(Heroicon::PencilSquare),
                RestoreAction::make()
                    ->label(false)
                    ->tooltip('Pulihkan data')
                    ->icon(Heroicon::ArrowPath),
                DeleteAction::make()
                    ->label(false)
                    ->tooltip('Hapus data')
                    ->icon(Heroicon::Trash),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus semua'),
                    ForceDeleteBulkAction::make()
                        ->label('Hapus permanent'),
                    RestoreBulkAction::make()
                        ->label('Pulihkan semua'),
                ]),
            ]);
    }
}
