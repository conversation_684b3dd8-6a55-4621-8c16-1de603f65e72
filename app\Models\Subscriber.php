<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Subscriber extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $guarded = [];


    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'subscriber_users')
            ->using(SubscriberUser::class)
            ->withPivot('role')
            ->withTimestamps()
            ->whereNotNull('users.id')
            ->whereNull('users.deleted_at');
    }

    public function village(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Village::class);
    }

    public function getDistrictAttribute()
    {
        return $this->village->district ?? null;
    }

    public function getRegencyAttribute()
    {
        return $this->village->district->regency ?? null;
    }

    public function getProvinceAttribute()
    {
        return $this->village->district->regency->province ?? null;
    }

    public function getAddressHierarchyAttribute(): string
    {
        return implode(', ', array_filter([
            $this->village->name ?? null,
            $this->village->district->name ?? null,
            $this->village->district->regency->name ?? null,
            $this->village->district->regency->province->name ?? null
        ]));
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class);
    }

    public function transactionCategories(): HasMany
    {
        return $this->hasMany(TransactionCategory::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function balances(): HasMany
    {
        return $this->hasMany(Balance::class);
    }

    public function budgets(): HasMany
    {
        return $this->hasMany(Budget::class);
    }

    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

}
