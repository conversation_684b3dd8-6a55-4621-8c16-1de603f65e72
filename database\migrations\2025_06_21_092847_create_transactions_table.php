<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('subscriber_id')->nullable()->constrained('subscribers')->nullOnDelete();
            $table->foreignUuid('transaction_category_id')->nullable()->constrained('transaction_categories')->nullOnDelete();
            $table->dateTime('date')->nullable()->index();
            $table->decimal('amount', 15, 2)->nullable();
            $table->string('description', 255)->nullable();
            $table->string('type')->nullable();
            $table->foreignUuid('created_by')->nullable()->references('id')->on('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
