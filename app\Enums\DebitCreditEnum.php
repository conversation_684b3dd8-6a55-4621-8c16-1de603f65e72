<?php

namespace App\Enums;

enum DebitCreditEnum: string
{
    case DEBIT = 'debit';
    case CREDIT = 'credit';

    // Label untuk tampilan
    public function label(): string
    {
        return match($this) {
            self::DEBIT => 'Debit (Masuk)',
            self::CREDIT => 'Kredit (Keluar)',
        };
    }

    // Nama Heroicon (versi outline)
    public function icon(): string
    {
        return match($this) {
            self::DEBIT => 'heroicon-o-arrow-down-circle', // Panah bawah (arus masuk)
            self::CREDIT => 'heroicon-o-arrow-up-circle',   // Panah atas (arus keluar)
        };
    }

    // Warna ikon (Tailwind CSS)
    public function color(): string
    {
        return match($this) {
            self::DEBIT => 'success',  // Hijau untuk debit
            self::CREDIT => 'danger',   // Merah untuk kredit
        };
    }

    // Kode akuntansi standar
    public function accountingCode(): string
    {
        return match($this) {
            self::DEBIT => 'D',
            self::CREDIT => 'C',
        };
    }

    // Format untuk dropdown HTML
    public static function options(): array
    {
        return [
            self::DEBIT->value => self::DEBIT->label(),
            self::CREDIT->value => self::CREDIT->label(),
        ];
    }

    // Format untuk JavaScript/API
    public static function selectOptions(): array
    {
        return array_map(
            fn($case) => [
                'value' => $case->value,
                'label' => $case->label(),
                'icon' => $case->icon(),
                'color' => $case->color(),
                'code' => $case->accountingCode(),
            ],
            self::cases()
        );
    }
}
