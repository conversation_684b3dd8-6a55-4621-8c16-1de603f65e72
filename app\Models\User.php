<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */

    protected $keyType = 'string';
    public $incrementing = false;


    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }


    public function isSuperadmin(): bool
    {
        return $this->global_role === 'superadmin';
    }

    public function hasRole(string $role): bool
    {
        return $this->subscribers()->wherePivot('role', $role)->exists();
    }
    public function tenantIds() {
        if ($this->isSuperadmin()) {
            return Subscriber::pluck('id');
        }

        if (!$this->relationLoaded('subscribers')) {
            $this->load('subscribers');
        }

        return $this->subscribers->pluck('id');
    }

    public function subscribers()
    {
        return $this->belongsToMany(Subscriber::class, 'subscriber_users')
            ->using(SubscriberUser::class)
            ->withPivot('role')
            ->withTimestamps();
    }

    public function activeSubscription()
    {
        return $this->subscribers()->first();
    }

}
